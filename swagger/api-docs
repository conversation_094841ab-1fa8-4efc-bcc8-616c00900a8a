{"openapi": "3.0.1", "info": {"title": "API V1", "version": "v1"}, "paths": {"/v06700edc6a3b7f12/messages/health": {"get": {"summary": "Message from database", "tags": ["Message"], "responses": {"200": {"description": "Database is up"}, "404": {"description": "Entity not present"}, "503": {"description": "Database is down"}}}}, "/v1/messages/connected-accounts/{connected_account_id}/agents/{entity_type}": {"get": {"summary": "Lists Connected Account Agents", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "entity_type", "in": "path", "enum": ["lead", "contact"], "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Lists connected account agents"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Invalid entity type"}}}}, "/v1/messages/connected-accounts/{connected_account_id}/agents/{entity_type}/save": {"post": {"summary": "Save Connected Account Agents", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "entity_type", "in": "path", "enum": ["lead", "contact"], "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Save connected account agents"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Invalid entity type or user"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["agents"], "properties": {"agents": {"type": "object", "schema": {"id": {"type": "integer"}, "name": {"type": "string"}}}}}}}}}}, "/v1/messages/{message_id}/attachments/{id}": {"get": {"summary": "Downloads attachment", "tags": ["Message App"], "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Attachment found"}, "404": {"description": "Attachment not found"}}}}, "/v1/messages/connected-accounts/{connected_account_id}/chatbot-media": {"post": {"summary": "Upload Chatbot Media", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Create Chatbot Media"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account not found"}}, "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "string"}}}, "required": true}}, "get": {"summary": "Get Chatbot Media", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "chatbotId", "in": "query", "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Chatbot Media Details"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account not found"}}}}, "/v1/messages/connected-accounts": {"get": {"summary": "Lists Connected Accounts", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return Connected Accounts"}, "401": {"description": "Authentication failed"}}}, "post": {"summary": "Create Connected Account", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Creates Connected Account"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["phoneNumberId", "wabaId", "authCode"], "properties": {"phoneNumberId": {"type": "number"}, "wabaId": {"type": "number"}, "authCode": {"type": "number"}}}}}}, "x-access-policy": {"action": "create", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/connected-accounts/{id}": {"get": {"summary": "Get Connected Account Details", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Connected Account Details"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}}}, "put": {"summary": "Update Connected Account", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Updates Connected Account"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["displayName"], "properties": {"displayName": {"type": "string"}, "entitiesToCreate": {"type": "array"}}}}}}}}, "/v1/messages/connected-accounts/{id}/activate": {"post": {"summary": "Activates Connected Account", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Connected account activated"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}, "422": {"description": "Failed to activate account"}}, "x-access-policy": {"action": "create", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/connected-accounts/{id}/request-code": {"post": {"summary": "Requests Code for Connected Account", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Verification code requested successfully"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}, "422": {"description": "Invalid data"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["codeMethod"], "properties": {"codeMethod": {"type": "string", "enum": ["SMS", "VOICE"]}, "language": {"type": "string"}}}}}}, "x-access-policy": {"action": "activate", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/connected-accounts/{id}/verify-code": {"post": {"summary": "Verify Code for Connected Account", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Phone Number verified successfully"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}, "422": {"description": "Invalid code"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["otpCode"], "properties": {"otpCode": {"type": "integer"}}}}}}, "x-access-policy": {"action": "activate", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/connected-accounts/lookup": {"get": {"summary": "Connected Accounts Lookup", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "view", "in": "query", "schema": {"type": "string"}}, {"name": "entityType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Connected Accounts Lookup"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/connected-accounts/{id}/{entity_type}/{entity_id}/phone-numbers": {"get": {"summary": "Entity phone numbers with session details", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Phone numbers with session details"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account Not Found"}}}}, "/v1/messages/conversations/{id}/search": {"post": {"summary": "returns messages in the conversation", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns messages in the given conversation with entity permissions"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/messages/conversations/search": {"post": {"summary": "returns conversations", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns conversations for given user"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/messages/conversations/{id}/permissions": {"get": {"summary": "returns entity permissions for a conversation", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Conversation ID", "schema": {"type": "integer"}}, {"name": "entityType", "in": "query", "required": true, "description": "Entity type (e.g., lead, contact)", "schema": {"type": "string"}}, {"name": "entityId", "in": "query", "required": true, "description": "Entity ID", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns entity permissions for the conversation", "content": {"application/json": {"schema": {"type": "object", "properties": {"isEntityAccessible": {"type": "boolean"}, "isPhoneNumberPresent": {"type": "boolean"}}, "required": ["isEntityAccessible", "isPhoneNumberPresent"]}}}}, "401": {"description": "Authentication failed"}, "422": {"description": "Conversation not found"}}}}, "/v1/messages/conversations/{id}": {"delete": {"summary": "deletes a conversation", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Conversation ID", "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Conversation soft deleted successfully"}, "401": {"description": "Authentication failed"}, "403": {"description": "Permission denied"}, "422": {"description": "Conversation not found"}}}}, "/v1/messages/conversations": {"post": {"summary": "create or fetch a conversation", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the created or fetched conversation ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"]}}}}, "401": {"description": "Authentication failed"}, "403": {"description": "Permission denied"}, "404": {"description": "Connected account not found or inactive"}, "422": {"description": "Conversation not found"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"lastConversationId": {"type": "integer", "description": "ID of the last conversation (required)"}, "connectedAccountId": {"type": "integer", "description": "ID of the connected account (required)"}, "entityId": {"type": "integer", "description": "Entity ID (optional, for entity-based conversations)"}, "entityType": {"type": "string", "description": "Entity type (optional, for entity-based conversations)"}}, "required": ["lastConversationId", "connectedAccountId"]}}}}}}, "/v1/messages/conversations/by-entity": {"post": {"summary": "returns conversations by entity", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns conversations for the given entity"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"entityType": {"type": "string", "description": "Entity type (e.g., lead, contact)"}, "entityId": {"type": "integer", "description": "Entity ID"}, "entityName": {"type": "string", "description": "Name of the entity"}, "phoneId": {"type": "integer", "description": "ID of the phone number (optional)"}}, "required": ["entityType", "entityId", "entityName"]}}}}}}, "/v1/messages/conversations/{id}/complete": {"put": {"summary": "marks a conversation as completed", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Conversation ID", "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Conversation marked as completed successfully"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/connected-accounts/{connected_account_id}/mapped-fields/{entity_type}": {"get": {"summary": "Connected account mapped fields for entity", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "entity_type", "in": "path", "required": true, "enum": ["lead", "contact"], "schema": {"type": "string"}}], "responses": {"200": {"description": "Connected account field mappings for entity"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Invalid entity type"}}}, "post": {"summary": "Save Connected account mapped fields for entity", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "entity_type", "in": "path", "required": true, "enum": ["lead", "contact"], "schema": {"type": "string"}}], "responses": {"200": {"description": "Connected account field mappings for entity saved successfully"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Invalid data"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"campaign": {"type": "integer"}, "source": {"type": "integer"}, "subSource": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}}}}}}}}, "/v1/messages/whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks": {"post": {"summary": "Interakt Webhook", "tags": ["Message"], "parameters": [], "responses": {"200": {"description": "Interakt webhook received"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"payload_dict": {"type": "object", "properties": {"event": {"type": "string", "enum": ["WABA_ONBOARDED", "WABA_ONBOARDING_FAILED"]}, "isv_name_token": {"type": "string", "description": "Whatsapp Business Token"}, "waba_id": {"type": "string"}, "phone_number_id": {"type": "string"}, "error": {"type": "object", "description": "Error object"}}}}}}}}}, "get": {"summary": "Interakt Challenge", "tags": ["Message"], "responses": {"200": {"description": "Interakt challenge successful"}}}}, "/v1/messages/whatsapp/layout/list": {"get": {"summary": "Layout List", "tags": ["Layouts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Layout List API"}, "401": {"description": "Authentication Failed"}}}}, "/v1/messages": {"post": {"summary": "Creates message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Message created"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["direction"], "properties": {"content": {"type": "text"}}}}}}, "x-access-policy": {"action": "create", "policy": "records", "resource": "message"}}}, "/v1/messages/sync": {"post": {"summary": "Creates message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Message created"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["direction"], "senderNumber": "string", "recipientNumber": "string", "direction": "string", "properties": {"content": {"type": "text"}}}}}}, "x-access-policy": {"action": "create", "policy": "records", "resource": "message"}}}, "/v1/messages/search": {"post": {"summary": "Searches messages", "tags": ["Message"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message searched"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/messages/{id}": {"delete": {"summary": "Delete a message", "tags": ["Message"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message deleted"}, "401": {"description": "Authentication failed"}}}, "get": {"summary": "Retrieves a message", "tags": ["Message"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message found", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "medium": {"type": "string"}, "content": {"type": "string"}, "sentAt": {"type": "datetime"}, "deliveredAt": {"type": "datetime"}, "readAt": {"type": "datetime"}, "direction": {"type": "string"}, "status": {"type": "string"}, "recipientNumber": {"type": "string"}, "senderNumber": {"type": "string"}, "owner": {"type": "object"}, "relatedTo": {"type": "array", "items": {"type": "object"}}, "attachments": {"type": "array", "items": {"type": "object"}}}}}}}, "404": {"description": "Message not found"}}}, "patch": {"summary": "Updates a message", "tags": ["Message"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message updated"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "medium": {"type": "string"}, "content": {"type": "string"}, "sentAt": {"type": "datetime"}, "deliveredAt": {"type": "datetime"}, "readAt": {"type": "datetime"}, "direction": {"type": "string"}, "status": {"type": "string"}, "recipientNumber": {"type": "string"}, "senderNumber": {"type": "string"}, "owner": {"type": "object"}, "relatedTo": {"type": "array", "items": {"type": "object"}}, "attachments": {"type": "array", "items": {"type": "object"}}}, "required": ["id"]}}}}}}, "/v1/messages/connected-accounts/{id}/session-message": {"post": {"summary": "Send Session Message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Session Message Sent"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Invalid params"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["entityType", "entityId", "phoneId", "messageType", "messageBody"], "properties": {"entityType": {"type": "string", "enum": ["lead", "contact"]}, "entityId": {"type": "integer"}, "phoneId": {"type": "integer"}, "messageType": {"type": "string", "enum": ["text"]}, "messageBody": {"type": "string"}}}}}}, "x-access-policy": {"action": "create", "policy": "records", "resource": "message"}}}, "/v1/messages/connected-accounts/{id}/media-session-message": {"post": {"summary": "Send Media Session Message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Session Message Sent"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected account not found"}, "422": {"description": "Insufficient whatsapp credits balance"}}, "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "string"}}}, "required": true}, "x-access-policy": {"action": "create", "policy": "records", "resource": "message"}}}, "/v1/conversations/{conversation_id}/messages/{id}": {"get": {"summary": "Retrieves a conversation message", "tags": ["Message"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "query", "schema": {"type": "string"}}, {"name": "entityType", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message found", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "medium": {"type": "string"}, "content": {"type": "string"}, "sentAt": {"type": "datetime"}, "deliveredAt": {"type": "datetime"}, "readAt": {"type": "datetime"}, "direction": {"type": "string"}, "status": {"type": "string"}, "recipientNumber": {"type": "string"}, "senderNumber": {"type": "string"}, "owner": {"type": "object"}, "relatedTo": {"type": "array", "items": {"type": "object"}}, "attachments": {"type": "array", "items": {"type": "object"}}}}}}}, "404": {"description": "Message not found"}}}}, "/v1/messages/connected-accounts/{connected_account_id}/template-media": {"post": {"summary": "Upload Template Media", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Create Template Media"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account not found"}}, "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "file"}}}}, "x-access-policy": {"action": "upload", "policy": "storage", "resource": "message"}}}, "/v1/messages/connected-accounts/{connected_account_id}/template-media/{id}": {"get": {"summary": "Get Template Media", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "connected_account_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Template Media Details"}, "401": {"description": "Authentication failed"}, "404": {"description": "Connected Account not found"}}}}, "/v1/messages/whatsapp-templates/{id}/variable-mappings": {"get": {"summary": "Variable Mappings", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Whatsapp template variables fetched successfully"}, "401": {"description": "Authentication failed"}, "404": {"description": "Template Not Found"}}}}, "/v1/messages/whatsapp-templates/{id}/variable-mappings/save": {"post": {"summary": "Save Variable Mappings", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Template Updated"}, "401": {"description": "Authentication failed"}, "422": {"description": "Invalid Data"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"variableMappings": {"type": "array", "properties": {"componentType": {"type": "string", "enum": ["HEADER", "BODY", "BUTTON"]}, "templateVariable": {"type": "integer"}, "entity": {"type": "string"}, "internalName": {"type": "string"}, "fallbackValue": {"type": "string"}, "fieldType": {"type": "string"}}}}}}}}, "x-access-policy": {"action": "update", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks": {"post": {"summary": "Whatsapp Webhook", "tags": ["Message"], "parameters": [], "responses": {"200": {"description": "Whatsapp webhook received"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string", "description": "Facebook subscribed business object"}, "entry": {"type": "array", "properties": {"id": {"type": "string", "description": "Whatsapp Business Account ID"}, "changes": {"type": "array", "properties": {"field": {"type": "string", "description": "Whatsapp business entity for which value is given below. Ex: message_template_status_update"}, "value": {"type": "object", "description": "Changes in entity; object differs accordingly"}}}}}}}}}}}, "get": {"summary": "Facebook Challenge", "tags": ["Message"], "responses": {"200": {"description": "Facebook challenge successful"}}}}, "/v1/messages/whatsapp-credits/summary": {"get": {"summary": "Get Summary of Whatsapp credits", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return Whatsapp Credits Summary"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/whatsapp-credits/history": {"post": {"summary": "Get History of Whatsapp credits", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return Whatsapp Credits History"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/whatsapp-credits/status": {"get": {"summary": "Get credits status for bulk message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return Whatsapp Credits Summary"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/whatsapp-templates/{id}": {"get": {"summary": "Whatsapp Template", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Whatsapp Template fetched successfully"}, "401": {"description": "Authentication failed"}, "404": {"description": "Template Not Found"}}}, "put": {"summary": "Update Whatsapp Template", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Template Updated"}, "401": {"description": "Authentication failed"}, "404": {"description": "Template Not Found"}, "422": {"description": "Invalid params"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "components": {"type": "array", "properties": {"id": {"type": "integer"}, "type": {"type": "string", "enum": ["HEADER", "BODY", "FOOTER", "BUTTON"]}, "format": {"type": "string", "enum": ["TEXT", "PHONE_NUMBER", "URL", "QUICK_REPLY", "COPY_CODE"]}, "text": {"type": "string"}, "value": {"type": "string"}, "position": {"type": "integer"}, "content": {"type": "object"}}}}}}}}}}, "/v1/messages/whatsapp-templates/search": {"post": {"summary": "Search Whatsapp Templates", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful search"}, "401": {"description": "Authentication failed"}, "422": {"description": "Invalid search"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "properties": {"field": {"type": "string"}, "type": {"type": "string"}, "operator": {"type": "string"}, "value": {"type": "string"}}}}}}}}}}}, "/v1/messages/whatsapp-templates": {"post": {"summary": "Create Whatsapp Template", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Template Created"}, "401": {"description": "Authentication failed"}, "422": {"description": "Invalid params"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "language": {"type": "string"}, "category": {"type": "string", "enum": ["MARKETING", "UTILITY"]}, "entityType": {"type": "string", "enum": ["lead", "deal", "contact"]}, "connectedAccount": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "integer"}}}, "components": {"type": "array", "properties": {"type": {"type": "string", "enum": ["HEADER", "BODY", "FOOTER", "BUTTON"]}, "format": {"type": "string", "enum": ["TEXT", "PHONE_NUMBER", "URL", "QUICK_REPLY", "COPY_CODE"]}, "text": {"type": "string"}, "value": {"type": "string"}, "position": {"type": "integer"}, "content": {"type": "object"}}}}}}}}}}, "/v1/messages/whatsapp-templates/create-and-submit": {"post": {"summary": "Create & Submit Whatsapp Template", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Template Created & Submitted"}, "401": {"description": "Authentication failed"}, "422": {"description": "Invalid params"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "language": {"type": "string"}, "category": {"type": "string", "enum": ["MARKETING", "UTILITY"]}, "entityType": {"type": "string", "enum": ["lead", "deal", "contact"]}, "connectedAccount": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "integer"}}}, "components": {"type": "array", "properties": {"type": {"type": "string", "enum": ["HEADER", "BODY", "FOOTER", "BUTTON"]}, "format": {"type": "string", "enum": ["TEXT", "PHONE_NUMBER", "URL", "QUICK_REPLY", "COPY_CODE"]}, "text": {"type": "string"}, "value": {"type": "string"}, "position": {"type": "integer"}, "content": {"type": "object"}}}}}}}}, "x-access-policy": {"action": "create", "policy": "WHATSAPP_BUSINESS", "resource": "message"}}}, "/v1/messages/whatsapp-templates/{id}/deactivate": {"post": {"summary": "Deactivate Whatsapp Template", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Whatsapp Template Deactivated"}, "401": {"description": "Authentication failed"}, "404": {"description": "Template Not Found"}, "422": {"description": "Invalid template status to update"}}}}, "/v1/messages/whatsapp-templates/{entity}/variables": {"get": {"summary": "Whatsapp Template Entity Variables", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "entity", "in": "path", "enum": ["lead", "contact", "deal"], "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Entity variables fetched successfully"}, "401": {"description": "Authentication failed"}, "422": {"description": "Invalid params"}}}}, "/v1/messages/whatsapp-templates/lookup": {"get": {"summary": "Whatsapp Template Lookup", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "connectedAccountId", "in": "query", "schema": {"type": "string"}}, {"name": "entityType", "in": "query", "enum": ["lead", "contact", "deal"], "schema": {"type": "string"}}, {"name": "q", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Whatsapp Templates fetched successfully"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/whatsapp-templates/{id}/preview/{entity_type}/{entity_id}": {"get": {"summary": "Whatsapp Template Preview", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_type", "in": "path", "enum": ["lead", "contact", "deal"], "required": true, "schema": {"type": "string"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Whatsapp Templates preview"}, "401": {"description": "Authentication failed"}}}}, "/v1/messages/whatsapp-templates/{id}/send": {"post": {"summary": "Send Template Message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully message sent"}, "422": {"description": "Insufficient whatsapp credits balance"}, "404": {"description": "Missing template"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"entityType": {"type": "string"}, "entityId": {"type": "integer"}, "phoneId": {"type": "integer"}}}}}}}}, "/v1/messages/whatsapp-templates/{id}/send-bulk-message": {"post": {"summary": "Send Bulk Message", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully message sent"}, "404": {"description": "Missing template"}, "401": {"description": "Authentication failed"}, "422": {"description": "Insufficient whatsapp credits balance"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"entityType": {"type": "string"}, "entityId": {"type": "integer"}, "phoneNumber": {"type": "object"}, "entityData": {"type": "object"}, "bulkJob": {"type": "boolean"}}}}}}}}, "/v1/messages/whatsapp-templates/{id}/sync-status": {"put": {"summary": "Sync Whatsapp Template Status", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Template Updated"}, "401": {"description": "Authentication failed"}, "404": {"description": "Template Not Found"}}}}, "/v1/messages/whatsapp-templates/sync": {"post": {"summary": "Sync Whatsapp Templates", "tags": ["Message"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Templates synced successfully"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"entityType": {"type": "array", "items": {"type": "string"}}, "connectedAccountId": {"type": "integer"}}}}}}}}}, "servers": [{"url": "http://{defaultHost}", "variables": {"defaultHost": {"default": "localhost:3000"}}}, {"url": "https://{defaultHost}", "variables": {"defaultHost": {"default": "127.0.0.1:3000"}}}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}