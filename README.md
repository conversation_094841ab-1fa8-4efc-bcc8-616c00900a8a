# SD-Message Service - Complete Documentation

> **This is the single source of truth for the SD-Message microservice.**
> Anyone reading this document should gain complete understanding of the service architecture, flows, and implementation details.

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Database Schema](#database-schema)
- [Core Flows](#core-flows)
- [File Storage](#file-storage)
- [Sequence Diagrams](#sequence-diagrams)
- [Performance Considerations & Bottlenecks](#performance-considerations--bottlenecks)
- [Design Patterns](#design-patterns)
- [Improvements Needed](#improvements-needed)
- [Setup & Deployment](#setup--deployment)

---

## Overview

**SD-Message** is a multi-channel messaging microservice that handles SMS, Chat, and WhatsApp Business communications. It serves as the central messaging hub for the SellDo/Kylas CRM ecosystem.

### Key Capabilities
- **Multi-Channel Messaging**: SMS, Chat, WhatsApp Business
- **WhatsApp Business API**: Integration via Facebook Graph API and Interakt
- **Template Management**: Dynamic templates with variable substitution for personalized messaging
- **Conversation Management**: Thread-based conversations with entity associations (Leads, Contacts, Deals)
- **File Attachments**: Media support (images, videos, documents, audio) via AWS S3
- **Credit Tracking**: WhatsApp conversation billing and usage monitoring
- **Chatbot Integration**: AI-based and rule-based chatbot support
- **Event-Driven Architecture**: RabbitMQ-based microservice communication

### Business Context
- **Multi-tenant**: Supports multiple organizations (tenants) with data isolation
- **Entity-Centric**: Messages are linked to CRM entities (Leads, Contacts, Deals)
- **Permission-Based**: Fine-grained access control via sd-iam service
- **Audit Trail**: Soft deletes, comprehensive logging, and status tracking
- **24-Hour Window**: WhatsApp conversations follow Meta's 24-hour messaging window policy

---

## Architecture

### High-Level System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        External Services                         │
├─────────────────────────────────────────────────────────────────┤
│  Facebook Graph API  │  Interakt API  │  AWS S3  │  RabbitMQ   │
└─────────────────────────────────────────────────────────────────┘
                              ▲
                              │
┌─────────────────────────────┼─────────────────────────────────┐
│                    SD-Message Service                           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐        │
│  │   REST API   │  │   Webhooks   │  │   Sidekiq    │        │
│  │  Controllers │  │   Handlers   │  │   Workers    │        │
│  └──────────────┘  └──────────────┘  └──────────────┘        │
│         │                  │                  │                │
│  ┌──────────────────────────────────────────────────┐         │
│  │            Service Layer (Business Logic)         │         │
│  └──────────────────────────────────────────────────┘         │
│         │                                                       │
│  ┌──────────────────────────────────────────────────┐         │
│  │         ActiveRecord Models (ORM Layer)           │         │
│  └──────────────────────────────────────────────────┘         │
│         │                                                       │
│  ┌──────────────────────────────────────────────────┐         │
│  │              PostgreSQL Database                  │         │
│  └──────────────────────────────────────────────────┘         │
│                                                                 │
│  ┌──────────────────────────────────────────────────┐         │
│  │         RabbitMQ Event Listeners/Publishers       │         │
│  └──────────────────────────────────────────────────┘         │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Microservices Ecosystem                       │
├─────────────────────────────────────────────────────────────────┤
│  sd-iam  │  sd-sales  │  sd-deal  │  sd-config  │  sd-chatbot │
└─────────────────────────────────────────────────────────────────┘
```

### Component Layers

1. **API Layer** (`app/controllers`)
   - RESTful endpoints for CRUD operations
   - JWT-based authentication via `before_action :authenticate`
   - Request validation and parameter sanitization
   - Webhook endpoints for Facebook/Interakt

2. **Service Layer** (`app/services`)
   - Business logic orchestration
   - External API integration (Facebook, Interakt, S3)
   - Data transformation and validation
   - Permission checking

3. **Background Jobs** (`app/jobs`)
   - Sidekiq workers for async processing
   - Template syncing from Meta
   - Media file uploads to S3
   - WhatsApp credit updates
   - Chatbot response processing

4. **Event System** (`app/events`)
   - **Publishers**: Publish events to RabbitMQ
   - **Listeners**: Subscribe to events from other services
   - Inter-service communication
   - Event-driven workflows

5. **Data Layer** (`app/models`)
   - ActiveRecord models with associations
   - Database relationships and constraints
   - Validations and callbacks
   - Soft delete support via `SoftDeletable` concern

---

## Technology Stack

### Core Technologies
- **Ruby**: 3.0.0
- **Rails**: *******
- **Database**: PostgreSQL (with JSONB, array, and full-text search support)
- **Cache/Queue**: Redis (Sidekiq backend, namespace: 'message')
- **Background Jobs**: Sidekiq 6.5.10 (concurrency: 2)
- **Message Queue**: RabbitMQ (via Bunny gem)
- **File Storage**: AWS S3
- **Web Server**: Puma 5.6.8
- **API Documentation**: Rswag (Swagger/OpenAPI)
- **Monitoring**: Prometheus client

### Key Dependencies
```ruby
gem 'bunny'              # RabbitMQ client for event-driven architecture
gem 'aws-sdk-s3'         # AWS S3 integration for file storage
gem 'jwt'                # Token-based authentication
gem 'phonelib'           # Phone number validation and formatting
gem 'prometheus-client'  # Metrics collection and monitoring
gem 'will_paginate'      # Pagination support
gem 'rest-client'        # HTTP client for external API calls
gem 'sidekiq'            # Background job processing
```

### External Service Integrations

| Service | Purpose | API Version |
|---------|---------|-------------|
| **Facebook Graph API** | WhatsApp Business API | v19.0 |
| **Interakt API** | Alternative WhatsApp provider | v17.0 |
| **AWS S3** | File/media storage | - |
| **sd-iam** | Authentication & authorization | - |
| **sd-sales** | Lead/Contact management | - |
| **sd-deal** | Deal management | - |
| **sd-config** | Configuration & share rules | - |
| **sd-whatsapp-chatbot** | Chatbot orchestration | - |

---

## Database Schema

### Entity Relationship Diagram

```
┌─────────────────┐       ┌──────────────────────┐       ┌─────────────────────┐
│     users       │       │  connected_accounts  │       │ whatsapp_templates  │
│─────────────────│       │──────────────────────│       │─────────────────────│
│ id (PK)         │◄──┐   │ id (PK)              │◄──┐   │ id (PK)             │
│ name            │   │   │ tenant_id            │   │   │ name                │
│ tenant_id       │   │   │ waba_id              │   │   │ whatsapp_template_id│
│ phone_number    │   │   │ waba_number          │   │   │ entity_type         │
└─────────────────┘   │   │ phone_number_id      │   │   │ category            │
                      │   │ access_token (enc)   │   │   │ status              │
                      │   │ status               │   │   │ connected_account_id│
                      │   │ is_verified          │   │   │ tenant_id           │
                      │   └──────────────────────┘   │   └─────────────────────┘
                      │              │               │              │
                      │              │               │              │
                      │              ▼               │              ▼
┌─────────────────────┴──────────────────────────────┴──────────────────────────┐
│                              messages                                          │
│────────────────────────────────────────────────────────────────────────────────│
│ id (PK)                                                                        │
│ owner_id (FK → users)                                                          │
│ tenant_id                                                                      │
│ connected_account_id (FK → connected_accounts)                                 │
│ conversation_id (FK → conversations)                                           │
│ sub_conversation_id (FK → sub_conversations)                                   │
│ whatsapp_template_id (FK → whatsapp_templates)                                 │
│                                                                                │
│ medium (sms/whatsapp/chat)                                                     │
│ message_type (whatsapp_business/sms/chat)                                      │
│ direction (incoming=0/outgoing=1)                                              │
│ status (sent/delivered/read/failed/sending/received)                           │
│                                                                                │
│ content (TEXT - HTML/JSON)                                                     │
│ plain_text_content (TEXT)                                                      │
│ component_wise_content (JSONB)                                                 │
│                                                                                │
│ sender_number, recipient_number                                                │
│ remote_id (Facebook/Interakt message ID)                                       │
│                                                                                │
│ sent_at, delivered_at, read_at, failed_at                                      │
│ deleted_at (soft delete)                                                       │
│                                                                                │
│ metadata (JSONB)                                                               │
│ campaign_info (JSONB)                                                          │
└────────────────────────────────────────────────────────────────────────────────┘
         │                                    │
         │                                    │
         ▼                                    ▼
┌─────────────────┐              ┌──────────────────────┐
│  attachments    │              │   message_look_ups   │
│─────────────────│              │──────────────────────│
│ id (PK)         │              │ id (PK)              │
│ message_id (FK) │              │ message_id (FK)      │
│ file_name       │              │ look_up_id (FK)      │
│ size            │              │ recipient (boolean)  │
│ url             │              │ related (boolean)    │
└─────────────────┘              └──────────────────────┘
                                          │
                                          ▼
                                 ┌─────────────────┐
                                 │    look_ups     │
                                 │─────────────────│
                                 │ id (PK)         │
                                 │ entity_type     │
                                 │ entity_id       │
                                 │ phone_number    │
                                 │ name            │
                                 │ tenant_id       │
                                 │ owner_id        │
                                 └─────────────────┘
                                          │
                                          ▼
                                 ┌──────────────────────┐
                                 │ conversation_look_ups│
                                 │──────────────────────│
                                 │ conversation_id (FK) │
                                 │ look_up_id (FK)      │
                                 └──────────────────────┘
                                          │
                                          ▼
┌──────────────────────────────────────────────────────────────┐
│                      conversations                            │
│──────────────────────────────────────────────────────────────│
│ id (PK)                                                       │
│ tenant_id                                                     │
│ connected_account_id (FK)                                     │
│ phone_number (unique per tenant + connected_account)          │
│ owner_id (FK → users)                                         │
│ status (NEW/IN_PROGRESS/COMPLETED)                            │
│ last_message_received_at                                      │
│ last_activity_at                                              │
│ chatbot_conversation_id                                       │
│ chatbot_conversation_completed (boolean)                      │
│ deleted_at (soft delete)                                      │
└──────────────────────────────────────────────────────────────┘
         │
         ▼
┌──────────────────────┐
│  sub_conversations   │
│──────────────────────│
│ id (PK)              │
│ conversation_id (FK) │
│ tenant_id            │
│ connected_account_id │
│ status               │
└──────────────────────┘
```

### Core Tables

#### 1. **messages**
The central table storing all messages (SMS, Chat, WhatsApp).

**Key Columns:**
- `medium`: 'sms', 'whatsapp', 'chat'
- `message_type`: 'whatsapp_business', 'sms', 'chat'
- `direction`: 0 (incoming), 1 (outgoing)
- `status`: 0 (sent), 1 (delivered), 2 (read), 3 (failed), 4 (sending), 5 (received)
- `content`: HTML/JSON content for WhatsApp templates
- `plain_text_content`: Plain text version for search/display
- `component_wise_content`: JSONB array of template components
- `metadata`: JSONB for retry config, additional data
- `campaign_info`: JSONB for campaign tracking
- `remote_id`: External message ID from Facebook/Interakt
- `deleted_at`: Soft delete timestamp

**Indexes:**
- `idx_messages_conversation_not_deleted` on `conversation_id` WHERE `deleted_at IS NULL`
- `index_messages_on_connected_account_id`

**Relationships:**
- `belongs_to :owner` (User)
- `belongs_to :connected_account` (optional)
- `has_many :attachments`
- `has_many :recipient_look_ups` (MessageLookUp where recipient: true)
- `has_many :related_look_ups` (MessageLookUp where related: true)

#### 2. **connected_accounts**
WhatsApp Business Account (WABA) configurations.

**Key Columns:**
- `waba_id`: WhatsApp Business Account ID
- `waba_number`: WhatsApp phone number
- `phone_number_id`: Meta's phone number ID
- `access_token`: Encrypted access token (AES-128-CBC)
- `status`: 'draft', 'pending', 'active', 'inactive'
- `is_verified`: Phone number verification status
- `interakt_onboarding_status`: Interakt-specific onboarding state
- `entities_to_create`: Array of entity types to auto-create (lead/contact)
- `is_chatbot_configured`: Chatbot enabled flag
- `credits_revised_upto`: Last credit sync timestamp
- `sync_whatsapp_template_active_job_count`: Counter for template sync jobs

**Security:**
- Access tokens are encrypted using `OpenSSL::Cipher::AES` with environment-based keys
- Decryption happens on-demand in `Facebook::Request` and `Interakt::Request`

#### 3. **conversations**
Conversation threads between users and phone numbers.

**Key Columns:**
- `phone_number`: Unique per tenant + connected_account
- `owner_id`: Current conversation owner
- `status`: 'NEW', 'IN_PROGRESS', 'COMPLETED'
- `last_message_received_at`: For 24-hour window tracking
- `last_activity_at`: For sorting/filtering
- `chatbot_conversation_id`: Link to chatbot service
- `chatbot_conversation_completed`: Chatbot completion flag

**Business Rules:**
- Unique constraint: `(phone_number, tenant_id, connected_account_id)` where `deleted_at IS NULL`
- WhatsApp 24-hour window: Messages can only be sent within 24 hours of last incoming message (unless using templates)

#### 4. **sub_conversations**
24-hour messaging windows for WhatsApp.

**Purpose:**
- Track individual 24-hour windows
- Each incoming message can start a new sub-conversation
- Status: 'NEW', 'IN_PROGRESS', 'COMPLETED'

#### 5. **whatsapp_templates**
Message templates approved by Meta.

**Key Columns:**
- `name`: Template name (unique per tenant + entity_type + connected_account)
- `whatsapp_template_namespace`: Lowercase, underscored version
- `whatsapp_template_id`: Meta's template ID
- `entity_type`: 'lead', 'contact', 'deal'
- `category`: 'MARKETING', 'UTILITY'
- `status`: 'DRAFT', 'SUBMITTING', 'PENDING', 'APPROVED', 'REJECTED', 'FLAGGED', 'PAUSED', 'PENDING_DELETION', 'INACTIVE'
- `language`: Template language code (e.g., 'en', 'en_US')
- `reason`: Rejection/flagging reason
- `additional_info`: JSONB for extra metadata

**Relationships:**
- `has_many :components` (WhatsappTemplateComponent)
- `has_many :variable_mappings`
- `belongs_to :connected_account`

#### 6. **whatsapp_template_components**
Template structure (header, body, footer, buttons).

**Key Columns:**
- `component_type`: 'HEADER', 'BODY', 'FOOTER', 'BUTTON'
- `component_format`: 'TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT', 'URL', 'PHONE_NUMBER', 'COPY_CODE'
- `component_text`: Template text with variables (e.g., "Hello {{1}}")
- `component_value`: Button URL/phone number
- `content`: JSONB for complex structures
- `media_type`: For header media
- `position`: Component order

#### 7. **variable_mappings**
Maps template variables to entity fields.

**Key Columns:**
- `component_type`: 'HEADER', 'BODY', 'BUTTON_URL'
- `template_variable`: Variable index (1, 2, 3...)
- `entity`: 'lead', 'contact', 'deal', 'user'
- `internal_name`: Field name (e.g., 'firstName', 'companyName')
- `fallback_value`: Default value if field is empty
- `field_type`: Field data type
- `parent_entity`: For nested fields (e.g., 'ownerId' → 'user')

#### 8. **look_ups**
Entity references (Leads, Contacts, Deals, Users).

**Purpose:**
- Normalize entity references
- Enable polymorphic associations
- Support phone number-based lookups

**Key Columns:**
- `entity_type`: 'lead', 'contact', 'deal', 'user'
- `entity_id`: ID in the respective service
- `phone_number`: Normalized phone number
- `name`: Entity name (cached)
- `owner_id`: Entity owner

#### 9. **message_look_ups**
Join table linking messages to entities.

**Key Columns:**
- `recipient`: Boolean - is this a recipient?
- `related`: Boolean - is this a related entity?

**Usage:**
- A message can have multiple recipients
- A message can be related to multiple entities (e.g., Lead + Deal)

#### 10. **whatsapp_credits**
Credit balance per tenant.

**Key Columns:**
- `total`: Total credits purchased
- `consumed`: Credits used
- `parked`: Credits reserved for pending messages
- `credits_revised_at`: Last update timestamp
- `is_low_credits_email_sent`: Email notification flag

#### 11. **whatsapp_credit_histories**
Credit consumption log.

**Key Columns:**
- `entry_type`: 'CREDIT', 'DEBIT'
- `conversation_category`: 'MARKETING', 'UTILITY', 'SERVICE', 'AUTHENTICATION'
- `conversation_type`: 'REGULAR', 'FREE_TIER', 'FREE_ENTRY_POINT'
- `start_time`, `end_time`: Conversation window
- `conversation_count`: Number of conversations
- `phone_number`, `country`: For analytics
- `value`: Credit amount
- `balance`: Remaining balance after transaction

**Indexes:**
- `whatsappcredithistories_tenantId_idx`
- `whatsappcredithistories_tenantIdAndConversationCategory_idx`

#### 12. **attachments**
File attachments for messages.

**Key Columns:**
- `file_name`: S3 path (e.g., `tenant_1/user_2/attachments/123_file_1234567890.jpg`)
- `size`: File size in bytes
- `url`: Legacy field (not used, S3 presigned URLs generated on-demand)

**File Naming Convention:**
```
tenant_{tenant_id}/user_{owner_id}/attachments/{message_id}_{original_name}_{timestamp}.{ext}
```

#### 13. **field_mappings**
Campaign/source tracking for WhatsApp conversations.

**Key Columns:**
- `entity_type`: 'lead', 'contact'
- `campaign`, `source`, `sub_source`: Campaign tracking
- `utm_source`, `utm_campaign`, `utm_medium`, `utm_content`, `utm_term`: UTM parameters

**Usage:**
- When a conversation is created, these fields are applied to the created entity

#### 14. **agent_users**
WhatsApp agents assigned to connected accounts.

**Key Columns:**
- `connected_account_id`
- `user_id`
- `entity_type`: 'lead', 'contact', 'deal'

**Purpose:**
- Control which users can send messages from a connected account
- Permission filtering

#### 15. **share_rules**
Sharing rules for entity access.

**Key Columns:**
- `entity_type`: 'lead', 'contact', 'deal'
- `from_type`, `from_id`: Sharer (USER/TEAM)
- `to_type`, `to_id`: Sharee (USER/TEAM)
- `share_all_records`: Share all vs specific records
- `actions`: JSONB of allowed actions (read, write, sms, etc.)

#### 16. **teams**
User teams for permission management.

**Key Columns:**
- `name`
- `tenant_id`
- `user_ids`: Array of user IDs

#### 17. **template_media**
Media files for template headers.

**Key Columns:**
- `file_name`: S3 path
- `file_size`, `file_type`
- `whatsapp_file_handle`: Meta's media handle
- `whatsapp_template_component_id`: Associated component

**Cleanup:**
- Unused media is deleted daily via `Listeners::ListenForDailyScheduler`

#### 18. **chatbot_media**
Media files for chatbot messages.

**Key Columns:**
- `file_name`: S3 path
- `file_size`, `file_type`
- `chatbot_id`
- `facebook_media_id`: Meta's media ID
- `connected_account_id`

---

## Core Flows

### 1. WhatsApp Template Message Flow

**Endpoint:** `POST /v1/messages/connected-accounts/:id/whatsapp-templates/:template_id/send`

**Flow:**
1. **Request Validation**
   - Authenticate user via JWT
   - Validate permissions (`whatsappTemplate.write`)
   - Check if user is agent for connected account
   - Validate template exists and is APPROVED

2. **Entity & Phone Number Resolution**
   - Fetch entity details from sd-sales/sd-deal
   - Extract phone numbers
   - Validate phone numbers using `phonelib`

3. **Conversation Management**
   - Find or create conversation for phone number
   - Create sub-conversation if needed
   - Update conversation owner if manual send

4. **Variable Substitution**
   - Fetch variable mappings for template
   - Resolve entity field values
   - Apply fallback values if needed
   - Build template payload with substituted values

5. **Message Sending**
   - Call Interakt/Facebook API
   - Receive `remote_id` from provider
   - Create message record with status 'sending'

6. **Post-Send Actions**
   - Publish `WhatsappMessageCreatedV2` event
   - Update conversation `last_activity_at`
   - End chatbot conversation if manual send

**Code Path:**
```
WhatsappTemplatesController#send_message
  → WhatsappTemplateService#send_message
    → ConversationService#create_or_fetch_conversation
    → WhatsappTemplateService#send_message_via_interakt
      → Interakt::Message#send_template_message
        → Interakt::Request.process
    → Message::MessageService.call
    → Publishers::WhatsappMessageCreatedV2Publisher.call
```

---

### 2. Incoming WhatsApp Message Flow (Webhook)

**Endpoint:** `POST /v1/messages/whatsapp/{webhook_token}/webhooks`

**Flow:**
1. **Webhook Reception**
   - Receive webhook from Facebook/Interakt
   - Skip authentication (webhook uses secret token in URL)
   - Publish to RabbitMQ immediately (async processing)

2. **Event Processing** (via `Listeners::ListenForWhatsappWebhook`)
   - Parse webhook payload
   - Extract message data (text, media, location, etc.)
   - Find connected account by `waba_id` and `phone_number_id`

3. **Conversation Lookup/Creation**
   - Find conversation by phone number
   - If not found, create new conversation
   - Associate with entity if phone number matches Lead/Contact
   - Create sub-conversation for 24-hour window

4. **Message Creation**
   - Determine message type (text, image, video, document, audio, location, etc.)
   - Create message record with status 'received'
   - For media messages, create attachment placeholder

5. **Media Processing** (if applicable)
   - Queue `UploadMediaAttachmentToS3Job`
   - Job fetches media URL from Meta
   - Downloads media file
   - Uploads to S3
   - Updates attachment record

6. **Chatbot Handling** (if configured)
   - Check if chatbot is enabled for connected account
   - Queue `InitiateChatbotConversationJob`
   - Publish `ChatbotUserResponse` event to chatbot service

7. **Event Publishing**
   - Publish `WhatsappMessageCreatedV2` event
   - Publish `MessageReceivedFromEntity` event (if from Lead/Contact)

**Code Path:**
```
WebhooksController#handler
  → PublishEvent.call(Event::WhatsappWebhook)
    → RabbitMQ: message.whatsapp.webhook
      → Listeners::ListenForWhatsappWebhook
        → WhatsappWebhooks#process
          → WhatsappWebhooks#process_incoming_message
            → ConversationService#create_or_fetch_conversation
            → Message::SyncService.call
            → UploadMediaAttachmentToS3Job.perform_later (if media)
            → InitiateChatbotConversationJob.perform_later (if chatbot)
            → Publishers::WhatsappMessageCreatedV2Publisher.call
```

---

### 3. Message Status Update Flow (Webhook)

**Endpoint:** Same webhook endpoint as incoming messages

**Flow:**
1. **Webhook Reception**
   - Receive status update (sent, delivered, read, failed)
   - Parse status from webhook payload

2. **Message Lookup**
   - Find message by `remote_id`
   - Validate message exists

3. **Status Update**
   - Update message status
   - Set timestamp (`sent_at`, `delivered_at`, `read_at`, `failed_at`)
   - Store error message if failed

4. **Event Publishing**
   - Publish `WhatsappMessageStatusUpdatedV2` event
   - Include old and new status for tracking

**Status Transitions:**
```
sending → sent → delivered → read
sending → failed
```

---

### 4. Session Message Flow (24-Hour Window)

**Endpoint:** `POST /v1/messages/connected-accounts/:id/session-message`

**Purpose:** Send free-form messages within 24-hour window (no template required)

**Flow:**
1. **Validation**
   - Check if conversation exists
   - Validate 24-hour window (last_message_received_at + 24 hours > now)
   - Authenticate and authorize user

2. **Message Sending**
   - Call Interakt API with message body
   - Create message record

3. **Conversation Update**
   - Update `last_activity_at`
   - End chatbot if manual send

**Code Path:**
```
MessagesController#session_message
  → Message::SessionMessage#call
    → Interakt::Message#send_session_message
    → Message::MessageService.call
```

---

### 5. Media Session Message Flow

**Endpoint:** `POST /v1/messages/connected-accounts/:id/media-session-message`

**Flow:**
1. **File Upload**
   - Receive file via multipart/form-data
   - Validate file type and size
   - Save to temp directory

2. **S3 Upload**
   - Upload to S3 with naming convention
   - Generate S3 key

3. **Facebook Media Upload**
   - Download from S3
   - Upload to Facebook via resumable upload session
   - Receive Facebook media handle

4. **Message Sending**
   - Send message with media handle
   - Create message and attachment records

5. **Cleanup**
   - Delete temp file

**Supported Media Types:**
- Image: jpg, jpeg, png
- Video: mp4, 3gp
- Document: pdf, doc, docx, xls, xlsx, ppt, pptx
- Audio: mp3, ogg, amr, aac

**Code Path:**
```
MessagesController#media_session_message
  → Message::SessionMessage#send_media_message
    → S3::UploadFile.call
    → Facebook::Media#upload
    → Interakt::Message#send_session_message
    → Message::MessageService.call
```

---

### 6. Template Creation & Submission Flow

**Endpoint:** `POST /v1/messages/connected-accounts/:id/whatsapp-templates/create-and-submit`

**Flow:**
1. **Template Creation**
   - Validate template structure
   - Generate namespace (lowercase, underscored)
   - Create template components (header, body, footer, buttons)
   - Extract variables from component text

2. **Media Upload** (if header has media)
   - Upload media to S3
   - Upload to Facebook
   - Store Facebook media handle

3. **Facebook Submission**
   - Build Facebook API payload
   - Submit template for review
   - Set status to 'SUBMITTING'

4. **Background Job**
   - Queue `SubmitWhatsappTemplateJob`
   - Job submits to Facebook
   - Updates status based on response

5. **Status Tracking**
   - Webhook updates status (APPROVED, REJECTED, FLAGGED, etc.)
   - Event published on status change

**Code Path:**
```
WhatsappTemplatesController#create_and_submit
  → WhatsappTemplateService#create_and_submit
    → WhatsappTemplateService#create
    → SubmitWhatsappTemplateJob.perform_later
      → Facebook::WhatsappTemplate#create
      → WhatsappTemplate.update(status: ...)
```

---

### 7. Template Sync Flow

**Endpoint:** `POST /v1/messages/connected-accounts/:id/whatsapp-templates/sync`

**Purpose:** Sync templates from Meta to local database

**Flow:**
1. **Fetch Template List**
   - Call Facebook API to get all templates
   - Paginate through results (100 per page)

2. **Template Processing**
   - For each template, check if exists locally
   - If new, queue `FetchWhatsappTemplateDetailsFromMetaJob`
   - If exists, skip

3. **Detail Fetching** (Background Job)
   - Fetch full template details from Meta
   - Parse components, variables, media
   - Create/update local template record

4. **Completion**
   - Decrement `sync_whatsapp_template_active_job_count`
   - All jobs complete when counter reaches 0

**Code Path:**
```
WhatsappTemplatesController#sync
  → WhatsappTemplateService#sync_templates
    → FetchWhatsappTemplatesFromMetaJob.perform_later
      → Facebook::Request (paginated)
      → FetchWhatsappTemplateDetailsFromMetaJob.perform_later (per template)
        → Facebook::WhatsappTemplate#get_details
        → WhatsappTemplate.create_or_update
```

---

### 8. Conversation Creation & Entity Association Flow

**Triggered by:** Incoming message, outgoing message, or entity creation event

**Flow:**
1. **Phone Number Normalization**
   - Parse phone number with `phonelib`
   - Extract country code and national number

2. **Conversation Lookup**
   - Search by phone number + tenant + connected_account
   - Exclude soft-deleted conversations

3. **Entity Matching**
   - Search look_ups table for matching phone number
   - If found, associate conversation with entity
   - If not found and incoming message, create unassociated conversation

4. **Conversation Creation**
   - Create conversation record
   - Create initial sub-conversation
   - Set owner (entity owner or current user)

5. **Entity Creation** (if configured)
   - Check `connected_account.entities_to_create`
   - If includes 'lead' or 'contact', create entity in sd-sales
   - Apply field mappings (campaign, source, UTM params)

**Event-Driven Association:**
- When Lead/Contact created in sd-sales, event published
- `Listeners::ListenForLeadCreatedV2` / `ListenForContactCreatedV2`
- Associates existing conversations with new entity

**Code Path:**
```
ConversationService#create_or_fetch_conversation
  → Conversation.find_or_create
  → ConversationService#associate_with_entity
  → ConversationService#create_entity_if_configured
```

---

### 9. WhatsApp Credit Tracking Flow

**Scheduled:** Daily at 1 AM via `Listeners::ListenFor1amScheduler`

**Flow:**
1. **Trigger**
   - RabbitMQ event: `scheduler.1am`
   - Queue `UpdateWhatsappCreditsJob`

2. **Tenant Processing**
   - Get all tenants with connected accounts
   - Queue `TenantWiseUpdateWhatsappCreditsJob` per tenant

3. **Credit Fetching**
   - Call Interakt API for conversation analytics
   - Fetch conversations since last sync (`credits_revised_upto`)
   - Group by conversation category and type

4. **Credit Calculation**
   - Calculate cost per conversation based on country and category
   - Use pricing from `config/whatsapp-pricing.json`
   - Sum total consumption

5. **Database Update**
   - Update `whatsapp_credits.consumed`
   - Create `whatsapp_credit_histories` records
   - Update `connected_account.credits_revised_upto`

6. **Low Credit Alert**
   - Check if balance < 10% of total
   - If low and email not sent, publish `WhatsappCreditsAboutToExpire` event
   - Set `is_low_credits_email_sent` flag

7. **Usage Publishing**
   - Publish `TenantUsagePublisher` event for billing service

**Code Path:**
```
Listeners::ListenFor1amScheduler
  → UpdateWhatsappCreditsJob.perform
    → TenantWiseUpdateWhatsappCreditsJob.perform (per tenant)
      → WhatsappCreditsService#update
        → Interakt::ConversationAnalytics#fetch
        → WhatsappCredit.update(consumed: ...)
        → WhatsappCreditHistory.create
        → Publishers::SendLowWhatsappCreditsEmailPublisher.call (if low)
        → Publishers::TenantUsagePublisher.call
```

---

### 10. Chatbot Integration Flow

**Trigger:** Incoming message when chatbot is configured

**Flow:**
1. **Chatbot Check**
   - Check `connected_account.is_chatbot_configured`
   - Check if conversation already has active chatbot session

2. **Initiation** (First Message)
   - Queue `InitiateChatbotConversationJob`
   - Publish `ChatbotUserResponse` event to chatbot service
   - Chatbot service processes and responds

3. **Response Handling**
   - Listen for `ChatbotConversationResponse` event
   - Queue `ProcessChatbotResponseJob`
   - Send chatbot messages via session message API

4. **Continuation**
   - User replies
   - Publish `ChatbotUserResponse` event
   - Chatbot responds
   - Repeat until conversation complete

5. **Completion**
   - Chatbot marks conversation as complete
   - Set `chatbot_conversation_completed = true`
   - Publish `ChatbotConversationCompleted` event

6. **Manual Override**
   - If user sends manual message, end chatbot
   - Set `chatbot_conversation_completed = true`
   - Publish completion event

**Code Path:**
```
WhatsappWebhooks#process_incoming_message
  → InitiateChatbotConversationJob.perform_later
    → Publishers::ChatbotUserResponsePublisher.call
      → RabbitMQ: message.chatbot.user.response
        → Chatbot Service processes
        → Publishes: chatbot.conversation.response
          → Listeners::ListenForChatbotConversationResponse
            → ProcessChatbotResponseJob.perform_later
              → ChatbotMessageService#send_message
                → Interakt::Message#send_session_message
```

---

## File Storage

### AWS S3 Architecture

**Bucket:** Configured via `S3_ATTACHMENT_BUCKET` environment variable
- **QA:** `qa-message-attachment`
- **Staging/Production:** Set per environment

**Storage Structure:**
```
s3://bucket-name/
├── tenant_1/
│   ├── user_10/
│   │   └── attachments/
│   │       ├── 123_photo_1640000000.jpg
│   │       ├── 124_document_1640000001.pdf
│   │       └── 125_video_1640000002.mp4
│   └── user_11/
│       └── attachments/
│           └── ...
├── tenant_1/
│   └── connected_account_5/
│       ├── template_header_uuid.jpg
│       └── chatbot-attachments/
│           └── chatbot_media_uuid.png
└── ...
```

### File Naming Conventions

#### 1. Message Attachments
**Pattern:** `tenant_{tenant_id}/user_{owner_id}/attachments/{message_id}_{original_name}_{timestamp}.{ext}`

**Example:** `tenant_42/user_123/attachments/9876_invoice_1640000000.pdf`

**Generation:**
```ruby
file_name = @file['file_name'].split('.').first
file_ext = @file['file_name'].split('.').last
name = "#{@message.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"
file_name = "tenant_#{@message.tenant_id}/user_#{@message.owner_id}/attachments/#{name}"
```

#### 2. Template Media
**Pattern:** `tenant_{tenant_id}/connected_account_{id}/{original_name}_{uuid}.{ext}`

**Example:** `tenant_42/connected_account_5/header_image_a1b2c3d4.jpg`

**Generation:**
```ruby
file_name = "tenant_#{tenant_id}/connected_account_#{connected_account.id}/#{file_name.gsub(file_type, '')}_#{SecureRandom.uuid}#{file_type}"
```

#### 3. Chatbot Media
**Pattern:** `tenant_{tenant_id}/connected_account_{id}/chatbot-attachments/{original_name}_{uuid}.{ext}`

**Example:** `tenant_42/connected_account_5/chatbot-attachments/welcome_image_x9y8z7.png`

### Upload Flow

#### Outgoing Message Attachments
1. **Client Upload**
   - Client sends file in request body
   - File saved to temp directory

2. **S3 Upload**
   - `S3::UploadFile.new(file_path, file_name, S3_ATTACHMENT_BUCKET).call`
   - Uses AWS SDK v3
   - Uploads via `obj.upload_file(file_path)`

3. **Database Record**
   - Create `Attachment` record
   - Store S3 key in `file_name` column
   - Store file size

4. **Cleanup**
   - Delete temp file (unless `skip_delete: true`)

**Code:**
```ruby
# app/services/attachment/create_service.rb
S3::UploadFile.new(file_path, file_name, S3_ATTACHMENT_BUCKET, @file['skip_delete']).call
Attachment.create!(
  message_id: @message.id,
  file_name: file_name,
  size: file_size
)
```

#### Incoming Message Attachments (WhatsApp)
1. **Webhook Reception**
   - Receive media ID from Facebook/Interakt
   - Create attachment placeholder

2. **Background Job** (`UploadMediaAttachmentToS3Job`)
   - Fetch media URL from Meta API
   - Download media to temp file
   - Upload to S3
   - Update attachment record

**Code:**
```ruby
# app/jobs/upload_media_attachment_to_s3_job.rb
media_url_response = Interakt::Media.new(connected_account).get_media_url(media_id)
fetch_media_response = Interakt::Media.new(connected_account).download_from_media_url(media_url)

incoming_media_file = open("#{Rails.root}/tmp/#{name}", 'wb') do |file|
  file << fetch_media_response.body
end

S3::UploadFile.new(file_path, file_name, S3_ATTACHMENT_BUCKET).call
message_attachment.update(file_name: file_name, size: file_size)
```

### Download Flow

#### Presigned URLs
Files are **never** served directly. Instead, presigned URLs are generated on-demand.

**Generation:**
```ruby
# app/services/s3/get_presigned_url.rb
obj = @bucket.object(@path)
url = obj.presigned_url(:get, expires_in: 300, response_content_disposition: "attachment; filename=#{@file_name}")
```

**Expiration:** 5 minutes (300 seconds)

**Usage:**
- When client requests message details, presigned URLs generated for attachments
- URLs include `Content-Disposition` header for proper filename

**Code:**
```ruby
# app/serializers/message_serializer.rb
attachment_url = S3::GetPresignedUrl.new(
  attachment.file_name,
  attachment.extract_file_name,
  S3_ATTACHMENT_BUCKET
).call
```

### File Operations

#### Copy
**Use Case:** Duplicate attachments when copying messages

```ruby
S3::CopyAttachmentOnS3.new(src_path, dest_path).call
```

#### Delete
**Use Case:** Cleanup when deleting messages

```ruby
S3::DeleteFileFromS3.new(files_path, S3_ATTACHMENT_BUCKET).call
```

**Batch Delete:**
```ruby
objects_to_delete = files_path.map { |path| { key: path } }
@bucket.delete_objects({ delete: { objects: objects_to_delete } })
```

#### Download (for processing)
**Use Case:** Download file for re-upload to Facebook

```ruby
S3::DownloadFileFromS3.new(file_name).call
obj = @bucket.object(@file_name)
obj.download_file(local_path)
```

### AWS Configuration

**Credentials:**
```ruby
Aws.config.update(
  endpoint: ENV['AWS_ENDPOINT'],
  region: ENV['AWS_REGION'],
  credentials: Aws::Credentials.new(
    ENV['AWS_ACCESS_KEY_ID'],
    ENV['AWS_SECRET_ACCESS_KEY']
  )
)
```

**Environment Variables:**
- `AWS_ENDPOINT`: S3 endpoint (optional, for S3-compatible services)
- `AWS_REGION`: AWS region (e.g., 'us-east-1')
- `AWS_ACCESS_KEY_ID`: Access key
- `AWS_SECRET_ACCESS_KEY`: Secret key
- `S3_ATTACHMENT_BUCKET`: Bucket name

### Storage Cleanup

#### Daily Cleanup (3 AM)
**Trigger:** `Listeners::ListenForDailyScheduler`

**Actions:**
1. **Unused Template Media**
   - Find `template_media` not linked to any template component
   - Delete from S3
   - Delete database record

2. **Soft-Deleted Messages**
   - Find messages with `deleted_at` set
   - Delete attachments from S3
   - Hard delete message and attachment records

3. **Soft-Deleted Conversations**
   - Find conversations with `deleted_at` set (older than retention period)
   - Hard delete conversation records

**Code:**
```ruby
# app/events/listeners/listen_for_daily_scheduler.rb
TemplateMediaService.new({}).delete_unused_media
ConversationService.new({}).cleanup_soft_deleted_conversations

Message.soft_deleted.find_each do |message|
  Message::DeleteMessageService.new(message.id, false, {...}, true).call
end
```

### File Type Support

#### WhatsApp Supported Types
```ruby
ALLOWED_FILE_TYPES = {
  'image' => ['.jpg', '.jpeg', '.png'],
  'video' => ['.mp4', '.3gp'],
  'document' => ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
  'audio' => ['.mp3', '.ogg', '.amr', '.aac']
}
```

#### MIME Type Mapping
```ruby
def get_mime_type(file_type)
  case file_type
  when '.jpg', '.jpeg' then 'image/jpeg'
  when '.png' then 'image/png'
  when '.mp4' then 'video/mp4'
  when '.pdf' then 'application/pdf'
  # ... etc
  end
end
```

### Security Considerations

1. **Access Control**
   - Presigned URLs expire after 5 minutes
   - No public bucket access
   - Files organized by tenant for isolation

2. **Encryption**
   - S3 server-side encryption (SSE) recommended
   - Access tokens encrypted in database (AES-128-CBC)

3. **Validation**
   - File type validation before upload
   - File size limits enforced
   - Virus scanning recommended (not implemented)

---

## Sequence Diagrams

### 1. Send WhatsApp Template Message

```mermaid
sequenceDiagram
    participant Client
    participant API as SD-Message API
    participant Service as WhatsappTemplateService
    participant Conv as ConversationService
    participant Interakt as Interakt API
    participant DB as PostgreSQL
    participant RMQ as RabbitMQ
    participant Sales as SD-Sales

    Client->>API: POST /whatsapp-templates/:id/send
    API->>API: Authenticate & Authorize
    API->>Service: send_message(params)

    Service->>Sales: GET /entities/:type/:id
    Sales-->>Service: Entity details + phone numbers

    Service->>Conv: create_or_fetch_conversation
    Conv->>DB: Find/Create Conversation
    Conv->>DB: Create SubConversation
    DB-->>Conv: Conversation
    Conv-->>Service: Conversation

    Service->>Service: Resolve variable mappings
    Service->>Service: Build template payload

    Service->>Interakt: POST /messages (template)
    Interakt-->>Service: {remote_id}

    Service->>DB: Create Message (status: sending)
    DB-->>Service: Message

    Service->>RMQ: Publish WhatsappMessageCreatedV2

    Service-->>API: Success
    API-->>Client: 200 OK

    Note over Interakt: Async webhook
    Interakt->>API: POST /webhooks (status: sent)
    API->>RMQ: Publish WhatsappWebhook
    RMQ->>Service: Process status update
    Service->>DB: Update Message (status: sent)
    Service->>RMQ: Publish WhatsappMessageStatusUpdatedV2
```

### 2. Receive Incoming WhatsApp Message with Media

```mermaid
sequenceDiagram
    participant WA as WhatsApp User
    participant Meta as Facebook/Interakt
    participant Webhook as Webhook Handler
    participant RMQ as RabbitMQ
    participant Listener as WhatsappWebhooks
    participant Conv as ConversationService
    participant DB as PostgreSQL
    participant Sidekiq as Sidekiq Worker
    participant S3 as AWS S3
    participant Chatbot as Chatbot Service

    WA->>Meta: Send message with image
    Meta->>Webhook: POST /webhooks {message, media_id}
    Webhook->>RMQ: Publish WhatsappWebhook event
    Webhook-->>Meta: 200 OK

    RMQ->>Listener: Consume event
    Listener->>Listener: Parse payload
    Listener->>DB: Find ConnectedAccount

    Listener->>Conv: create_or_fetch_conversation
    Conv->>DB: Find/Create Conversation
    Conv-->>Listener: Conversation

    Listener->>DB: Create Message (status: received)
    Listener->>DB: Create Attachment (placeholder)

    Listener->>Sidekiq: Queue UploadMediaAttachmentToS3Job
    Listener->>RMQ: Publish WhatsappMessageCreatedV2

    alt Chatbot Enabled
        Listener->>Sidekiq: Queue InitiateChatbotConversationJob
        Sidekiq->>RMQ: Publish ChatbotUserResponse
        RMQ->>Chatbot: Process user message
    end

    Note over Sidekiq: Background processing
    Sidekiq->>Meta: GET /media/{media_id}
    Meta-->>Sidekiq: {media_url}
    Sidekiq->>Meta: Download media
    Meta-->>Sidekiq: Binary data
    Sidekiq->>S3: Upload file
    S3-->>Sidekiq: Success
    Sidekiq->>DB: Update Attachment (file_name, size)
```

### 3. Media Message Send Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as SD-Message API
    participant Service as SessionMessage
    participant S3 as AWS S3
    participant Meta as Facebook API
    participant Interakt as Interakt API
    participant DB as PostgreSQL

    Client->>API: POST /media-session-message<br/>(multipart/form-data)
    API->>API: Authenticate
    API->>Service: send_media_message(file, params)

    Service->>Service: Validate file type & size
    Service->>Service: Generate S3 key

    Service->>S3: Upload file
    S3-->>Service: Success

    Service->>S3: Download file (for Facebook)
    S3-->>Service: File data

    Service->>Meta: POST /upload_session (create)
    Meta-->>Service: {upload_session_id}

    Service->>Meta: POST /upload (file data)
    Meta-->>Service: {media_handle}

    Service->>Interakt: POST /messages<br/>{type: image, image: {id: media_handle}}
    Interakt-->>Service: {remote_id}

    Service->>DB: Create Message
    Service->>DB: Create Attachment

    Service->>Service: Delete temp file
    Service-->>API: {message_id}
    API-->>Client: 200 OK {id}
```

---

## Performance Considerations & Bottlenecks

### Current Performance Issues

#### 1. **N+1 Query Problem in Conversation Listing**
**Location:** `app/queries/filter_conversations_query.rb`

**Issue:**
```ruby
# For each conversation, fetches associated entities separately
conversations.each do |conv|
  conv.look_ups.each do |lookup|
    # Separate query to fetch entity details from sd-sales/sd-deal
    entity_details = fetch_entity(lookup.entity_type, lookup.entity_id)
  end
end
```

**Impact:**
- 1 query for conversations
- N queries for look_ups (where N = number of conversations)
- M queries for entity details (where M = number of unique entities)
- For 100 conversations with 2 entities each: **201 queries**

**Solution:**
```ruby
# Eager load associations
conversations = conversations.includes(:look_ups)

# Batch fetch entity details
entity_ids_by_type = look_ups.group_by(&:entity_type).transform_values { |v| v.map(&:entity_id) }
entity_ids_by_type.each do |type, ids|
  # Single batch request per entity type
  fetch_entities_batch(type, ids)
end
```

**Estimated Improvement:** 201 queries → 4 queries (75% reduction)

---

#### 2. **Synchronous External API Calls in Request Path**
**Location:** `app/services/whatsapp_template_service.rb#send_message`

**Issue:**
- Fetches entity details from sd-sales/sd-deal synchronously
- Calls Interakt/Facebook API synchronously
- Total request time: 500ms - 2000ms

**Impact:**
- Blocks request thread
- Poor user experience
- Sidekiq concurrency limited to 2 (can't handle spikes)

**Solution:**
```ruby
# Move to background job
SendWhatsappTemplateMessageJob.perform_later(template_id, entity_id, params)

# Return immediately
render json: { status: 'queued', job_id: job_id }, status: :accepted
```

**Estimated Improvement:** Response time 1500ms → 50ms (96% reduction)

---

#### 3. **Missing Database Indexes**
**Issue:** Several queries lack proper indexes

**Missing Indexes:**
```sql
-- Frequently queried but not indexed
CREATE INDEX idx_messages_tenant_owner ON messages(tenant_id, owner_id);
CREATE INDEX idx_messages_remote_id ON messages(remote_id);
CREATE INDEX idx_conversations_phone_tenant ON conversations(phone_number, tenant_id, connected_account_id);
CREATE INDEX idx_look_ups_phone_tenant ON look_ups(phone_number, tenant_id);
CREATE INDEX idx_whatsapp_templates_namespace ON whatsapp_templates(whatsapp_template_namespace, tenant_id);
```

**Impact:**
- Full table scans on large tables
- Slow webhook processing (remote_id lookup)
- Slow conversation lookups

**Estimated Improvement:** Query time 500ms → 5ms (99% reduction)

---

#### 4. **Inefficient Conversation Query with Window Function**
**Location:** `app/queries/filter_conversations_query.rb`

**Issue:**
```sql
SELECT * FROM (
  SELECT *, ROW_NUMBER() OVER (PARTITION BY phone_number ORDER BY last_activity_at DESC) as rownum
  FROM conversations
  LEFT JOIN conversation_look_ups ...
  WHERE ...
) tmp
WHERE rownum=1
```

**Problem:**
- Window function processes ALL conversations before filtering
- No index on `(phone_number, last_activity_at)`
- Inefficient for large datasets

**Solution:**
```sql
-- Use DISTINCT ON (PostgreSQL-specific)
SELECT DISTINCT ON (phone_number)
  conversations.*
FROM conversations
LEFT JOIN conversation_look_ups ...
WHERE ...
ORDER BY phone_number, last_activity_at DESC NULLS LAST
```

**Estimated Improvement:** 2000ms → 200ms (90% reduction) for 10k conversations

---

#### 5. **Lack of Caching**
**Issue:** No caching layer implemented

**Cacheable Data:**
- User permissions (fetched from sd-iam on every request)
- Entity details (fetched from sd-sales/sd-deal frequently)
- WhatsApp template details
- Connected account details

**Solution:**
```ruby
# config/environments/production.rb
config.cache_store = :redis_cache_store, {
  url: ENV['REDIS_URL'],
  namespace: 'sd-message',
  expires_in: 5.minutes
}

# Usage
Rails.cache.fetch("user_permissions:#{user_id}", expires_in: 5.minutes) do
  User::GetUserProfile.call(user_id)
end
```

**Estimated Improvement:**
- 80% reduction in external API calls
- 200ms → 5ms for cached requests

---

#### 6. **Sidekiq Concurrency Too Low**
**Location:** `config/initializers/sidekiq.rb`

**Issue:**
```ruby
config.options[:concurrency] = 2  # Only 2 workers!
```

**Impact:**
- Media upload jobs queue up
- Template sync jobs take hours
- Credit update jobs delayed

**Solution:**
```ruby
config.options[:concurrency] = 10  # Or based on available resources
```

**Recommended:**
- Separate queues for different job types
- Priority queues (critical, default, low)

```ruby
class UploadMediaAttachmentToS3Job < ApplicationJob
  queue_as :critical
end

class FetchWhatsappTemplatesFromMetaJob < ApplicationJob
  queue_as :low
end
```

---

#### 7. **Inefficient Soft Delete Cleanup**
**Location:** `app/events/listeners/listen_for_daily_scheduler.rb`

**Issue:**
```ruby
Message.soft_deleted.find_each do |message|
  Message::DeleteMessageService.new(message.id, ...).call
end
```

**Problem:**
- Processes one message at a time
- Each deletion is a separate transaction
- Fetches attachments individually

**Solution:**
```ruby
# Batch process
Message.soft_deleted.find_in_batches(batch_size: 100) do |messages|
  attachment_paths = messages.flat_map { |m| m.attachments.pluck(:file_name) }
  S3::DeleteFileFromS3.new(attachment_paths, S3_ATTACHMENT_BUCKET).call

  Message.where(id: messages.map(&:id)).delete_all
  Attachment.where(message_id: messages.map(&:id)).delete_all
end
```

**Estimated Improvement:** 10,000 messages: 2 hours → 10 minutes

---

#### 8. **RabbitMQ Connection Pooling Issue**
**Location:** `config/initializers/rabbitmq_connection.rb`

**Issue:**
- Creates new connection per routing key
- Stores in class variables (not thread-safe)
- No connection pooling

**Problem:**
```ruby
@@channel = nil  # Shared across threads!
```

**Solution:**
- Use connection pool gem
- Thread-local connections
- Connection reuse

---

#### 9. **Large JSONB Columns Without Indexing**
**Issue:** `messages.metadata` and `messages.campaign_info` are JSONB but not indexed

**Solution:**
```sql
-- If querying specific keys
CREATE INDEX idx_messages_metadata_retry ON messages USING GIN ((metadata->'retryConfig'));
CREATE INDEX idx_messages_campaign_info ON messages USING GIN (campaign_info);
```

---

#### 10. **Webhook Processing Bottleneck**
**Issue:** Webhooks publish to RabbitMQ but processing is synchronous

**Current Flow:**
```
Webhook → RabbitMQ → Listener (synchronous processing) → Database
```

**Problem:**
- If listener is slow, messages queue up in RabbitMQ
- No retry mechanism for failed processing
- No dead letter queue

**Solution:**
- Implement dead letter queue
- Add retry logic with exponential backoff
- Monitor queue depth

---

### Performance Recommendations

#### Immediate (High Impact, Low Effort)
1. ✅ Add missing database indexes
2. ✅ Increase Sidekiq concurrency to 10
3. ✅ Implement Redis caching for user permissions
4. ✅ Fix N+1 queries in conversation listing

#### Short-term (High Impact, Medium Effort)
5. ✅ Move external API calls to background jobs
6. ✅ Optimize conversation query (use DISTINCT ON)
7. ✅ Implement batch processing for soft delete cleanup
8. ✅ Add GIN indexes for JSONB columns

#### Long-term (Medium Impact, High Effort)
9. ⚠️ Implement connection pooling for RabbitMQ
10. ⚠️ Add read replicas for PostgreSQL
11. ⚠️ Implement CDN for S3 presigned URLs
12. ⚠️ Add full-text search index for message content

---

## Design Patterns

### 1. **Service Object Pattern** ✅ **Correctly Used**
**Location:** `app/services/*`

**Purpose:** Encapsulate business logic outside of models/controllers

**Example:**
```ruby
class WhatsappTemplateService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def send_message
    # Business logic here
  end
end
```

**Benefits:**
- Single Responsibility Principle
- Testable in isolation
- Reusable across controllers/jobs

---

### 2. **Repository Pattern** ❌ **Missing**
**Issue:** ActiveRecord models are used directly in services

**Current:**
```ruby
Message.where(tenant_id: tenant_id).order(created_at: :desc)
```

**Better:**
```ruby
class MessageRepository
  def find_by_tenant(tenant_id, order: :desc)
    Message.where(tenant_id: tenant_id).order(created_at: order)
  end
end
```

**Benefits:**
- Abstraction over data access
- Easier to test (mock repository)
- Centralized query logic

---

### 3. **Observer Pattern** ✅ **Correctly Used (via Events)**
**Location:** `app/events/publishers/*`, `app/events/listeners/*`

**Implementation:**
```ruby
# Publisher
Publishers::WhatsappMessageCreatedV2Publisher.call(message)

# Listener
Listeners::ListenForWhatsappWebhook.listen
```

**Benefits:**
- Decoupled services
- Async processing
- Event-driven architecture

---

### 4. **Strategy Pattern** ⚠️ **Partially Used**
**Location:** `app/services/facebook/*` vs `app/services/interakt/*`

**Issue:** No abstraction for switching between providers

**Current:**
```ruby
if connected_account.use_interakt?
  Interakt::Message.new(connected_account).send_template_message(...)
else
  Facebook::Message.new(connected_account).send_template_message(...)
end
```

**Better:**
```ruby
class WhatsappProvider
  def self.for(connected_account)
    if connected_account.use_interakt?
      InteraktProvider.new(connected_account)
    else
      FacebookProvider.new(connected_account)
    end
  end
end

# Usage
provider = WhatsappProvider.for(connected_account)
provider.send_template_message(...)
```

---

### 5. **Factory Pattern** ❌ **Missing**
**Issue:** Object creation scattered across codebase

**Example:** Message creation logic duplicated in multiple places

**Better:**
```ruby
class MessageFactory
  def self.create_whatsapp_message(params)
    Message.new(
      medium: 'whatsapp',
      message_type: 'whatsapp_business',
      direction: params[:direction],
      # ... standardized creation logic
    )
  end
end
```

---

### 6. **Decorator Pattern** ❌ **Missing**
**Use Case:** Adding functionality to messages (e.g., encryption, formatting)

**Example:**
```ruby
class EncryptedMessage < SimpleDelegator
  def content
    decrypt(super)
  end
end

message = EncryptedMessage.new(Message.find(id))
```

---

### 7. **Command Pattern** ✅ **Used in Background Jobs**
**Location:** `app/jobs/*`

**Example:**
```ruby
class UploadMediaAttachmentToS3Job < ApplicationJob
  def perform(connected_account_id, message_id, media_id)
    # Command execution
  end
end
```

---

### 8. **Adapter Pattern** ✅ **Used for External APIs**
**Location:** `app/services/facebook/request.rb`, `app/services/interakt/request.rb`

**Purpose:** Adapt external API responses to internal format

**Example:**
```ruby
class Facebook::Response
  def status_200?
    @status_code.to_i == 200
  end
end
```

---

### 9. **Template Method Pattern** ⚠️ **Misused**
**Location:** `app/services/message/message_service.rb`

**Issue:** Complex conditional logic instead of inheritance

**Current:**
```ruby
def call
  if @params[:message_type] == WHATSAPP_BUSINESS
    # WhatsApp-specific logic
  elsif @params[:message_type] == SMS
    # SMS-specific logic
  end
end
```

**Better:**
```ruby
class MessageService
  def call
    validate
    create_message
    send_message
    publish_events
  end

  def send_message
    raise NotImplementedError
  end
end

class WhatsappMessageService < MessageService
  def send_message
    # WhatsApp-specific implementation
  end
end
```

---

### 10. **Concern Pattern** ✅ **Correctly Used**
**Location:** `app/models/concerns/soft_deletable.rb`

**Example:**
```ruby
module SoftDeletable
  extend ActiveSupport::Concern

  included do
    scope :soft_deleted, -> { where.not(deleted_at: nil) }
  end

  def soft_delete
    update(deleted_at: Time.current)
  end
end
```

---

## Improvements Needed

### Critical Issues

#### 1. **Security: Unencrypted Sensitive Data**
**Issue:** Access tokens stored in database but encryption keys in environment variables

**Risk:** If environment variables leak, all tokens compromised

**Solution:**
- Use AWS Secrets Manager or HashiCorp Vault
- Rotate encryption keys regularly
- Implement key versioning

---

#### 2. **No Rate Limiting**
**Issue:** No rate limiting on API endpoints or external API calls

**Risk:**
- DDoS vulnerability
- WhatsApp API rate limit violations
- Cost overruns

**Solution:**
```ruby
# Gemfile
gem 'rack-attack'

# config/initializers/rack_attack.rb
Rack::Attack.throttle('api/ip', limit: 100, period: 1.minute) do |req|
  req.ip if req.path.start_with?('/v1/')
end
```

---

#### 3. **No Circuit Breaker for External APIs**
**Issue:** If Facebook/Interakt API is down, requests hang indefinitely

**Solution:**
```ruby
gem 'circuitbox'

circuit = Circuitbox.circuit(:facebook_api, {
  exceptions: [Net::HTTPError],
  timeout_seconds: 5,
  sleep_window: 30
})

circuit.run do
  Facebook::Request.process(...)
end
```

---

#### 4. **Missing Idempotency Keys**
**Issue:** Duplicate message sends if request retried

**Solution:**
- Add `idempotency_key` column to messages
- Check for duplicates before sending

---

#### 5. **No Monitoring/Alerting**
**Issue:** Prometheus client added but no dashboards/alerts configured

**Solution:**
- Set up Grafana dashboards
- Configure alerts for:
  - High error rates
  - Slow response times
  - Queue depth
  - Failed jobs

---

#### 6. **Inconsistent Error Handling**
**Issue:** Some errors logged, some raised, some swallowed

**Example:**
```ruby
rescue StandardError => e
  Rails.logger.error "Error: #{e.message}"
  # No re-raise, no notification
end
```

**Solution:**
- Standardize error handling
- Use error tracking service (Sentry, Rollbar)
- Define error handling policy

---

#### 7. **No Database Connection Pooling Configuration**
**Issue:** Default connection pool size may be insufficient

**Solution:**
```yaml
# config/database.yml
production:
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 25 } %>
  timeout: 5000
```

---

#### 8. **Missing API Versioning Strategy**
**Issue:** Only v1 exists, no deprecation plan

**Solution:**
- Document API versioning policy
- Add deprecation headers
- Plan v2 migration

---

#### 9. **No Request Timeout Configuration**
**Issue:** External API calls can hang indefinitely

**Solution:**
```ruby
# config/initializers/timeout.rb
Rack::Timeout.timeout = 30  # seconds
```

---

#### 10. **Insufficient Testing**
**Issue:** No integration tests for critical flows

**Needed:**
- End-to-end tests for message sending
- Webhook processing tests
- Failure scenario tests

---

## Setup & Deployment

### Local Development Setup

```bash
# 1. Install dependencies
bundle install

# 2. Setup database
rails db:create
rails db:migrate
rails db:seed

# 3. Setup environment variables
cp .env.example .env
# Edit .env with your credentials

# 4. Start Redis
redis-server

# 5. Start RabbitMQ
rabbitmq-server

# 6. Start Sidekiq
bundle exec sidekiq

# 7. Start Rails server
rails server
```

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/sd_message_development

# Redis
REDIS_URL=redis://localhost:6379/2

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VIRTUAL_HOST=/

# AWS S3
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret
S3_ATTACHMENT_BUCKET=your-bucket-name

# Encryption
MESSAGE_CREDENTIAL_ENCRYPTION_SECRET=32_byte_secret_key
MESSAGE_CREDENTIAL_ENCRYPTION_IV=16_byte_iv

# Facebook
FACEBOOK_CLIENT_ID=your_client_id
FACEBOOK_CLIENT_SECRET=your_client_secret

# Interakt
INTERAKT_PARTNER_TOKEN=your_partner_token
INTERAKT_SOLUTION_ID=your_solution_id

# Microservices
SERVICE_IAM=http://sd-iam
SERVICE_SALES=http://sd-sales
SERVICE_DEAL=http://sd-deal
SERVICE_CONFIG=http://sd-config
SERVICE_WHATSAPP_CHATBOT=http://sd-whatsapp-chatbot
```

### Docker Deployment

```bash
# Build images
docker build -f Dockerfile -t sd-message:latest .
docker build -f Sidekiq-Dockerfile -t sd-message-sidekiq:latest .

# Run with docker-compose
docker-compose up -d
```

### Kubernetes Deployment

Helm charts available in `deploy/` directory:
- `deploy/qa/` - QA environment
- `deploy/stage/` - Staging environment
- `deploy/prod/` - Production environment

```bash
# Deploy to QA
helm upgrade --install sd-message ./deploy/qa -n qa

# Deploy to Production
helm upgrade --install sd-message ./deploy/prod -n production
```

### CI/CD Pipeline

**Jenkinsfile** defines the pipeline:
1. Build Docker images
2. Run tests
3. Push to registry
4. Deploy to environment

---

## Conclusion

This README provides a comprehensive overview of the SD-Message service. For specific implementation details, refer to the codebase and inline documentation.

**Key Takeaways:**
- Multi-channel messaging service with WhatsApp Business focus
- Event-driven architecture using RabbitMQ
- Multi-tenant with permission-based access control
- Several performance bottlenecks identified with solutions
- Design patterns mostly well-applied, some improvements needed
- Critical security and reliability improvements recommended

**Next Steps:**
1. Address critical performance issues (indexes, caching)
2. Implement rate limiting and circuit breakers
3. Add comprehensive monitoring and alerting
4. Improve test coverage
5. Document API endpoints (Swagger/OpenAPI)
