# frozen_string_literal: true

namespace :conversation do
  desc 'Update nil conversation and sub-conversation statuses to IN_PROGRESS'

  task update_nil_statuses: :environment do
    puts "\n Starting WhatsApp message status update task..."
    batch_size = ENV.fetch('BATCH_SIZE', 1000).to_i

    begin
      total_conversations = Conversation.where(status: nil).count
      updated_conversations = 0

      puts "\n Processing Conversations (#{total_conversations} total with nil status)..."

      Conversation.where(status: nil).find_each(batch_size: batch_size) do |conversation|
        conversation.update_columns(status: IN_PROGRESS)
        updated_conversations += 1
      end

      total_sub_conversations = SubConversation.where(status: nil).count
      updated_sub_conversations = 0

      puts "\nProcessing SubConversations (#{total_sub_conversations} total with nil status)..."

      SubConversation.where(status: nil).find_each(batch_size: batch_size) do |sub_conversation|
        sub_conversation.update_columns(status: IN_PROGRESS)
        updated_sub_conversations += 1
      end
      
      puts "\n Status Update Summary"
      puts "--------------------------------------------"
      puts "Conversations:"
      puts "   Total with nil status      : #{total_conversations}"
      puts "   Successfully updated       : #{updated_conversations}"
      puts ""
      puts "SubConversations:"
      puts "   Total with nil status      : #{total_sub_conversations}"
      puts "   Successfully updated       : #{updated_sub_conversations}"
      puts "--------------------------------------------"

    rescue => e
      puts "\n Error occurred while updating statuses: #{e.message}"
    end
  end
end

# Run with:
# RAILS_ENV=production bundle exec rake conversation:update_nil_statuses
# Optionally set batch size (default 1000):
# RAILS_ENV=production BATCH_SIZE=2000 bundle exec rake conversation:update_nil_statuses
