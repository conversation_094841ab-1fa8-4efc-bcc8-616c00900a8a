# frozen_string_literal: true

namespace :whatsapp_message do
  desc 'Migrate message content to component-wise content structure'
  task :migrate_message_content, [:from_tenant_id, :to_tenant_id] => :environment do |task, args|
    from_tenant_id = args[:from_tenant_id]
    to_tenant_id = args[:to_tenant_id]

    whatsapp_messages = Message.where(
      "component_wise_content = '[]'::jsonb AND message_type = ?", 
      WHATSAPP_BUSINESS
    )

    if from_tenant_id.present? && to_tenant_id.present?
      whatsapp_messages = whatsapp_messages.where(tenant_id: from_tenant_id..to_tenant_id)
    end

    migrated_count = 0
    whatsapp_messages.find_each do |message|
      begin
        next if message.content.blank?

        message.update_column(:component_wise_content, [
          {
            position: 0,
            type: 'BODY',
            format: 'TEXT',
            text: message.content,
            value: nil
          }
        ])

        migrated_count += 1
      rescue => e
        Rails.logger.error "Error during migration: #{e.message}"
      end
    end
    
    puts "#{migrated_count} messages migrated successfully."
  end
end

# Run task with following command:
# RAILS_ENV=<ENV> bundle exec rake "whatsapp_message:migrate_message_content[1]"
# RAILS_ENV=<ENV> bundle exec rake "whatsapp_message:migrate_message_content"
