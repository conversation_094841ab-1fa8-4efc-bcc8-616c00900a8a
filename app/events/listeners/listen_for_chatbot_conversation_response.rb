# frozen_string_literal: true

class Listeners::ListenForChatbotConversationResponse
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CHATBOT_EXCHANGE, CHATBOT_CONVERSATION_RESPONSE_EVENT, CHATBOT_CONVERSATION_RESPONSE_QUEUE) do |payload|
      Rails.logger.info "Received message for #{CHATBOT_CONVERSATION_RESPONSE_EVENT} #{payload}"
      new(JSON.parse(payload)).process
    end
  end

  def initialize(payload)
    @payload = payload
    @chatbot_conversation_id = payload['chatbotConversationId']
  end

  def process
    Rails.logger.info "ListenForChatbotConversationResponse | Processing chatbot conversation response for chatbot_conversation_id: #{@chatbot_conversation_id}"

    unless @chatbot_conversation_id.present?
      Rails.logger.error "ListenForChatbotConversationResponse | Missing chatbot_conversation_id in event data"
      return
    end

    ProcessChatbotResponseJob.perform_later(@payload)

    Rails.logger.info "ListenForChatbotConversationResponse | Scheduled ProcessChatbotResponseJob for chatbot_conversation_id: #{@chatbot_conversation_id}"
  rescue StandardError => e
    Rails.logger.error "ListenForChatbotConversationResponse | Error processing chatbot conversation response: #{e.message}"
  end
end
