# frozen_string_literal: true

class Listeners::ListenForChatbotStatusUpdated
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CHATBOT_EXCHANGE, CHATBOT_STATUS_UPDATED_EVENT, CHATBOT_STATUS_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{CHATBOT_STATUS_UPDATED_EVENT} #{payload}"
      new(JSON.parse(payload)).process
    end
  end

  def initialize(payload)
    @payload = payload
    @status = payload['status']
    @connected_account_data = payload['connectedAccount']
  end

  def process
    return unless valid_payload?

    connected_account = find_connected_account
    return unless connected_account

    update_chatbot_configuration(connected_account)
  end

  private

  attr_reader :payload, :status, :connected_account_data

  def valid_payload?
    return false unless status.present? && connected_account_data.present?
    return false unless connected_account_data['id'].present?
    return false unless %w[ACTIVE DRAFT INACTIVE].include?(status)

    true
  end

  def find_connected_account
    ConnectedAccount.find_by(id: connected_account_data['id'])
  end

  def update_chatbot_configuration(connected_account)
    is_configured = status == 'ACTIVE'
    
    if connected_account.update(is_chatbot_configured: is_configured)
      Rails.logger.info "Updated chatbot configuration for connected account #{connected_account.id} to #{is_configured}"
    else
      Rails.logger.error "Failed to update chatbot configuration for connected account #{connected_account.id}: #{connected_account.errors.full_messages.join(', ')}"
    end
  end
end
