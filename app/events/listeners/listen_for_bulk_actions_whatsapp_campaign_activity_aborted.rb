class Listeners::ListenForBulkActionsWhatsappCampaignActivityAborted
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(BULK_ACTIONS_EXCHANGE, BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_EVENT, BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "#{BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_EVENT} payload: #{payload}"
      
      Message.where(id: payload['messageIds'], tenant_id: payload['tenantId']).update_all(status: 'failed', status_message: "Campaign aborted", failed_at: DateTime.now.utc)
      Rails.logger.info "Tenant: #{payload['tenantId']}, campaign_id: #{payload['campaignId']} activity_id: #{payload['activityId']} Aborted sending messages"
    end
  end
end
