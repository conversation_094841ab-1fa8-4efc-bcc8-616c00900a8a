# frozen_string_literal: true

class Listeners::ListenForChatbotMediaDeleted
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CHATBOT_EXCHANGE, CHATBOT_MEDIA_DELETED_EVENT, CHATBOT_MEDIA_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{CHATBOT_MEDIA_DELETED_EVENT} #{payload}"
      new(JSON.parse(payload)).process
    end
  end

  def initialize(payload)
    @payload = payload
    @chatbot_id = payload['chatbotId']
    @tenant_id = payload['tenantId']
  end

  def process
    return unless valid_payload?

    media_to_delete = ChatbotMedia.where(chatbot_id: @chatbot_id, tenant_id: @tenant_id).where(id: @payload['mediaIds'])

    if media_to_delete.blank?
      Rails.logger.info "No media to delete for chatbot #{@chatbot_id} and tenant #{@tenant_id}"
      return
    end

    file_names = media_to_delete.map { |media| media.file_name }
    S3::DeleteFileFromS3.new(file_names, S3_ATTACHMENT_BUCKET).call
    media_to_delete.delete_all
  end

  private

  def valid_payload?
    return false unless @chatbot_id.present? && @tenant_id.present?
    return false unless @payload['mediaIds'].present?

    true
  end
end
