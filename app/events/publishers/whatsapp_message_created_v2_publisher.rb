# frozen_string_literal: true

class Publishers::WhatsappMessageCreatedV2Publisher < ApplicationService
  def self.call(message)
    Rails.logger.info "Message Service: Publishers::WhatsappMessageCreatedV2 called"
  
    data = MessageSerializer::Details.serialize_whatsapp_message_created_v2_payload(message)

    event = Event::WhatsappMessageCreatedV2.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::WhatsappMessageCreatedV2 data #{event.to_json}"
  end
end
