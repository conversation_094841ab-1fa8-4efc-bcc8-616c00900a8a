# frozen_string_literal: true

class Publishers::ChatbotConversationCompletedPublisher < ApplicationService
  def self.call(conversation)
    Rails.logger.info "Publishers::ChatbotConversationCompletedPublisher called for conversation #{conversation.id}"

    event = Event::ChatbotConversationCompleted.new(
      messageConversationId: conversation.id,
      chatbotConversationId: conversation.chatbot_conversation_id
    )
    PublishEvent.call(event)
    Rails.logger.info "Event::ChatbotConversationCompleted published for conversation #{conversation.id}"
  end
end
