# frozen_string_literal: true

class Publishers::ChatbotUserResponsePublisher < ApplicationService
  def self.call(message:, chatbot_conversation_id: nil, completed: false, is_media_message: false)
    Rails.logger.info "Message Service: Publishers::ChatbotUserResponsePublisher called with chatbot_conversation_id: #{chatbot_conversation_id}, completed: #{completed}, is_media_message: #{is_media_message}"
    new(message: message, chatbot_conversation_id: chatbot_conversation_id, completed: completed, is_media_message: is_media_message).call
  end

  def initialize(message:, chatbot_conversation_id: nil, completed: false, is_media_message: false)
    @message = message
    @chatbot_conversation_id = chatbot_conversation_id
    @completed = completed
    @is_media_message = is_media_message
  end

  def call
    Rails.logger.info "ChatbotUserResponsePublisher | Publishing chatbot user response event for chatbot_conversation_id: #{@chatbot_conversation_id}"

    event = Event::ChatbotUserResponse.new(
      message: @message,
      chatbot_conversation_id: @chatbot_conversation_id,
      completed: @completed,
      is_media_message: @is_media_message
    )

    PublishEvent.new(event, MESSAGE_EXCHANGE).call

    Rails.logger.info "ChatbotUserResponsePublisher | Successfully published chatbot user response event"
  rescue StandardError => e
    Rails.logger.error "ChatbotUserResponsePublisher | Error publishing chatbot user response event: #{e.message}"
  end
end
