# frozen_string_literal: true

module V1
  class AgentsController < ApplicationController
    skip_before_action :authenticate, only: [:delete_all]

    def index
      render json: AgentsSerializer.new.serialize(AgentsService.new(underscorize_params).get_all)
    end

    def save
      AgentsService.new(underscorize_params).save
      render json: ''
    end

    def delete_all
      AgentsService.new(underscorize_params).delete_all
      render json: '', status: :ok
    end

    private

    def permit_params
      case action_name
      when 'index'
        params.permit(:connected_account_id, :entity_type)
      when 'save'
        params.permit(:connected_account_id, :entity_type, agents: [:id, :name])
      when 'delete_all'
        params.permit(:id)
      end
    end
  end
end
