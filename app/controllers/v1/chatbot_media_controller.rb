# frozen_string_literal: true

module V1
  class ChatbotMediaController < ApplicationController
    def upload
      render json: ChatbotMediaSerializer.new(ChatbotMediaService.new(underscorize_params).upload).serialize, status: :created
    end

    def list
      render json: ChatbotMediaService.new(underscorize_params).list
    end

    private

    def permit_params
      case action_name
      when 'upload'
        params.permit(:connected_account_id, :chatbotId, :mediaFile, :mediaType)
      when 'list'
        params.permit(:connected_account_id, :chatbotId)
      end
    end
  end
end
