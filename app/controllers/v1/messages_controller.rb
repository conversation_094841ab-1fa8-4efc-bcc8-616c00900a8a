module V1
  class MessagesController < ApplicationController
    before_action :set_related_to, only: [:create, :update]

    def create
      id = Message::MessageService.call(underscorize_params, nil, nil, nil, false, { check_usage_limit: true })
      render json: { id: id }, status: :created
    end

    def show
      message = Message::GetMessageDetailsService.call(params[:id])
      render json: MessageSerializer::Details.serialize(message, nil, params[:relatedTo])
    end

    def search
      messages = Message::GetMessagesService.call(underscorize_params)
      related_to_filters = underscorize_params.dig(:json_rule, :rules)&.select { |rule| rule[:id] == 'related_to' }
      related_entities_for_masking =
        if related_to_filters.present?
          related_to_filters.map { |rule| rule.dig('value', 'entity') }.compact.reject(&:blank?)
        else
          []
        end

      render json: MessageSerializer::SearchResult.serialize(messages, related_entities_for_masking)
    end

    def destroy
      Message::DeleteMessageService.new(underscorize_params[:id]).soft_delete
      head :ok
    end

    def sync
      res = Message::SyncService.call(underscorize_params, { check_usage_limit: true })
      render json: res, status: :created
    end

    def update
      message = Message::PatchUpdateMessageService.call(underscorize_params)
      render json: { id: message.id }
    end

    def session_message
      render json: { id: Message::SessionMessage.new(underscorize_params.merge(is_manual: true)).send_text }, status: :created
    end

    def media_session_message
      render json: { id: Message::SessionMessage.new(underscorize_params.merge(is_manual: true)).send_media }, status: :created
    end

    def mark_as_read
      Message::MarkAsRead.call(underscorize_params[:id])
      head :ok
    end

    def show_conversation_message
      message, related_to = Message::GetConversationMessageDetailsService.call(underscorize_params)
      render json: MessageSerializer::Details.serialize(message, nil, params[:entityType], related_to)
    end

    def show_whatsapp_message
      message = Message::GetMessageDetailsService.call(underscorize_params[:id], skip_permission_check: true)
      render json: JSON(MessageSerializer::Details.serialize_whatsapp_message(message).target!)
    end

    private

    def permit_params
      case action_name
      when 'create', 'update'
        params.permit(:id, :content, :direction, :medium, :ownerId, :sentAt, :messageType,
                      :deliveredAt, :readAt, :senderNumber, :recipientNumber, :status,
                      {
                        recipients: [ :entity, :id, :name, :phoneNumber, :_destroy]
                      },
                      {
                        relatedTo: [ :entity, :id, :name, :phoneNumber, :_destroy]
                      },
                      {
                        attachments: [:id, :data, :url, :fileName, :_destroy]
                      }
                     )
      when 'sync'
        params.permit(:content, :direction, :medium, :ownerId, :sentAt, :remoteId, :messageType,
                      :deliveredAt, :readAt, :senderNumber, :recipientNumber, :status,
                      {
                        attachments: [:id, :data, :url, :fileName, :_destroy]
                      }
                     )
      when 'session_message'
        params.permit(:id, :entityType, :entityId, :phoneId, :conversationId, :messageType, :messageBody)
      when 'media_session_message'
        params.permit(:id, :entityType, :entityId, :phoneId, :conversationId, :messageType, {
          media: [:type, :caption, :file]
        })
      when 'show_conversation_message'
        params.permit(:message_id, :entityType, :entityId, :conversation_id)
      when 'show_whatsapp_message'
        params.permit(:id)
      else
        params.permit!
      end
    end

    def set_related_to
      params[:relatedTo] ||= []
      params[:recipients] ||= []
      params[:relatedTo] = params[:relatedTo].union(params[:recipients]).uniq { |recipient| [recipient['entity'], recipient['id']] }
      params[:recipients] = params[:relatedTo].reject{ |en| en[:entity] == LOOKUP_DEAL }
    end
  end
end
