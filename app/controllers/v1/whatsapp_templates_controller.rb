# frozen_string_literal: true

module V1
  class WhatsappTemplatesController < ApplicationController
    skip_before_action :authenticate, only: [:delete_all_templates]

    def show
      render json: WhatsappTemplatesSerializer.new.serialize_single(WhatsappTemplateService.new(underscorize_params).get)
    end

    def sync
      WhatsappTemplateService.new(underscorize_params).sync_templates
      render json: '', status: :ok
    end

    def search
      render json: WhatsappTemplatesSerializer.new.serialize(WhatsappTemplateService.new(underscorize_params).search)
    end

    def create
      render json: WhatsappTemplatesSerializer.new.serialize_single(WhatsappTemplateService.new(underscorize_params).create)
    end

    def create_and_submit
      render json: WhatsappTemplatesSerializer.new.serialize_single(WhatsappTemplateService.new(underscorize_params).create_and_submit)
    end

    def update
      render json: WhatsappTemplatesSerializer.new.serialize_single(WhatsappTemplateService.new(underscorize_params).update)
    end

    def deactivate
      WhatsappTemplateService.new(underscorize_params).deactivate
      render json: '', status: :ok
    end

    def variables
      render json: EntityVariables.new(underscorize_params[:entity]).get
    end

    def lookup
      render json: WhatsappTemplatesSerializer.new.serialize_lookup(WhatsappTemplateService.new(underscorize_params).lookup)
    end

    def preview
      render json: WhatsappTemplatesSerializer.new.serialize_single(WhatsappTemplateService.new(underscorize_params).preview)
    end

    def send_message
      WhatsappTemplateService.new(underscorize_params.merge(is_manual: true)).send_message
      render json: '', status: :ok
    end

    def sync_status
      WhatsappTemplateService.new(underscorize_params).sync_status
      render json: '', status: :ok
    end

    def send_bulk_message
      message_id = WhatsappTemplateService.new(underscorize_params(skip_nested_keys: true)).send_bulk_message
      render json: { id: message_id }, status: :ok
    end

    def retry_message_delivery
      result = WhatsappTemplateService.new(underscorize_params(skip_nested_keys: true)).retry_message_delivery
      render json: { id: result }, status: :ok
    end

    def delete_all_templates
      WhatsappTemplateService.new({ connected_account_id: underscorize_params[:id] }).delete_all_templates
      render json: '', status: :ok
    end

    private

    def permit_params
      case action_name
      when 'show'
        params.permit(:id)
      when 'search'
        params.permit(:page, :size, :sort, jsonRule: {})
      when 'create', 'create_and_submit'
        params.permit(:name, :entityType, :category, :language, connectedAccount: [:id, :name]).merge(components: params['components'].map { |component| component.slice(*%i[type format text value position mediaType]).permit! })
      when 'update'
        params.permit(:id, :name, components: [:id, :type, :format, :text, :value, :position, :mediaType])
      when 'deactivate'
        params.permit(:id)
      when 'variables'
        params.permit(:entity)
      when 'lookup'
        params.permit(:connectedAccountId, :entityType, :q)
      when 'preview'
        params.permit(:id, :entity_type, :entity_id)
      when 'send_message'
        params.permit(:id, :entityType, :phoneId, :entityId, :conversationId, :dynamicTemplateMediaId, :templateEntity, :templateEntityId)
      when 'sync'
        params.permit(:connectedAccountId, entityType: [])
      when 'sync_status'
        params.permit(:id)
      when 'send_bulk_message'
        params.permit(:id, :entityType, :entityId, :bulkJob, :dynamicTemplateMediaId, :campaignId, :activityId, :templateEntity, :templateEntityId, phoneNumber: {}, entityData: {}, retryConfig: [:noOfTimes])
      when 'retry_message_delivery'
        params.permit(:message_id, :whatsappTemplateId, :entityType, :entityId, :bulkJob, :dynamicTemplateMediaId, :campaignId, :activityId, :templateEntity, :templateEntityId, phoneNumber: {}, entityData: {}, retryConfig: [:noOfTimes, :timesRetried])
      when 'delete_all_templates'
        params.permit(:id)
      end
    end
  end
end
