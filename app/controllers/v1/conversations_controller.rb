# frozen_string_literal: true

module V1
  class ConversationsController < ApplicationController
    skip_before_action :authenticate, only: [:delete_all]

    def search
      conversation_messages, related_to, entity_permissions = ConversationService.new(underscorize_params).fetch_messages_and_related_to

      related_to_filters = underscorize_params.dig(:json_rule, :rules)&.select { |rule| rule[:id] == 'related_to' }
      related_entities_for_masking =
        if related_to_filters.present?
          related_to_filters.map { |rule| rule.dig('value', 'entity') }.compact.reject(&:blank?)
        else
          []
        end

      render json: MessageSerializer::SearchResult.serialize(conversation_messages, related_entities_for_masking, related_to, entity_permissions)
    end

    def index
      conversations = ConversationService.new(underscorize_params).fetch_conversations
      render json: ConversationSerializer::SearchResult.serialize(conversations)
    end

    def destroy
      ConversationService.new(permit_params).delete_conversation
      head :ok
    end

    def permissions
      permissions = ConversationService.new(underscorize_params).fetch_conversation_permissions
      render json: permissions
    end

    def create
      result = ConversationService.new(underscorize_params).create_or_fetch_conversation
      render json: result
    end

    def get_by_entity
      result = ConversationService.new(underscorize_params).get_conversation_by_entity
      render json: ConversationSerializer::ByEntity.serialize(result)
    end

    def mark_as_completed
      result = ConversationService.new(underscorize_params).mark_as_completed
      render json: result
    end

    def mark_as_unread
      result = ConversationService.new(underscorize_params).mark_as_unread
      render json: result
    end

    def mark_as_read
      result = ConversationService.new(underscorize_params).mark_as_read
      render json: result
    end

    def delete_all
      ConversationService.new(underscorize_params).delete_all
      head :ok
    end

    private

    def permit_params
      case action_name
      when 'destroy'
        params.permit(:id)
      when 'permissions'
        params.permit(:id, :entityType, :entityId)
      when 'create'
        params.permit(:lastConversationId, :connectedAccountId, :entityId, :entityType)
      when 'get_by_entity'
        params.permit(:entityId, :entityType, :entityName, :phoneId)
      when 'delete_all'
        params.permit(:id)
      else
        params.permit(:id, :page, :size, jsonRule: {})
      end
    end
  end
end
