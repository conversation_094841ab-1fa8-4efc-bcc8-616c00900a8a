# frozen_string_literal: true

module V1
  class WhatsappCreditHistoriesController < ApplicationController
    skip_before_action :authenticate, only: [:delete_all]

    def delete_all
      WhatsappCreditHistory.where(connected_account_id: underscorize_params[:id]).update_all(connected_account_id: nil)
      render json: '', status: :ok
    end

    private

    def permit_params
      case action_name
      when 'delete_all'
        params.permit(:id)
      end
    end
  end
end
