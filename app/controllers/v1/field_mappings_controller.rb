# frozen_string_literal: true

module V1
  class FieldMappingsController < ApplicationController
    skip_before_action :authenticate, only: [:delete_all]

    def index
      render json: FieldMappingSerializer.new.serialize(FieldMappingsService.new(underscorize_params).get)
    end

    def create
      render json: FieldMappingSerializer.new.serialize(FieldMappingsService.new(underscorize_params).create_or_update)
    end

    def delete_all
      FieldMappingsService.new({ id: underscorize_params[:id] }).delete_all
      render json: '', status: :ok
    end

    private

    def permit_params
      case action_name
      when 'index'
        params.permit(:connected_account_id, :entity_type)
      when 'create'
        params.permit(:connected_account_id, :entity_type, :campaign, :source, :subSource, :utmSource, :utmCampaign, :utmMedium, :utmContent, :utmTerm)
      when 'delete_all'
        params.permit(:id)
      end
    end
  end
end
