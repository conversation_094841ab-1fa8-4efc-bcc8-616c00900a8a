module ExceptionHandler
  extend ActiveSupport::Concern
  class AuthenticationError < StandardError; end
  class InternalServerError < StandardError; end
  class InvalidDataError < StandardError; end
  class NotFound < StandardError; end
  class MessageNotAllowedError < StandardError; end
  class DeleteNotAllowedError < StandardError; end
  class AttachmentUrlDownloadError < StandardError; end
  class InvalidLeadError < StandardError; end
  class InvalidProfileError < StandardError; end
  class InvalidLeadPhoneError < StandardError; end
  class PhoneNumbersNotMatchedError < StandardError; end
  class RecipientNotPresentError < StandardError; end
  class RemoveRecipientNotAllowedError < StandardError; end
  class ThirdPartyAPIError < StandardError; end
  class AccountNotConnectedError < StandardError; end
  class InsufficientWhatsappCreditsBalance < StandardError; end
  class ConversationNotFoundError < StandardError; end
  class InactiveWhatsappTemplate < StandardError; end
  class AccountNotActiveError < StandardError; end
  class WhatsappTemplatesSyncInProgress < StandardError; end
  class ChatBotInProgress < StandardError; end
  class EntityNotFound < StandardError; end

  included do
    # Define custom handlers
    rescue_from ExceptionHandler::AuthenticationError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.unauthorized,
          message: message
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InternalServerError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.internal_error,
          message: message
        },
        :internal_server_error
      )
    end

    rescue_from ExceptionHandler::InvalidDataError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.invalid_data,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::NotFound do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.not_found,
          message: message
        },
        :not_found
      )
    end

    rescue_from ExceptionHandler::EntityNotFound do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.entity_not_found,
          message: message
        },
        :not_found
      )
    end

    rescue_from ExceptionHandler::MessageNotAllowedError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.message_not_allowed,
          message: message
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::DeleteNotAllowedError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.delete_not_allowed,
          message: message
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::AttachmentUrlDownloadError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.attachment_url_download_failed,
          message: message
        },
        :forbidden
      )
    end

    rescue_from ActiveRecord::RecordNotFound do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.not_found,
          message: message
        },
        :not_found
      )
    end

    rescue_from ExceptionHandler::InvalidLeadError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.invalid_lead
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::RecipientNotPresentError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.recipient_not_present
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::RemoveRecipientNotAllowedError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.remove_recipient_not_allowed
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::InvalidProfileError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.invalid_profile
        },
        :not_found
      )
    end

    rescue_from ExceptionHandler::InvalidLeadPhoneError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.invalid_lead_phone
        },
        :unprocessable_entity
      )
    end


    rescue_from ExceptionHandler::PhoneNumbersNotMatchedError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.phone_numbers_not_match
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::ThirdPartyAPIError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::AccountNotConnectedError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::AccountNotActiveError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::InsufficientWhatsappCreditsBalance do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::ConversationNotFoundError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::InactiveWhatsappTemplate do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end
    
    rescue_from ExceptionHandler::WhatsappTemplatesSyncInProgress do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::ChatBotInProgress do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode,
          message: message
        },
        :conflict
      )
    end
  end

  def json_response(object, status = :ok)
    render json: object, status: status
  end
end

