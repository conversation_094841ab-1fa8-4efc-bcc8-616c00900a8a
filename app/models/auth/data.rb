class Auth::Data
  include ActiveModel::Model

  attr_accessor(
    :expires_in,
    :access_token,
    :expiry,
    :token_type,
    :user_id,
    :username,
    :tenant_id,
    :permissions,
    :meta
  )
  validates :expires_in, :expiry, :token_type, :user_id, :tenant_id, :permissions, presence: true

  def initialize options = {}
    UnderscorizeKeys.do(options)
    valid_options = permit(options)
    super(valid_options)
    permissions = valid_options['permissions'].collect{|permission| Auth::Permission.new(permission) } unless valid_options['permissions'].nil?
    @permissions = permissions
  end

  def as_json(options = {})
    super(options.merge(except: ["validation_context", "errors"]))
  end

  def to_json(options = {})
    super(options.merge(except: ["validation_context", "errors"]))
  end

  def can_access? permission_name, action_name = 'read'
    return false unless permission_name
    return true if permissions.select{ |permission| permission.name == permission_name && permission.can_access?(action_name)}.present?
    return false
  end

  def user_id
    @user_id = @user_id.to_i if @user_id.present?
  end

  def tenant_id
    @tenant_id = @tenant_id.to_i if @tenant_id.present?
  end

  private

    def permit options
      options = ActionController::Parameters.new(options)
      options.permit(
        :expires_in,
        :expiry,
        :token_type,
        :access_token,
        :user_id,
        :username,
        :tenant_id,
        meta: {},
        permissions: [
          :id,
          :name,
          :description,
          :limits,
          :units,
          action: [
            :read,
            :write,
            :update,
            :delete,
            :delete_all,
            :email,
            :call,
            :sms,
            :task,
            :note,
            :read_all,
            :update_all
          ]
        ]
      ).to_hash
    end

end
