# frozen_string_literal: true

class ChatbotMedia < ApplicationRecord
  validates :file_name, :file_size, :file_type, :tenant_id, presence: true

  def extract_file_name
    regex = Regexp.new(/[^>]*\/[^>]*\/([^>]+)_[^>]*\.([^>]*)/)
    file_name.scan(regex).flatten.join(".") rescue file_name
  end

  def media_url(send_content_type = false)
    content_type = send_content_type ? file_type : nil
    S3::GetPresignedUrl.new(file_name, file_name.split('/', 3)[-1], S3_ATTACHMENT_BUCKET, content_type).call
  end
end
