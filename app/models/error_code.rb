class ErrorCode
  SERVICE_CODE = '022'

  def self.invalid_token
    SERVICE_CODE + '001'
  end

  def self.unauthorized
    SERVICE_CODE + '002'
  end

  def self.invalid_data
    SERVICE_CODE + '003'
  end

  def self.internal_error
    SERVICE_CODE + '004'
  end

  def self.invalid_user_details
    SERVICE_CODE + '005'
  end

  def self.not_found
    SERVICE_CODE + '006'
  end

  def self.message_not_allowed
    SERVICE_CODE + '007'
  end

  def self.delete_not_allowed
    SERVICE_CODE + '008'
  end

  def self.attachment_url_download_failed
    SERVICE_CODE + '009'
  end

  def self.invalid_lead
    SERVICE_CODE + '010'
  end

  def self.invalid_profile
    SERVICE_CODE + '011'
  end

  def self.invalid_lead_phone
    SERVICE_CODE + '012'
  end

  def self.phone_numbers_not_match
    SERVICE_CODE + '013'
  end

  def self.invalid
    SERVICE_CODE + '014'
  end

  def self.recipient_not_present
    SERVICE_CODE + '015'
  end

  def self.remove_recipient_not_allowed
    SERVICE_CODE + '016'
  end

  def self.third_party_api_error
    SERVICE_CODE + '017'
  end

  def self.account_not_connected
    SERVICE_CODE + '018'
  end

  def self.invalid_or_inactive_field
    SERVICE_CODE + '019'
  end

  def self.invalid_whatsapp_template
    SERVICE_CODE + '020'
  end

  def self.invalid_template_file_type
    SERVICE_CODE + '021'
  end

  def self.invalid_url_whatsapp_template_button
    SERVICE_CODE + '022'
  end

  def self.insufficient_whatsapp_credits_balance
    SERVICE_CODE + '023'
  end

  def self.credit_details_not_found
    SERVICE_CODE + '024'
  end

  def self.invalid_share_rule
    SERVICE_CODE + '025'
  end

  def self.conversation_not_found
    SERVICE_CODE + '026'
  end

  def self.invalid_phone_number
    SERVICE_CODE + '027'
  end

  def self.insufficient_whatsapp_credits_balance_for_bulk
    SERVICE_CODE + '028'
  end

  def self.inactive_whatsapp_template
    SERVICE_CODE + '029'
  end

  def self.account_not_active
    SERVICE_CODE + '030'
  end

  def self.whatsapp_template_sync_in_progress
    SERVICE_CODE + '031'
  end

  def self.entity_not_found
    SERVICE_CODE + '032'
  end

  def self.retryable_message_not_found
    SERVICE_CODE + '033'
  end
  
  def self.chatbot_in_progress
    SERVICE_CODE + '034'
  end
end
