# frozen_string_literal: true

class WhatsappTemplate < ApplicationRecord
  validates :name, :tenant_id, presence: true
  validates :name, uniqueness: { scope: [:tenant_id, :entity_type, :connected_account_id], case_sensitive: false }
  validates :whatsapp_template_namespace, uniqueness: { scope: [:tenant_id, :entity_type, :connected_account_id], case_sensitive: false }
  validates :whatsapp_template_namespace, format: { with:/[a-z0-9_]/, message: I18n.t('error.invalid_template.invalid_namespace_format') }
  validates :entity_type, inclusion: [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL]
  validates :category, inclusion: [MARKETING, UTILITY]
  validates :status, inclusion: [DRAFT_STATUS, SUBMITTING, PENDING_STATUS, APPROVED, REJECTED, FLAGGED, PAUSED, PENDING_DELETION, INACTIVE_TEMPLATE]

  has_many :components, class_name: 'WhatsappTemplateComponent'
  has_many :variable_mappings
  belongs_to :connected_account
  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User'

  accepts_nested_attributes_for :components, allow_destroy: true
  accepts_nested_attributes_for :variable_mappings, allow_destroy: true

  def self.generate_namespace(template_name)
    template_name.downcase.tr(' ', '_').scan(/[0-9a-z_]/).join
  end

  def extract_variables
    components.inject({}.with_indifferent_access) do |hash, component|
      case component.component_type
      when HEADER, BODY
        hash[component.component_type] = component.component_text&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).to_a
      when BUTTON
        if component.component_format == URL
          hash[BUTTON_URL] ||= []
          hash[BUTTON_URL] += component.component_value&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).to_a
        elsif component.component_format == COPY_CODE
          hash[BUTTON_COPY_CODE] = ['{{1}}']
        end
      end

      hash
    end.compact_blank.each { |type, variables| variables.map! { |variable| variable.tr('{}', '').to_i } }
  end
end
