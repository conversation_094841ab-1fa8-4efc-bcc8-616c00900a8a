class Conversation < ApplicationRecord
  include SoftDeletable

  has_many :conversation_look_ups, dependent: :destroy
  has_many :look_ups, through: :conversation_look_ups
  has_many :sub_conversations, dependent: :destroy

  validates :phone_number, uniqueness: { scope: %i[tenant_id connected_account_id], conditions: -> { where(deleted_at: nil) } }
  # We are adding phone number value with country code

  validates :status, inclusion: { in: [NEW, IN_PROGRESS, COMPLETED] }

  def ongoing_sub_conversation
    sub_conversations.where(status: [NEW, IN_PROGRESS]).order(created_at: :desc).first rescue nil
  end

  def is_chatbot_in_progress
    chatbot_conversation_id.present? && !chatbot_conversation_completed
  end
end
