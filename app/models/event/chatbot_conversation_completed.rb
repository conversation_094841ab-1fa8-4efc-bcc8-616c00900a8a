# frozen_string_literal: true

class Event::ChatbotConversationCompleted
  include ActiveModel::Model

  def initialize(messageConversationId:, chatbotConversationId:)
    @conversation_id = messageConversationId
    @chatbot_conversation_id = chatbotConversationId
  end

  def routing_key
    CHATBOT_CONVERSATION_COMPLETE_EVENT
  end

  def to_json(options = {})
    {
      messageConversationId: @conversation_id,
      chatbotConversationId: @chatbot_conversation_id
    }.to_json
  end
end
