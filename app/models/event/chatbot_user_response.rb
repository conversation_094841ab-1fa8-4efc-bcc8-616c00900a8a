# frozen_string_literal: true

class Event::ChatbotUserResponse
  include ActiveModel::Model

  attr_accessor :message, :chatbot_conversation_id, :completed, :is_media_message

  def initialize(message:, chatbot_conversation_id: nil, completed: false, is_media_message: false)
    @message = message
    @chatbot_conversation_id = chatbot_conversation_id
    @completed = completed
    @is_media_message = is_media_message
  end

  def routing_key
    CHATBOT_USER_RESPONSE_EVENT
  end

  def to_json(_options = {})
    {
      message: @message,
      chatbotConversationId: @chatbot_conversation_id,
      completed: @completed,
      isMedia: @is_media_message
    }.to_json
  end
end
