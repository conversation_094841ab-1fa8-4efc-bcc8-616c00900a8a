# frozen_string_literal: true

class WhatsappMessageStatusTransitionService < ApplicationService
  STATUS_SEQUENCE = %w[sending sent delivered read].freeze

  def initialize(message, payload)
    @message = message
    @payload = payload
    @new_status = payload[:value][:statuses][0][:status]
    @timestamp = Time.at(payload[:value][:statuses][0][:timestamp].to_i).utc

    if @new_status == 'failed'
      @error_data = {
        code: payload[:value][:statuses][0][:errors][0][:code],
        error_data: payload[:value][:statuses][0][:errors][0][:error_data]
      }
    end
  end

  def call
    return unless valid_status_transition?

    return if @new_status == 'failed' && should_retry_failed_message?

    statuses_to_publish = determine_statuses_to_publish
    statuses_to_publish.each do |status|
      old_serialized_message = MessageSerializer::Details.serialize_whatsapp_message(@message)
      update_message_to_status(status)
      publish_status_event(status, old_serialized_message)
    end
  end

  private

  def valid_status_transition?
    case @new_status
    when 'sent'
      @message.status == 'sending'
    when 'delivered'
      %w[sending sent].include?(@message.status)
    when 'read', 'failed'
      true
    else
      false
    end
  end

  def determine_statuses_to_publish
    current_status = @message.status
    target_status = @new_status

    return [target_status] if target_status == 'failed'

    current_index = STATUS_SEQUENCE.index(current_status)
    target_index = STATUS_SEQUENCE.index(target_status)

    return [target_status] if target_index <= current_index

    STATUS_SEQUENCE[(current_index + 1)..target_index]
  end

  def update_message_to_status(status)
    case status
    when 'sent'
      @message.update!(status: status, sent_at: @timestamp)
    when 'delivered'
      @message.update!(status: status, delivered_at: @timestamp)
    when 'read'
      @message.update!(status: status, read_at: @timestamp)
    when 'failed'
      error_code = @error_data&.dig(:code)
      error_details = @error_data&.dig(:error_data, :details)

      error_code_prefix = error_code.present? ? "#{error_code} - " : ""
      status_message = "#{error_code_prefix}#{error_details}"

      @message.update!(status: status, failed_at: @timestamp, status_message: status_message)
    end
  end

  def publish_status_event(status, old_serialized_message)
    Rails.logger.info "TenantId: #{@message.tenant_id} Publishing status event: #{status} for message #{@message.id}"

    Publishers::WhatsappMessageStatusUpdatedPublisher.call(@message)
    Publishers::WhatsappMessageStatusUpdatedV2Publisher.call(@message, old_serialized_message)
    Publishers::WhatsappEntityMessageStatusTrackPublisher.call(@message) if @message.campaign_info.present?
  end

  def should_retry_failed_message?
    return false unless @error_data.present?

    error_code = @error_data[:code]
    return false unless WHATSAPP_RETRYABLE_ERROR_CODES.include?(error_code)

    max_retry_count = @message.metadata&.dig('retryConfig', 'noOfTimes') || 0
    current_retry_count = @message.metadata&.dig('retryConfig', 'timesRetried') || 0

    if current_retry_count.to_i < max_retry_count.to_i
      Rails.logger.info "TenantId: #{@message.tenant_id} MessageId: #{@message.id} retrying error code: #{error_code}"
      true
    else
      Rails.logger.info "TenantId: #{@message.tenant_id} MessageId: #{@message.id} max retries exhausted error code: #{error_code}"
      false
    end
  end
end
