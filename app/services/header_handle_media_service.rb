# frozen_string_literal: true

require 'open-uri'

class HeaderHandleMediaService
  
  def initialize(header_handle_url, connected_account, current_user, tenant_id, meta_response_name)
    @header_handle_url = header_handle_url
    @connected_account = connected_account
    @current_user = current_user
    @tenant_id = tenant_id
    @meta_response_name = WhatsappTemplate.generate_namespace(meta_response_name)
  end

  def process
    Rails.logger.info "Processing header_handle URL: #{@header_handle_url}"

    begin
      URI.open(@header_handle_url) do |file_io|
        file_name = generate_s3_key
        upload_to_s3(file_io, file_name)
        template_media = create_template_media_record(file_name, file_io)

        {
          template_media: template_media,
          success: true
        }
      end
    rescue => e
      Rails.logger.error "Error processing header_handle URL: #{e.message}"
      raise e
    end
  end

  private

  def generate_s3_key
    uri = URI.parse(@header_handle_url)
    file_extension = File.extname(uri.path).presence || '.jpg'

    "tenant_#{@tenant_id}/connected_account_#{@connected_account.id}/#{@meta_response_name}_#{SecureRandom.uuid}#{file_extension}"
  end

  def upload_to_s3(file_io, file_name)
    Rails.logger.info "Uploading file to S3: #{file_name}"
    S3::UploadFile.new(file_io, file_name, S3_ATTACHMENT_BUCKET).call
    Rails.logger.info "Successfully uploaded to S3: #{file_name}"
  end

  def create_template_media_record(s3_file_name, file_data)
    file_extension = File.extname(s3_file_name)

    template_media = TemplateMedia.create!(
      file_name: s3_file_name,
      file_size: file_data&.size,
      tenant_id: @tenant_id,
      file_type: get_mime_type(file_extension)
    )

    Rails.logger.info "Created template media record: #{template_media.id}"
    template_media
  end


  def get_mime_type(file_extension)
    case file_extension.downcase
    when '.pdf'
      'application/pdf'
    when '.jpeg', '.jpg'
      'image/jpeg'
    when '.png'
      'image/png'
    when '.mp4'
      'video/mp4'
    end
  end
end
