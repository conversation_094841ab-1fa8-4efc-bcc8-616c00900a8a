# frozen_string_literal: true

class FieldMappingsService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
    @token = Thread.current[:token]
  end

  def get
    unless @auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to view connected account field mappings"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = ConnectedAccount.where(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id]).first
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}")
    end

    unless [LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid entity type')}")
    end

    connected_account.send("#{@params[:entity_type]}_field_mapping")
  end

  def create_or_update
    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.error "User doesn't have permission to update connected account field mappings"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = ConnectedAccount.where(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id]).first
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}")
    end

    unless [LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid entity type')}")
    end

    field_mapping = FieldMapping.find_or_initialize_by(connected_account_id: connected_account.id, entity_type: @params[:entity_type])
    field_mapping.assign_attributes(
      @params.slice(
        :campaign,
        :source,
        :sub_source,
        :utm_campaign,
        :utm_content,
        :utm_medium,
        :utm_source,
        :utm_term
      )
    )

    if field_mapping.campaign_changed? || field_mapping.source_changed?
      fields_response = "Fields::#{@params[:entity_type].classify}".constantize.new(@token).fetch
      ['campaign', 'source'].each do |mapped_field|
        if field_mapping.send(mapped_field).present?
          active_ids = fields_response.find { |fr| fr['name'] == mapped_field }&.dig('picklist', 'values').to_a.reject { |val| val['disabled'] }.map { |val| val['id'] }
          if active_ids.exclude?(field_mapping.send(mapped_field))
            raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_or_inactive_field}||#{I18n.t('error.invalid_data', error: 'Invalid or inactive campaign or source field')}")
          end
        end
      end
    end

    begin
      field_mapping.save!
      connected_account.update!(updated_by: @current_user)
    rescue StandardError => e
      Rails.logger.error "FieldMappingsService Error while saving field mapping #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid fields')}")
    end

    connected_account.reload.send("#{@params[:entity_type]}_field_mapping")
  end

  def delete_all
    FieldMapping.where(connected_account_id: @params[:id]).destroy_all
  end
end
