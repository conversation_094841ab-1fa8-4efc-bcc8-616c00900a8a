# frozen_string_literal: true

class WhatsappCreditsService
  def add(tenant_id, credits)
    begin
      WhatsappCredit.transaction do
        whatsapp_credit = WhatsappCredit.lock.find_or_create_by(tenant_id: tenant_id) do |wc|
          wc.total = 0
          wc.consumed = 0
          wc.parked = 0.0
          wc.tenant_id = tenant_id
          wc.credits_revised_at = Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
        end

        added_credits = credits - (whatsapp_credit.total - whatsapp_credit.consumed)
        whatsapp_credit.total = credits&.round(2)
        whatsapp_credit.consumed = 0
        whatsapp_credit.is_low_credits_email_sent = false
        whatsapp_credit.save!

        WhatsappCreditHistory.create(
          tenant_id: tenant_id,
          entry_type: CREDITS_ADDED,
          value: added_credits&.abs&.round(2),
          start_time: Time.now.to_i,
          end_time: Time.now.to_i,
          balance: whatsapp_credit.total
        )

        Publishers::TenantUsagePublisher.call(tenant_id)
      end
    rescue StandardError => e
      Rails.logger.error "WhatsappCreditsService Error while adding credits Tenant id #{tenant_id} message: #{e.message}"
    end
  end

  def update(tenant_id)
    tenant_whatsapp_credits = WhatsappCredit.find_by(tenant_id: tenant_id)

    unless tenant_whatsapp_credits.present?
      Rails.logger.error "No whatsapp credits found for tenant id: #{tenant_id}"

      return
    end

    end_time = Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
    connected_accounts_list = ConnectedAccount.where('tenant_id = ? AND credits_revised_upto < ? AND (status = ? OR (deactivated_at IS NULL OR deactivated_at > credits_revised_upto))', tenant_id, end_time, ACTIVE)

    return unless connected_accounts_list.present?

    data_points = {}

    connected_accounts_list.each do |connected_account|
      start_time = connected_account.credits_revised_upto.to_i

      conversation_analytics_response = get_conversation_analytics(connected_account, start_time, end_time)

      unless conversation_analytics_response.present?
        Rails.logger.error "Error while fetching conversation analytics for connected acccount id: #{connected_account.id}, tenant id: #{tenant_id}"

        next
      end

      conversation_analytics_data = conversation_analytics_response.body.dig('pricing_analytics', 'data')

      unless conversation_analytics_data.present?
        Rails.logger.info "Conversation analytics not present for connected account id: #{connected_account.id}, tenant id: #{tenant_id}"

        connected_account.update!(credits_revised_upto: end_time)
        unpark_credits(connected_account, start_time, end_time)
        next
      end

      conversation_analytics_data[0]['data_points'].sort_by! { |data_point| data_point['start'] }

      data_points[connected_account.id] = conversation_analytics_data[0]['data_points']
    end

    connected_accounts_list.each do |connected_account|
      begin
        WhatsappCredit.transaction do
          tenant_whatsapp_credits = WhatsappCredit.lock.find_by(tenant_id: tenant_id)

          data_points_for_connected_account = data_points[connected_account.id]

          unless data_points_for_connected_account.present?
            next
          end
          start_time = connected_account.credits_revised_upto.to_i

          unpark_credits(connected_account, start_time, end_time)

          data_points_for_connected_account.each do |data_point|
            tenant_whatsapp_credits = tenant_whatsapp_credits.reload
            credits_balance = tenant_whatsapp_credits.total.to_f - tenant_whatsapp_credits.consumed.to_f

            cost_with_markup = data_point['cost'].to_f + (data_point['cost'].to_f * MARKUP)
            credits_consumed = tenant_whatsapp_credits.consumed.to_f + cost_with_markup.to_f
            updated_balance = credits_balance.to_f - cost_with_markup.to_f

            whatsapp_credits_history_payload = {
              entry_type: CREDITS_DEDUCTED,
              conversation_type: data_point['pricing_type'],
              conversation_category: data_point['pricing_category'],
              conversation_count: data_point['volume'],
              start_time: data_point['start'],
              end_time: data_point['end'],
              phone_number: data_point['phone_number'],
              country: data_point['country'],
              value: cost_with_markup.to_f.round(2),
              tenant_id: tenant_id,
              connected_account_id: connected_account.id,
              balance: updated_balance&.round(2)
            }.with_indifferent_access

            WhatsappCreditHistory.create!(whatsapp_credits_history_payload)
            tenant_whatsapp_credits.update!(consumed: credits_consumed&.round(2))
          end

          connected_account.update!(credits_revised_upto: end_time)
        end
      rescue StandardError => e
        Rails.logger.error "WhatsappCreditsService Error while updating credits Tenant id #{tenant_id}, message: #{e.message}"
      end
    end

    tenant_whatsapp_credits.update(credits_revised_at: end_time)
    Publishers::TenantUsagePublisher.call(tenant_id)
  end

  def get_summary
    auth_data = Thread.current[:auth]

    unless auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to read whatsapp business details"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_credit = WhatsappCredit.find_by(tenant_id: auth_data.tenant_id)

    if whatsapp_credit.nil?
      whatsapp_credit = WhatsappCredit.new(total: 0, consumed: 0, parked: 0)
    end

    whatsapp_credit
  end

  def get_history(params)
    auth_data = Thread.current[:auth]

    unless auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to read whatsapp business details"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_credit_histories = WhatsappCreditHistory.where(tenant_id: auth_data.tenant_id)

    if params.dig(:jsonRule, :rules).present?
      start_time_rule = params.dig(:jsonRule, :rules).find { |rule| rule[:id] == 'startTime' }

      connected_account_rule = params.dig(:jsonRule, :rules).find { |rule| rule[:id] == 'connectedAccount' }

      whatsapp_credit_histories = ValidateAndBuildQueryFromJsonRule.new(whatsapp_credit_histories, [start_time_rule]).build_query if start_time_rule.present?

      if connected_account_rule.present?
        whatsapp_credit_histories = ValidateAndBuildQueryFromJsonRule.new(whatsapp_credit_histories, [connected_account_rule]).build_query.or(whatsapp_credit_histories.where(entry_type: 'CREDITS_ADDED'))
      end
    end

    usage_per_conversation_category =
      whatsapp_credit_histories.where.not(conversation_category: nil).group(:conversation_category).sum(:value)

    created_at_field = 'created_at'
    direction = 'desc'

    [
      whatsapp_credit_histories.order(created_at_field.underscore.to_sym => direction).paginate(
        page: params[:page] || 1,
        per_page: params[:size] || 10
      ),
      usage_per_conversation_category
    ]
  end

  def unpark_credits(connected_account, start_time, end_time)
    current_start = start_time
    amount_to_unpark = 0

    while current_start < end_time
      current_end = current_start + 24 * 60 * 60
      whatsapp_credit_history = WhatsappCreditHistory.where(tenant_id: connected_account.tenant_id, entry_type: CREDITS_PARKED, start_time: current_start, end_time: current_end, connected_account_id: connected_account.id)&.first
      
      if whatsapp_credit_history.present?
        amount_to_unpark += whatsapp_credit_history.value
        WhatsappCreditHistory.create!({
          entry_type: CREDITS_UNPARKED,
          start_time: current_start,
          end_time: current_end,
          phone_number: connected_account.waba_number.gsub('+', ''),
          value: whatsapp_credit_history.value,
          tenant_id: connected_account.tenant_id,
          connected_account_id: connected_account.id
        })
      end

      current_start = current_end
    end

    return unless amount_to_unpark > 0

    begin
      WhatsappCredit.transaction do
        whatsapp_credit = WhatsappCredit.lock.find_by(tenant_id: connected_account.tenant_id)
        whatsapp_credit.parked = (whatsapp_credit.parked - amount_to_unpark).round(2)
        whatsapp_credit.save!
      end
    rescue StandardError => e
      Rails.logger.error "WhatsappCreditsService Error while un parking credits Tenant id #{connected_account.tenant_id}, message: #{e.message}"
    end
  end

  def park_credits(tenant_id, recipient_country_calling_code, conversation_category, connected_account)
    markets_by_country_calling_codes = JSON.parse(File.read("#{Rails.root}/config/whatsapp-markets.json"))
    cost_by_markets = JSON.parse(File.read("#{Rails.root}/config/whatsapp-pricing.json"))

    target_market = markets_by_country_calling_codes[recipient_country_calling_code] || 'Other'
    cost = cost_by_markets[target_market][conversation_category]

    begin
      WhatsappCredit.transaction do
        whatsapp_credit = WhatsappCredit.lock.find_by(tenant_id: tenant_id)
        message_cost = cost + (cost * MARKUP)
        whatsapp_credit.parked = (whatsapp_credit.parked + message_cost).round(2)
        whatsapp_credit.save!

        start_time = Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
        end_time = Date.today.in_time_zone('Asia/Calcutta').end_of_day.to_i + 1

        whatsapp_credit_history = WhatsappCreditHistory.where(tenant_id: tenant_id, entry_type: CREDITS_PARKED, start_time: start_time, end_time: end_time, connected_account_id: connected_account.id)&.first

        unless whatsapp_credit_history.present?
          whatsapp_credit_history = WhatsappCreditHistory.new({
            entry_type: CREDITS_PARKED,
            start_time: start_time,
            end_time: end_time,
            phone_number: connected_account.waba_number.gsub('+', ''),
            value: 0.0,
            tenant_id: tenant_id,
            connected_account_id: connected_account.id
          })
        end

        whatsapp_credit_history.value = (whatsapp_credit_history.value + message_cost).round(2)
        whatsapp_credit_history.save!
      end
    rescue StandardError => e
      Rails.logger.error "WhatsappCreditsService Error while parking credits Tenant id #{tenant_id}, message: #{e.message}"
    end
  end

  def deduct_chatbot_credits(tenant_id, connected_account, charge)
    begin
      WhatsappCredit.transaction do
        whatsapp_credit = WhatsappCredit.lock.find_by(tenant_id: tenant_id)
        unless whatsapp_credit
          Rails.logger.error "WhatsappCreditsService | No whatsapp credits found for tenant id: #{tenant_id}"
          return
        end

        credits_consumed = whatsapp_credit.consumed.to_f + charge.to_f
        credits_balance = whatsapp_credit.total.to_f - credits_consumed.to_f

        whatsapp_credit.update!(consumed: credits_consumed.round(2))

        create_or_update_chatbot_credit_history(tenant_id, connected_account, charge.to_f, credits_balance.round(2))

        Rails.logger.info "WhatsappCreditsService | Deducted chatbot credits: #{charge} for tenant: #{tenant_id}"
      end
    rescue StandardError => e
      Rails.logger.error "WhatsappCreditsService | Error while deducting chatbot credits for tenant #{tenant_id}: #{e.message}"
    end
  end

  def get_status
    auth_data = Thread.current[:auth]

    bulk_message_credits_status = WhatsappCredit.has_whatsapp_credits_for_bulk?(auth_data.tenant_id)

    bulk_message_credits_status
  end

  private

  def create_or_update_chatbot_credit_history(tenant_id, connected_account, charge, balance)
    start_time = Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
    end_time = Date.today.in_time_zone('Asia/Calcutta').end_of_day.to_i + 1

    whatsapp_credit_history = WhatsappCreditHistory.where(
      tenant_id: tenant_id,
      entry_type: CREDITS_DEDUCTED,
      conversation_category: CHATBOT,
      start_time: start_time,
      end_time: end_time,
      connected_account_id: connected_account.id
    ).first

    unless whatsapp_credit_history.present?
      whatsapp_credit_history = WhatsappCreditHistory.new({
        entry_type: CREDITS_DEDUCTED,
        conversation_category: CHATBOT,
        start_time: start_time,
        end_time: end_time,
        phone_number: connected_account.waba_number&.gsub('+', ''),
        value: 0.0,
        tenant_id: tenant_id,
        connected_account_id: connected_account.id,
        balance: balance
      })
    end

    # Add the charge to the daily total
    whatsapp_credit_history.value = (whatsapp_credit_history.value.to_f + charge).round(2)
    whatsapp_credit_history.balance = balance
    whatsapp_credit_history.save!

    Rails.logger.info "WhatsappCreditsService | Updated chatbot credit history for tenant: #{tenant_id}, daily total: #{whatsapp_credit_history.value}"
  end

  def get_conversation_analytics(connected_account, start_time, end_time, retry_count = 0)
    begin
      Interakt::ConversationAnalytics.new(connected_account, start_time, end_time).fetch
    rescue
      if retry_count < 3
        get_conversation_analytics(connected_account, start_time, end_time, retry_count + 1)
      end
    end
  end
end
