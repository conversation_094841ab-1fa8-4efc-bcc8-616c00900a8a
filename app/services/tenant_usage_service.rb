# frozen_string_literal: true

require 'rest-client'

class TenantUsageService < ApplicationService
  def initialize(tenant_id = nil)
    @tenant_id = tenant_id || Thread.current[:user]&.tenant_id
    @token = Thread.current[:token]
  end

  def call
    fetch_tenant_usage
  end

  private

  def fetch_tenant_usage
    begin
      response = RestClient.get(
        "#{SERVICE_IAM}/v1/tenants/usage",
        {
          'Authorization': "Bearer #{@token}"
        }
      )
      
      return parse_usage_response(response.body) unless response.nil?

      Rails.logger.error "TenantUsageService - Empty response"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::NotFound
      Rails.logger.error "TenantUsageService - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "TenantUsageService - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "TenantUsageService - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::Unauthorized
      Rails.logger.error "TenantUsageService - 401"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    rescue JSON::ParserError => e
      Rails.logger.error "TenantUsageService - JSON parsing error: #{e.message}"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    end
  end

  def parse_usage_response(response_body)
    parsed_response = JSON.parse(response_body)
    
    # Validate response structure
    unless parsed_response.is_a?(Hash) && 
           parsed_response.key?('records') && 
           parsed_response.key?('storage')
      Rails.logger.error "TenantUsageService - Invalid response structure"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    end

    {
      records: {
        used: parsed_response.dig('records', 'used').to_i,
        total: parsed_response.dig('records', 'total').to_i
      },
      storage: {
        used: parsed_response.dig('storage', 'used').to_f,
        total: parsed_response.dig('storage', 'total').to_f
      }
    }
  end
end
