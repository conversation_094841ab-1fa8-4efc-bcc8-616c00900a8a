# frozen_string_literal: true

require 'rest-client'

class TenantUsageService < ApplicationService
  CACHE_TTL = 5.minutes # Cache for 5 minutes

  def initialize(tenant_id = nil)
    @tenant_id = tenant_id || Thread.current[:user]&.tenant_id
    @token = Thread.current[:token]
  end

  def call
    return fetch_from_cache if cached_usage_exists?

    usage_data = fetch_tenant_usage
    cache_usage_data(usage_data) if usage_data && @tenant_id.present?
    usage_data
  end

  # Class method to clear cache for a specific tenant
  def self.clear_cache(tenant_id)
    cache_key = "tenant_usage:#{tenant_id}"
    Rails.cache.delete(cache_key)
    Rails.logger.info "TenantUsageService - Cache cleared for tenant_id: #{tenant_id}"
  end

  private

  def cache_key
    @cache_key ||= "tenant_usage:#{@tenant_id}"
  end

  def cached_usage_exists?
    @tenant_id.present? && Rails.cache.exist?(cache_key)
  end

  def fetch_from_cache
    cached_data = Rails.cache.read(cache_key)
    Rails.logger.info "TenantUsageService tenant_id: #{@tenant_id} - Cache hit"
    cached_data
  end

  def cache_usage_data(usage_data)
    Rails.cache.write(cache_key, usage_data, expires_in: CACHE_TTL)
    Rails.logger.info "TenantUsageService tenant_id: #{@tenant_id} - Data cached for #{CACHE_TTL} seconds"
  end

  def fetch_tenant_usage
    Rails.logger.info "TenantUsageService tenant_id: #{@tenant_id} - Fetching from API"

    begin
      response = RestClient.get(
        "#{SERVICE_IAM}/v1/tenants/usage",
        {
          'Authorization': "Bearer #{@token}"
        }
      )

      return parse_usage_response(response.body) unless response.nil?

      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - Empty response"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::NotFound
      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::Unauthorized
      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - 401"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    rescue JSON::ParserError => e
      Rails.logger.error "TenantUsageService tenant_id: #{@tenant_id} - JSON parsing error: #{e.message}"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    end
  end

  def parse_usage_response(response_body)
    parsed_response = JSON.parse(response_body)
    {
      records: {
        used: parsed_response.dig('records', 'used').to_i,
        total: parsed_response.dig('records', 'total').to_i
      },
      storage: {
        used: parsed_response.dig('storage', 'used').to_f,
        total: parsed_response.dig('storage', 'total').to_f
      }
    }
  end
end
