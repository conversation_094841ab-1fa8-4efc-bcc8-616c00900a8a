# frozen_string_literal: true

class Message::SessionMessage
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def send_text
    unless WhatsappCredit.has_whatsapp_credits?(@auth_data.tenant_id)
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end

    validate_entity 

    unless @params[:message_type] == TEXT.downcase
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_type')}")
    end

    unless @params[:message_body].present? && @params[:message_body].size <= 4096
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_body')}")
    end

    connected_account = validate_connected_account
    validate_agent_user(connected_account)

    entity_data = {}
    if @params[:entity_id].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
    end

    selected_phone_number = get_selected_phone(entity_data)

    if @params[:entity_id].present? && !entity_data.dig('recordActions', 'sms')
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    entity_name = "#{entity_data['firstName']} #{entity_data['lastName']}".strip
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"

    phone_number_conversation, phone_number_sub_conversation = fetch_conversation_based_on_permissions(connected_account, phone_number, entity_data['ownerId'])

    unless is_session_active?(phone_number_conversation)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.session_inactive')}")
    end

    update_conversation_owner(phone_number_conversation)

    if @params[:is_manual] && phone_number_conversation.is_chatbot_in_progress
      phone_number_conversation.update!(chatbot_conversation_completed: true)
      Publishers::ChatbotConversationCompletedPublisher.call(phone_number_conversation)
      Rails.logger.info "Manual user message sent, ending chatbot conversation for conversation #{phone_number_conversation.id}"
    end

    handle_ongoing_sub_conversation(phone_number_conversation, connected_account, 'text')
    
    phone_number_sub_conversation = phone_number_conversation.ongoing_sub_conversation

    response = Interakt::Message.new(connected_account).send_session_message(@params[:message_type], { body: @params[:message_body] }, phone_number)

    message_params = {
      content: @params[:message_body],
      direction: 'outgoing',
      medium: 'whatsapp',
      owner_id: @current_user.id,
      sent_at: DateTime.now,
      sender_number: connected_account.waba_number,
      recipient_number: phone_number,
      status: 'sending',
      message_type: WHATSAPP_BUSINESS,
      remote_id: response.body.dig('messages', 0)['id'],
      related_to: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      recipients: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      validate_lookup: false,
      connected_account: connected_account,
      conversation_id: phone_number_conversation.id,
      sub_conversation_id: phone_number_sub_conversation.id,
      component_wise_content: build_component_wise_content(header: nil, body_text: @params[:message_body], footer_text: nil, buttons: [])
    }.with_indifferent_access

    Message::MessageService.new(message_params).call
  end

  def send_media
    unless WhatsappCredit.has_whatsapp_credits?(@auth_data.tenant_id)
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end

    validate_entity

    unless @params[:message_type] == 'media'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_type')}")
    end

    @params[:media].map do |media|
      validate_media(media)
    end

    connected_account = validate_connected_account
    validate_agent_user(connected_account)

    entity_data = {}
    if @params[:entity_id].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
    end

    selected_phone_number = get_selected_phone(entity_data)
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"
    entity_name = "#{entity_data['firstName']} #{entity_data['lastName']}".strip

    if @params[:entity_id].present? && !entity_data.dig('recordActions', 'sms')
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    phone_number_conversation, phone_number_sub_conversation = fetch_conversation_based_on_permissions(connected_account, phone_number, entity_data['ownerId'])

    unless is_session_active?(phone_number_conversation)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.session_inactive')}")
    end

    update_conversation_owner(phone_number_conversation)

    if @params[:is_manual] && phone_number_conversation.is_chatbot_in_progress
      phone_number_conversation.update!(chatbot_conversation_completed: true)
      Publishers::ChatbotConversationCompletedPublisher.call(phone_number_conversation)
      Rails.logger.info "Manual user message sent, ending chatbot conversation for conversation #{phone_number_conversation.id}"
    end

    handle_ongoing_sub_conversation(phone_number_conversation, connected_account, 'media')

    phone_number_sub_conversation = phone_number_conversation.ongoing_sub_conversation

    @params[:media].map do |media|
      message_params = {
        content: media['caption'] || 'Media Message',
        direction: 'outgoing',
        medium: 'whatsapp',
        owner_id: @current_user.id,
        sent_at: DateTime.now,
        sender_number: connected_account.waba_number,
        recipient_number: phone_number,
        status: 'sending',
        message_type: WHATSAPP_BUSINESS,
        related_to: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
        recipients: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
        validate_lookup: false,
        connected_account: connected_account,
        attachments: [
          {
            data: media[:file],
            skip_delete: true,
            file_name: media[:file].original_filename
          }
        ],
        conversation_id: phone_number_conversation.id,
        sub_conversation_id: phone_number_sub_conversation.id,
        component_wise_content: build_component_wise_content(header: nil, body_text: media[:caption], footer_text: nil, buttons: [])
      }.with_indifferent_access

      message_id = Message::MessageService.new(message_params).call

      begin
        response = send("send_#{media[:type]}_message", media, connected_account, entity_data, phone_number)
        Message.where(id: message_id).update_all(remote_id: response.body.dig('messages', 0)['id'])
      rescue ExceptionHandler::ThirdPartyAPIError
        Message.where(id: message_id).update_all(status: 'failed', failed_at: DateTime.now.utc)
      end

      File.delete(media[:file].path)
      message_id
    end
  end

  def send_interactive_message
    unless WhatsappCredit.has_whatsapp_credits?(@auth_data.tenant_id)
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end

    validate_entity

    unless ['button', 'list', 'cta_url'].include?(@params[:message_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_type')}")
    end
    
    header = @params[:header]
    body_text = @params[:body]
    footer_text = @params[:footer]
    buttons = @params[:buttons] || []
    sections = @params[:sections] || []
    menu_button_text = @params[:menu_button] || @params[:menu_button_text]
    button_text = @params[:button_text]
    cta_url = @params[:cta_url]

    validate_interactive_message_payload(header: header, body_text: body_text, footer_text: footer_text, buttons: buttons, sections: sections, button_text: button_text, cta_url: cta_url)

    connected_account = validate_connected_account

    interactive_message_options = {
      header: header,
      body_text: body_text,
      footer_text: footer_text,
      buttons: buttons,
      connected_account: connected_account,
      sections: sections,
      menu_button_text: menu_button_text,
      message_type: @params[:message_type],
      button_text: button_text,
      cta_url: cta_url
    }
    payload, attachments = build_interactive_message_payload(interactive_message_options)

    entity_data = {}
    if @params[:entity_id].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
    end
    
    selected_phone_number = get_selected_phone(entity_data)
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"
    entity_name = "#{entity_data['firstName']} #{entity_data['lastName']}".strip

    if @params[:entity_id].present? && !entity_data.dig('recordActions', 'sms')
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    phone_number_conversation, phone_number_sub_conversation = fetch_conversation_based_on_permissions(connected_account, phone_number, entity_data['ownerId'])

    unless is_session_active?(phone_number_conversation)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.session_inactive')}")
    end

    update_conversation_owner(phone_number_conversation)

    handle_ongoing_sub_conversation(phone_number_conversation, connected_account, 'interactive message')

    phone_number_sub_conversation = phone_number_conversation.ongoing_sub_conversation

    begin
      response = Interakt::Message.new(connected_account).send_interactive_session_message(@params[:message_type], payload, phone_number)
      remote_id = response.body.dig('messages', 0)['id']
      status = 'sending'
    rescue ExceptionHandler::ThirdPartyAPIError
      remote_id = nil
      status = 'failed'
    end

    message_params = {
      content: build_interactive_message_content(header, body_text, footer_text, buttons, menu_button_text, button_text),
      direction: 'outgoing',
      medium: 'whatsapp',
      owner_id: @current_user.id,
      sent_at: DateTime.now,
      sender_number: connected_account.waba_number,
      recipient_number: phone_number,
      status: status,
      message_type: WHATSAPP_BUSINESS,
      remote_id: remote_id,
      related_to: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      recipients: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      validate_lookup: false,
      attachments: attachments,
      connected_account: connected_account,
      conversation_id: phone_number_conversation.id,
      sub_conversation_id: phone_number_sub_conversation.id,
      component_wise_content: build_component_wise_content(header: header, body_text: body_text, footer_text: footer_text, buttons: buttons, sections: sections, menu_button: menu_button_text, button_text: button_text, cta_url: cta_url)
    }.with_indifferent_access

    Message::MessageService.new(message_params).call
  end

  private

  def build_component_wise_content(header:, body_text:, footer_text:, buttons:, sections: [], menu_button: nil, button_text: nil, cta_url: nil)
    content = []
    header = header&.to_h&.with_indifferent_access || {}
    buttons&.each_with_index do |button, idx|
      content << {
        position: idx,
        type: "BUTTON",
        format: "QUICK_REPLY",
        text: button['text'],
        value: nil
      }
    end

    if menu_button.present?
      content << {
        position: 0,
        type: "BUTTON",
        format: "LIST_MENU_BUTTON",
        text: menu_button,
        value: nil
      }
    end

    section_row_position = 0
    sections&.each_with_index do |section, s_idx|
      section['rows']&.each_with_index do |row, r_idx|
        row = row&.to_h&.with_indifferent_access
        content << {
          position: section_row_position,
          type: "LIST",
          format: "LIST_ROW",
          text: row['title'],
          description: row['description'],
          section_title: section['title'],
          value: nil
        }

        section_row_position += 1
      end
    end

    if body_text.present?
      content << {
        position: 0,
        type: "BODY",
        format: "TEXT",
        text: body_text,
        value: nil
      }
    end

    if header.present? && header['format'] == 'text' && header['text'].present?
      content << {
        position: 0,
        type: "HEADER",
        format: "TEXT",
        text: header['text'],
        value: nil
      }
    end

    if footer_text.present?
      content << {
        position: 0,
        type: "FOOTER",
        format: "TEXT",
        text: footer_text,
        value: nil
      }
    end

    if button_text.present? && cta_url.present?
      content << {
        position: 0,
        type: "BUTTON",
        format: "CTA_URL",
        text: button_text,
        value: cta_url
      }
    end

    content
  end

  def map_mime_type_to_media_type(mime_type)
    case mime_type
    when /^audio\//
      'audio'
    when /^image\//
      'image'
    when /^video\//
      'video'
    when /^application\//
      'document'
    else
      # Default to document for unknown types
      'document'
    end
  end

  def validate_interactive_message_payload(header:, body_text:, footer_text:, buttons:, sections:, button_text:, cta_url:)
    if header.present? && header['format'] == 'text'
      if header['text'].blank? || header['text'].size > 60
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_header_text')}")
      end
    end

    if body_text.blank? || body_text.size > 1024
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_body_text')}")
    end

    if footer_text.present? && footer_text.size > 60
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_footer_text')}")
    end

    if buttons.size > 3
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.too_many_buttons')}")
    end

    buttons.each do |button|
      if button['text'].blank? || button['text'].size > 20
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_button_title')}")
      end
    end

    if @params[:message_type] == 'list'
      if sections.size > 10
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.too_many_sections')}")
      end

      sections.each_with_index do |section, s_idx|
        if section['title'].present? && section['title'].size > 24
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.section_title_too_long')}")
        end

        rows = section['rows'] || []
        if rows.size > 10
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.too_many_rows_in_section')}")
        end

        rows.each_with_index do |row, r_idx|
          if row['title'].present? && row['title'].size > 24
            raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.row_title_too_long')}")
          end

          if row['description'].present? && row['description'].size > 72
            raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.row_description_too_long')}")
          end
        end
      end
    end

    if @params[:message_type] == 'cta_url'
     if button_text.blank? || button_text.size > 20
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_button_text')}")
     end

      if cta_url.blank?
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_cta_url')}")
      end
    end
  end

  def send_audio_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(AUDIO.downcase, { id: upload_response.body['id'] }, phone_number)
  end

  def send_document_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(DOCUMENT.downcase, { id: upload_response.body['id'], caption: media[:caption], filename: media[:file].original_filename }, phone_number)
  end

  def send_video_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(VIDEO.downcase, { id: upload_response.body['id'], caption: media[:caption] }, phone_number)
  end

  def send_image_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(IMAGE.downcase, { id: upload_response.body['id'], caption: media[:caption] }, phone_number)
  end

  def validate_media(media)
    media_type = media[:type]
    allowed_extensions = ALLOWED_FILE_TYPES[media_type]
    
    unless allowed_extensions&.include?(File.extname(media[:file].original_filename).downcase)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_type')}")
    end

    if media[:file].size > MAX_FILE_SIZE[media_type]
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_size')}")
    end

    validate_caption(media)
  end

  def validate_caption(media)
    if media[:type] == 'audio' && media[:caption].present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.caption_not_allowed')}")
    end
  end

  def validate_connected_account
    # TODO: check onboarded status
    connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.invalid_message.connected_account_not_found')}")
    end

    unless connected_account.status == ACTIVE && connected_account.is_verified?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.inactive_or_unverified_account')}")
    end

    connected_account
  end

  def validate_agent_user(connected_account)
    # TODO: get agent user by entity
    unless AgentUser.where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, connected_account_id: connected_account.id).exists?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_connected_account')}")
    end
  end

  def get_selected_phone(entity_data)
    selected_phone_number = nil

    if @params[:conversation_id].present?
      phone_number_conversation = Conversation.find_by(id: @params[:conversation_id], tenant_id: @auth_data.tenant_id)
      
      unless phone_number_conversation.present?
        Rails.logger.info "Conversation not found for id: #{@params[:conversation_id]}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
      end

      if entity_data.blank?
        parsed_phone = Phonelib.parse(phone_number_conversation.phone_number)

        return { 'dialCode' => "+#{parsed_phone.country_code}", 'value' => parsed_phone.raw_national }
      end

      selected_phone_number = entity_data['phoneNumbers']&.find { |phone| "#{phone['dialCode']}#{phone['value']}" == phone_number_conversation.phone_number }
    else
      selected_phone_number = entity_data['phoneNumbers']&.find { |phone| phone['id'] == @params[:phone_id].to_i }
    end

    unless selected_phone_number.present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_phone_number}||#{I18n.t('error.invalid_phone_number_for_conversation')}")
    end

    selected_phone_number
  end

  def validate_entity
    if (@params[:entity_type].present? && ![LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])) ||
      (@params[:entity_type].present? && !@params[:entity_id].present?)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_entity')}")
    end

    unless @params[:phone_id].present? || @params[:conversation_id].present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.missing_phone_id_or_conversation_id')}")
    end
  end

  def fetch_conversation_based_on_permissions(connected_account, phone_number, entity_owner_id, conversation_id = nil)
    phone_number_conversation = nil

    if conversation_id.present?
      phone_number_conversation = Conversation.find_by(id: conversation_id, tenant_id: @auth_data.tenant_id)
    else
      phone_number_conversation =
      Conversation.find_by(
        tenant_id: @auth_data.tenant_id,
        connected_account_id: connected_account.id,
        phone_number: phone_number
      )
    end

    unless phone_number_conversation.present?
      Rails.logger.info "Conversation not found for the phone number: #{phone_number}, connected account id: #{connected_account.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end

    if !entity_owner_id.present?
      unless @current_user.can_send_conversation_message_unrelated_to_entity?(phone_number_conversation.owner_id)
        Rails.logger.info "User does not have conversation permission on conversation_id: #{phone_number_conversation.id}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
      end
    else
      unless @current_user.can_send_conversation_message?(@params[:entity_id], @params[:entity_type], entity_owner_id)
        Rails.logger.info "User does not have conversation permission on #{@params[:entity_type]}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
      end
    end

    [phone_number_conversation, phone_number_conversation.ongoing_sub_conversation]
  end

  def update_conversation_owner(conversation)
    if conversation.owner_id != @current_user.id
      conversation.update!(owner_id: @current_user.id)
    end
  end

  def is_session_active?(conversation)
    conversation.last_message_received_at.present? && conversation.last_message_received_at >= 24.hours.ago
  end

  def build_interactive_message_payload(options)
    header = options[:header]&.to_h&.with_indifferent_access
    body_text = options[:body_text]
    footer_text = options[:footer_text]
    buttons = options[:buttons]
    connected_account = options[:connected_account]
    sections = options[:sections] || []
    menu_button_text = options[:menu_button_text] || options[:menu_button] || 'Choose an option'
    message_type = options[:message_type]
    button_text = options[:button_text]
    cta_url = options[:cta_url]

    payload_header = nil
    attachments = []

    if header.present?
      header_format = header['format']&.downcase
      Rails.logger.info "Building interactive message payload header with format: #{header_format}, mime_type: #{map_mime_type_to_media_type(header_format)} and media details: #{header['mediaDetails']}"
      if %w[image video document].include?(header_format) && header['mediaDetails'].present?
        media_file = header['mediaDetails']['file']
        upload_response = Facebook::Media.new(connected_account).upload(media_file)
        payload_header = {
          type: header_format,
          header_format => {
            id: upload_response.body['id']
          }
        }.compact

        attachments << {
          data: media_file,
          skip_delete: true,
          file_name: media_file.original_filename
        }
      elsif header['format'] == 'text'
        payload_header = { type: 'text', text: header['text'] }
      end
    end

    if message_type == 'button'
      payload = {
        body: { text: body_text },
        action: {
          buttons: buttons.map do |button|
            {
              type: 'reply',
              reply: {
                id: button['id'],
                title: button['text']
              }
            }
          end
        }
      }
    elsif message_type == 'list'
      payload = {
        body: { text: body_text },
        action: {
          button: menu_button_text,
          sections: sections.map.with_index do |section, idx|
            {
              title: section['title'],
              rows: section['rows'].map.with_index do |row, r_idx|
                {
                  id: row['id'] || "row_#{idx}_#{r_idx}",
                  title: row['title'] || row['text'],
                  description: row['description']
                }
              end
            }
          end
        }
      }
    elsif message_type == 'cta_url'
      payload = {
        body: { text: body_text },
        action: {
          name: 'cta_url',
          parameters: {
            display_text: button_text,
            url: cta_url
          }
        }
      }
    end

    payload[:header] = payload_header if payload_header.present?
    payload[:footer] = { text: footer_text } if footer_text.present?

    [payload.with_indifferent_access, attachments]
  end

  def build_interactive_message_content(header, body_text, footer_text, buttons, menu_button_text, button_text)
    content_parts = []
    if header.present? && header['format'] == 'text' && header['text'].present?
      content_parts << header['text']
    end
    content_parts << body_text if body_text.present?
    content_parts << footer_text if footer_text.present?
    buttons.each do |button|
      content_parts << button['text']
    end
    content_parts << menu_button_text if menu_button_text.present?
    content_parts << button_text if button_text.present?
    content_parts.reject(&:blank?).join("\n")
  end

  def handle_ongoing_sub_conversation(phone_number_conversation, connected_account, message_type)
    unless phone_number_conversation.ongoing_sub_conversation.present?
      Rails.logger.info "Message::SessionMessage | sending #{message_type} | No Ongoing Sub conversation found ... Creating new one for conversation id: #{phone_number_conversation.id}, connected account id: #{connected_account.id}, Tenant id: #{@auth_data.tenant_id}"
      phone_number_conversation.sub_conversations.create!(
        tenant_id: connected_account.tenant_id,
        connected_account_id: connected_account.id,
        status: NEW
      )
      phone_number_conversation.status = NEW
      phone_number_conversation.save!
      Rails.logger.info "Message::SessionMessage | sending #{message_type} | Created new ongoing sub conversation and updated conversation status to NEW for conversation id: #{phone_number_conversation.id}, connected account id: #{connected_account.id}, Tenant id: #{@auth_data.tenant_id}"
    end
  end
end
