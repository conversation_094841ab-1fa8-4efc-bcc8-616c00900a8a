class Message::SyncService < ApplicationService
  include RelatedEntityProcessor
  include OwnerTokenProcessor

  def initialize(params, options = {})
    @params = params
    @options = options.with_indifferent_access
  end

  def call
    @current_user = Thread.current[:user]
    @tenant_id = @current_user.tenant_id

    UsageLimitValidatorService.new(@tenant_id).call if @options[:check_usage_limit]

    phone_number = get_phone_number
    owner_id = @params[:owner_id]
    if @options[:token].present?
      @owner_token = @options[:token]
      @owner_profile_permissions = @options[:user_profile_permissions]
    elsif owner_id && @current_user.id != owner_id
      set_owner_token
    end

    phone_number = "+#{phone_number}" if @options[:native_whatsapp_webhook]

    @related_entities = get_matching_entities(phone_number, @tenant_id)

    if @owner_token.present?
      unless @related_entities.present?
        Rails.logger.info "Owner doesn't have permission. Couldn't set owner id on message"
        @owner_user = @owner_token = @owner_profile_permissions = @params[:owner_id] = nil
        @related_entities = get_matching_entities(phone_number, @tenant_id)
      end
    end

    if @related_entities.present? && @options[:native_whatsapp_webhook] && @options[:is_new_conversation]
      phone = Phonelib.parse(phone_number)
      AssociateConversationWithEntitiesJob.perform_later(@params[:conversation_id], LOOKUP_LEAD, { dialCode: "+#{phone.country_code}", value: phone.raw_national })
      AssociateConversationWithEntitiesJob.perform_later(@params[:conversation_id], LOOKUP_CONTACT, { dialCode: "+#{phone.country_code}", value: phone.raw_national })
    end

    has_existing_entities = @related_entities.present?

    if @related_entities.blank? && @options[:native_whatsapp_webhook]
      @related_entities = create_related_entities
      @entity_details_for_chatbot = @related_entities
    end

    if @related_entities.present? || @options[:native_whatsapp_webhook]
      if @options[:native_whatsapp_webhook]
        connected_account = @options[:connected_account]
        # We are updating last_message_received_at field on connected account to show recently contacted connected account in the connected account lookup
        connected_account.update_column(:last_message_received_at, @params[:sent_at].to_i) if connected_account.present?
      end

      # check if message record with same remote id already exists
      if @params[:remote_id] && Message.where(remote_id: @params[:remote_id], tenant_id: @tenant_id).exists?
        Rails.logger.info "PATCH UPDATING for message with remoteId #{@params[:remote_id]}"
        message_id = Message.find_by(remote_id: @params[:remote_id], tenant_id: @tenant_id).id
        @params[:id] = message_id
        message = Message::PatchUpdateMessageService.new(params_data_for_update_message).call
      else
        # Prepare the params data and pass it further to create service
        message_id = Message::MessageService.new(params_data_for_create_message, @owner_token, @owner_profile_permissions, @owner_user, @options[:skip_message_created_events]).call
        message = Message.find_by(id: message_id)
      end

      # From nowonwards we will trigger chatbot conversation via message workflow only
      # if @related_entities.present? && @params[:direction] == INCOMING && @options[:is_new_conversation] && @options[:native_whatsapp_webhook]
      #   trigger_chatbot_conversation_for_new_entities
      # end

      # Trigger chatbot user response event for incoming messages from existing entities only
      if has_existing_entities && @options[:native_whatsapp_webhook] && @params[:direction] == INCOMING
        publish_chatbot_user_response_event(message.reload)
      end

      # prepare the json to return it in response
      res = { id: message.id, recipients: message.recipients.map{ |r| { entityId: r.entity_id, entityType: r.entity_type } } }
    else
      Rails.logger.info "No related entities found matching with phone numbers: #{phone_number}"
      raise(ExceptionHandler::PhoneNumbersNotMatchedError, ErrorCode.phone_numbers_not_match)
    end

    res
  end

  private

  def create_related_entities
    connected_account = @options[:connected_account]
    return unless connected_account.present?

    phone = Phonelib.parse("+#{@params[:sender_number]}")

    entity_data = connected_account.entities_to_create.map do |entity|
      entity_mapping = connected_account.send("#{entity}_field_mapping")

      payload = {
        ownerId: connected_account.created_by_id,
        lastName: @options[:entity_name],
        phoneNumbers: [
          {
            type: "MOBILE",
            code: phone.country,
            primary: true,
            dialCode: "+#{phone.country_code}",
            value: phone.raw_national
          }
        ]
      }

      if entity_mapping.present?
        campaign_information = {
          campaign: entity_mapping.campaign,
          source: entity_mapping.source,
          subSource: entity_mapping.sub_source,
          utmSource: entity_mapping.utm_source,
          utmCampaign: entity_mapping.utm_campaign,
          utmMedium: entity_mapping.utm_medium,
          utmContent: entity_mapping.utm_content,
          utmTerm: entity_mapping.utm_term
        }

        payload.merge!(campaign_information.compact_blank)
      end

      created_response = EntityService.new({ entity_type: entity, payload: payload }).create
      {
        entity: entity,
        id: created_response['id'],
        phone_number: "#{phone.country_code}#{phone.raw_national}",
        name: @options[:entity_name],
        tenant_id: connected_account.tenant_id
      }
    end
    entity_data
  end

  def params_data_for_create_message
    @data = @params
    @data['related_to'] = @related_entities
    @data['recipients'] = @related_entities
    @data.with_indifferent_access
  end

  def params_data_for_update_message
    @data = @params
    @data['related_to'] = @related_entities
    @data['recipients'] = @related_entities.dup
    create_destroy_params
    @data.with_indifferent_access
  end

  def create_destroy_params
    message = Message.find_by_id(@params[:id])
    message.related_to.each do |rel|
      entity = @related_entities.find { |re| re[:entity] == rel.entity_type && re[:id] == rel.entity_id }
      unless entity
        @data['related_to'] << HashWithIndifferentAccess.new({ entity: rel.entity_type, id: rel.entity_id, name: rel.name, phone_number: rel.phone_number, _destroy: true  })
      end
    end
  end

  def get_matching_entities(phone_number, tenant_id)
    result = SearchContacts.new([phone_number], tenant_id, @owner_token).call
    matched_contacts = result[:matched]
    result = SearchLeads.new([phone_number], tenant_id, @owner_token).call
    matched_leads = result[:matched]

    @entity_details_for_chatbot = [matched_contacts.first, matched_leads.first].compact
    matched_contacts + matched_leads
  end

  def get_phone_number
    if need_user_phone_details?
      user_id = @params[:owner_id] || @current_user.id
      user = User::GetUserProfileDetails.call(user_id, @tenant_id)
      if @params[:sender_number].present?
        @params[:recipient_number] = user.phone_number
      else
        @params[:sender_number] = user.phone_number
      end
    end

    phone_number = @params[:direction] == 'incoming' ? @params[:sender_number] : @params[:recipient_number]
    phone_number
  end

  def need_user_phone_details?
    (@params[:direction] == 'incoming' && !@params[:recipient_number].present?) ||
      (@params[:direction] == 'outgoing' && !@params[:sender_number].present?)
  end

  def trigger_chatbot_conversation_for_new_entities
    return unless @params[:conversation_id].present?
    return unless @params[:content].present?
    return unless @entity_details_for_chatbot.present?
    return unless @options[:connected_account]&.is_chatbot_configured?

    entity_details = @entity_details_for_chatbot.map { |related_entity| { id: related_entity[:id], entityType: related_entity[:entity] } }

    sleep(0.5) # Adding this to avoid entity not found in the entity search api

    InitiateChatbotConversationJob.perform_later(
      @params[:conversation_id],
      @params[:content],
      entity_details
    )

    Rails.logger.info "Scheduled chatbot conversation job for conversation #{@params[:conversation_id]}"
  end

  def publish_chatbot_user_response_event(message)
    return unless @options[:connected_account]&.is_chatbot_configured?

    conversation = Conversation.find_by(id: message.conversation_id)
    return unless conversation

    return unless conversation.chatbot_conversation_id.present? && !conversation.chatbot_conversation_completed?

    ChatbotUserResponseService.call(message: message, conversation: conversation, interactive_message_reply_id: @options[:interactive_message_reply_id])
    Rails.logger.info "Published chatbot user response event for message #{message.id}, conversation #{conversation.id}"
  end
end
