class Message::DeleteMessageService < ApplicationService
  def initialize(id, check_permission = true, data = {}, skip_event_publishing = false)
    @id = id
    @check_permission = check_permission
    @data = data
    @skip_event_publishing = skip_event_publishing
  end

  def call
    current_user = Thread.current[:user]
    @user_id = @data[:user_id] || current_user.id
    @tenant_id = @data[:tenant_id] || current_user.tenant_id
    @message = Message.unscoped.where(id: @id, tenant_id: @tenant_id).first
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @message
    raise(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed) unless check_delete_permissions
    begin
      attachments = @message.attachments
      file_names = attachments.map(&:file_name) if attachments.present?

      deletion_message = "Delete|Message: id: #{@message.id}, medium:#{@message.medium} is deleted by tenantId #{@tenant_id}, userId #{@user_id}"
      serialized_message_data = MessageSerializer::Details.serialize(@message, fetch_deleted_by(@user_id, @tenant_id), nil, nil, { add_deals_in_related_to: true }) unless @skip_event_publishing
      @message.destroy!
      Publishers::MessageDeleted.call(@tenant_id, @user_id, serialized_message_data) unless @skip_event_publishing
      Rails.logger.info deletion_message
      if file_names
        S3::DeleteFileFromS3.call(file_names, S3_ATTACHMENT_BUCKET)
        Rails.logger.info "Delete|Message attachments: Files: #{file_names.inspect} is deleted by tenantId #{@tenant_id}, userId #{@user_id}"
      end
    rescue => e
      Rails.logger.info "Delete|Message: Failed to delete message: #{e.message}"
    end
  end

  def soft_delete
    current_user = Thread.current[:user]
    @user_id = @data[:user_id] || current_user.id
    @tenant_id = @data[:tenant_id] || current_user.tenant_id
    @message = Message.where(id: @id, tenant_id: @tenant_id).first
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @message
    raise(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed) unless check_delete_permissions
    begin
      deletion_message = "SoftDelete|Message: id: #{@message.id}, medium:#{@message.medium} is soft deleted by tenantId #{@tenant_id}, userId #{@user_id}"
      serialized_message_data = MessageSerializer::Details.serialize(@message, fetch_deleted_by(@user_id, @tenant_id), nil, nil, { add_deals_in_related_to: true })
      @message.soft_delete!
      Publishers::MessageDeleted.call(@tenant_id, @user_id, serialized_message_data)
      handle_sub_conversation_cleanup if @message.sub_conversation_id.present?
      Rails.logger.info deletion_message
    rescue => e
      Rails.logger.info "SoftDelete|Message: Failed to soft delete message: #{e.message}"
    end
  end

  private
      
  def handle_sub_conversation_cleanup
    sub_conversation = SubConversation.find_by(id: @message.sub_conversation_id)
    conversation     = sub_conversation&.conversation
    return unless sub_conversation && conversation

    message_count = Message.unscoped.where(connected_account_id: @message.connected_account_id, deleted_at: nil, sub_conversation_id: sub_conversation.id).count

    return unless message_count.zero?

    Rails.logger.info "Destroying sub_conversation id=#{sub_conversation.id} as no messages left | tenantId #{@tenant_id}"

    conversation.update!(status: COMPLETED) unless sub_conversation.status == COMPLETED

    sub_conversation.destroy

    Rails.logger.info "SoftDelete|SubConversation: id=#{sub_conversation.id} deleted as no messages left | tenantId #{@tenant_id}"
  end

  def check_delete_permissions
    return true unless @check_permission
    auth_data = Thread.current[:auth]
    permission = auth_data.permissions.find{ |p| p.name == 'sms' }
    return true if permission&.action&.delete_all && @message.tenant_id == @tenant_id
    return true if permission&.action&.delete && @message.owner_id == @user_id
  end

  def fetch_deleted_by(user_id, tenant_id)
    user = User.find_by(id: user_id, tenant_id: tenant_id)

    return user if user.present?

    permissions = [
      {
        "name": 'user',
        "description": 'has access to user resource',
        "limits": -1,
        "units": 'count',
        "action": {
          "read": true,
          "readAll": true
        }
      }
    ]
    token = GenerateToken.call(user_id, tenant_id, permissions)
    User::GetUserProfileDetails.call(user_id, tenant_id, token)
  end
end
