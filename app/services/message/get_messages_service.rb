class Message::GetMessagesService < ApplicationService
  def initialize(params)
    @params = params
  end

  def call
    current_user = Thread.current[:user]
    @related_to = get_filter_rule('related_to')
    if has_message_permission_on_related_entity_type
      messages = Message.left_outer_joins(:related_to)
                   .where(tenant_id: current_user.tenant_id)
                   .where.not(message_type: WHATSAPP_BUSINESS)
                   .where(look_ups: { tenant_id: current_user.tenant_id, entity_type: @related_to['entity'], entity_id: @related_to['id'] })
                   .distinct

      messages = messages.order(created_at: :desc)
      messages = paginate(messages)
      messages
    else
      Rails.logger.info "Insufficient permission to list messages"
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end
  end

  private

  def has_message_permission_on_related_entity_type
    return unless @related_to
    auth_data = Thread.current[:auth]
    permission = auth_data.permissions.find{ |p| p.name == @related_to['entity'] }
    permission&.action&.sms
  end

  def get_filter_rule(rule)
    return unless @params[:json_rule].present?
    if json_rule = @params[:json_rule]
      if json_rule[:rules].present?
        rule = json_rule[:rules].find {|r| r['field'] == rule}
      end
    end
    return rule['value'] if rule
  end

  def paginate(messages)
    page = @params[:page] || 1
    size = @params[:size] || 10
    messages.paginate(page: page.to_i, per_page: size.to_i)
  end
end