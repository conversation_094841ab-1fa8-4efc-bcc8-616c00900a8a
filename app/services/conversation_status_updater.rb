# frozen_string_literal: true

class ConversationStatusUpdater
  def initialize(conversation)
    @conversation = conversation
    @sub_conversation = @conversation.ongoing_sub_conversation
  end
  
  def call
    if @sub_conversation.status == IN_PROGRESS && @conversation.status == IN_PROGRESS
      Rails.logger.info("ConversationStatusUpdater | Both sub and main conversations already IN_PROGRESS, skipping update")
      return
    end

    @messages = Message.where(connected_account_id: @conversation.connected_account_id,  sub_conversation_id: @sub_conversation.id)
    if @messages.empty?
      Rails.logger.info("ConversationStatusUpdater | No messages found for sub_conversation_id=#{@sub_conversation.id}, skipping update")
      return
    end

    @directions_count = @messages.group(:direction).count
    @incoming_count = @directions_count['incoming'] || 0
    @outgoing_count = @directions_count['outgoing'] || 0

    sub_conversation_status = determine_sub_conversation_status
    main_conversation_status = determine_main_conversation_status

    update_statuses(sub_conversation_status, main_conversation_status)

    Rails.logger.info("ConversationStatusUpdater | Status Update completed for conversation_id=#{@conversation.id}, connected_account_id=#{@conversation.connected_account_id}, tenant_id=#{@conversation.tenant_id}")
  rescue => e
    Rails.logger.error("ConversationStatusUpdater | Error while updating conversation_id=#{@conversation.id} — #{e.message}")
  end

  private

  def determine_sub_conversation_status
    case
    when @incoming_count > 0 && @outgoing_count > 0 || @incoming_count > 1 || @outgoing_count > 1
      IN_PROGRESS
    else
      nil
    end
  end

  def determine_main_conversation_status
    case
    when @incoming_count > 0 && @outgoing_count > 0
      IN_PROGRESS
    else
      nil
    end
  end

  def update_statuses(sub_conversation_status, main_conversation_status)
    @sub_conversation.update!(status: sub_conversation_status) if sub_conversation_status.present?

    @conversation.update!(status: main_conversation_status) if main_conversation_status.present?
  end
end
