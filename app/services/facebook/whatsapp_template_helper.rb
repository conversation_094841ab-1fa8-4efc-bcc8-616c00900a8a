# frozen_string_literal: true

module Facebook
  module WhatsappTem<PERSON><PERSON><PERSON><PERSON>
    def generate_template_body(whatsapp_template)
      {
        name: whatsapp_template.whatsapp_template_namespace,
        language: whatsapp_template.language,
        category: whatsapp_template.category,
        components: generate_components_body(whatsapp_template.components)
    }.with_indifferent_access
    end

    def generate_components_body(components)
      button_components, other_components = components.partition { |component| component.component_type == BUTTON }

      other_components.inject([]) do |components_array, component|
        components_array << send("generate_#{component.component_type.downcase}_#{component.component_format.downcase}_component", component)
      end.concat(
        button_components.present? ? [generate_buttons_body(button_components)] : []
      )
    end

    def generate_header_text_component(component)
      {
        type: component.component_type,
        format: component.component_format,
        text: component.component_text
      }.merge(component.content.present? ? { example: component.content } : {})
    end

    def generate_header_image_component(component)
      {
        type: component.component_type,
        format: component.component_format,
        example: {
          header_handle: [ component.template_media.whatsapp_file_handle ]
        }
      }
    end

    def generate_body_text_component(component)
      {
        type: component.component_type,
        text: component.component_text
      }.merge(component.content.present? ? { example: component.content } : {})
    end

    def generate_footer_text_component(component)
      {
        type: component.component_type,
        text: component.component_text
      }
    end

    def generate_buttons_body(button_components)
      {
        type: "BUTTONS",
        buttons: generate_buttons_array(button_components.sort_by(&:position))
      }
    end

    def generate_buttons_array(sorted_button_components)
      sorted_button_components.inject([]) do |buttons_array, component|
        buttons_array << send("generate_#{component.component_type.downcase}_#{component.component_format.downcase}_component", component)
      end
    end

    def generate_button_quick_reply_component(component)
      {
        type: component.component_format,
        text: component.component_text
      }
    end

    def generate_button_phone_number_component(component)
      {
        type: component.component_format,
        text: component.component_text,
        phone_number: component.component_value
      }
    end

    def generate_button_url_component(component)
      {
        type: component.component_format,
        text: component.component_text,
        url: component.component_value.gsub('{{2}}', '{{1}}')
      }.merge(component.content.present? ? { example: component.content } : {})
    end


    def generate_button_copy_code_component(component)
      {
        type: component.component_format,
        example: component.component_text
      }
    end

    alias generate_header_video_component generate_header_image_component
    alias generate_header_document_component generate_header_image_component

    def self.parse_template_body(meta_response, tenant_id, connected_account, current_user)
      {
        name: meta_response[:name],
        category: meta_response[:category],
        language: meta_response[:language],
        status: meta_response[:status],
        whatsapp_template_id: meta_response[:id],
        components_attributes: parse_components_from_meta(meta_response[:components] || [], tenant_id, connected_account, current_user, meta_response[:name])
      }.with_indifferent_access
    end

    def self.parse_components_from_meta(components, tenant_id, connected_account, current_user, meta_response_name)
      components.flat_map.with_index do |component, index|
        case component[:type]
        when HEADER
          parse_header_component(component, tenant_id, index, connected_account, current_user, meta_response_name)
        when BODY
          parse_body_component(component, tenant_id, index)
        when FOOTER
          parse_footer_component(component, tenant_id, index)
        when BUTTONS
          parse_buttons_component(component, tenant_id, index)
        else
          []
        end
      end
    end

     def self.parse_header_component(component, tenant_id, position, connected_account, current_user, meta_response_name)
      case component[:format]
      when TEXT
        [{
          component_type: HEADER,
          component_format: TEXT,
          component_text: component[:text],
          content: component[:example],
          position: position,
          tenant_id: tenant_id
        }]
      when IMAGE, VIDEO, DOCUMENT
        header_handle_url = component.dig(:example, :header_handle, 0)
        if header_handle_url.present?
            header_handle_service = HeaderHandleMediaService.new(
              header_handle_url,
              connected_account,
              current_user,
              tenant_id,
              meta_response_name
            )
          result = header_handle_service.process
          template_media = result[:template_media]

          [{
            component_type: HEADER,
            component_format: component[:format],
            component_value: template_media.id,
            media_type: STATIC,
            position: position,
            tenant_id: tenant_id
          }]
        else
          []
        end
      else
        []
      end
    end
      
    def self.parse_body_component(component, tenant_id, position)
      {
        component_type: BODY,
        component_format: TEXT,
        component_text: component[:text],
        content: component[:example],
        position: position,
        tenant_id: tenant_id
      }
    end

    def self.parse_footer_component(component, tenant_id, position)
      {
        component_type: FOOTER,
        component_format: TEXT,
        component_text: component[:text],
        position: position,
        tenant_id: tenant_id
      }
    end

    def self.parse_buttons_component(component, tenant_id, position)
      (component[:buttons] || []).map.with_index do |button, index|
        case button[:type]
        when QUICK_REPLY
          {
            component_type: BUTTON,
            component_format: QUICK_REPLY,
            component_text: button[:text],
            position: index,
            tenant_id: tenant_id
          }
        when PHONE_NUMBER
          {
            component_type: BUTTON,
            component_format: PHONE_NUMBER,
            component_text: button[:text],
            component_value: button[:phone_number],
            position: index,
            tenant_id: tenant_id
          }
        when URL
          {
            component_type: BUTTON,
            component_format: URL,
            component_text: button[:text],
            component_value: button[:url],
            content: button[:example],
            position: index,
            tenant_id: tenant_id
          }
        when COPY_CODE
          {
            component_type: BUTTON,
            component_format: COPY_CODE,
            component_text: button[:example].first,
            position: index,
            tenant_id: tenant_id
          }
        else
          {}
        end
      end
    end
  end
end
