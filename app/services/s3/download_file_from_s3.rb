# frozen_string_literal: true

class S3::DownloadFileFromS3
  def initialize(file_name, chatbot_attachment: false)
    @file_name = file_name
    @chatbot_attachment = chatbot_attachment

    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )

    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(S3_ATTACHMENT_BUCKET)
  end

  def call
    begin
      obj = @bucket.object(@file_name)
      obj.download_file(@chatbot_attachment ? @file_name.split('/', 4)[-1] : @file_name.split('/', 3)[-1])
    rescue StandardError => e
      Rails.logger.error "Error while downloading attachment from s3 for Error: #{e.message}"
    end
  end
end
