# frozen_string_literal: true

require 'rest-client'

class ChatbotService
  def initialize(message, entity_details, connected_account, admin_token = nil, conversation_id)
    @message = message
    @entity_details = entity_details
    @connected_account = connected_account
    @admin_token = admin_token
    @conversation_id = conversation_id
  end

  def initiate_conversation
    payload = {
      message: @message,
      entityDetails: @entity_details,
      connectedAccount: {
        id: @connected_account.id,
        name: @connected_account.name
      },
      messageConversationId: @conversation_id
    }

    begin
      headers = {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
      }

      headers['Authorization'] = "Bearer #{@admin_token}"

      response = RestClient.post(
        "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations",
        payload.to_json,
        headers
      )

      if response.code == 200
        Rails.logger.info "Chatbot conversation initiated successfully: #{response.body}"
        {
          success: true,
          data: {}
        }
      else
        Rails.logger.error "Chatbot service returned non-200 status: #{response.code} - #{response.body}"
        { success: false, error: "Unexpected response code: #{response.code}" }
      end
    rescue RestClient::BadRequest => e
      Rails.logger.error "Chatbot service bad request: #{e.message} - #{e.response&.body}"
      { success: false, error: "Bad request: #{e.message}" }
    rescue RestClient::InternalServerError => e
      Rails.logger.error "Chatbot service internal server error: #{e.message} - #{e.response&.body}"
      { success: false, error: "Internal server error: #{e.message}" }
    rescue RestClient::RequestTimeout => e
      Rails.logger.error "Chatbot service timeout: #{e.message}"
      { success: false, error: "Request timeout: #{e.message}" }
    rescue RestClient::Exception => e
      Rails.logger.error "Chatbot service error: #{e.message} - #{e.response&.body}"
      { success: false, error: "Service error: #{e.message}" }
    rescue JSON::ParserError => e
      Rails.logger.error "Chatbot service JSON parse error: #{e.message}"
      { success: false, error: "Invalid JSON response: #{e.message}" }
    rescue StandardError => e
      Rails.logger.error "Chatbot service unexpected error: #{e.message}"
      { success: false, error: "Unexpected error: #{e.message}" }
    end
  end
end
