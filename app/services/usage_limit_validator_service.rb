# frozen_string_literal: true

class UsageLimitValidatorService < ApplicationService
  def initialize(message_type = nil, tenant_id = nil, direction = nil)
    @message_type = message_type
    @tenant_id = tenant_id || Thread.current[:user]&.tenant_id
    @direction = direction
  end

  def call
    validate_usage_limits
  end

  private

  def validate_usage_limits
    # Skip validation if message type is not restricted
    return true unless should_check_limits?

    usage_data = fetch_usage_data
    
    check_records_limit(usage_data) if USAGE_LIMIT_CONFIG[:check_records]
    check_storage_limit(usage_data) if USAGE_LIMIT_CONFIG[:check_storage]
    
    true
  end

  def should_check_limits?
    return false if @message_type.blank?

    # Skip validation for incoming messages as they are received from external sources
    return false if @direction == 'incoming'

    USAGE_LIMIT_CONFIG[:restricted_message_types].include?(@message_type)
  end

  def fetch_usage_data
    TenantUsageService.new(@tenant_id).call
  rescue => e
    Rails.logger.error "UsageLimitValidatorService - Failed to fetch usage data: #{e.message}"
    # If we can't fetch usage data, we should allow the operation to proceed
    # to avoid blocking legitimate operations due to service issues
    return nil
  end

  def check_records_limit(usage_data)
    return unless usage_data&.dig(:records)

    records = usage_data[:records]
    used = records[:used]
    total = records[:total]

    if used >= total
      Rails.logger.info "UsageLimitValidatorService - Records limit exceeded: #{used}/#{total} for tenant #{@tenant_id}"
      raise(
        ExceptionHandler::UsageLimitExceededError,
        "#{ErrorCode.usage_limit_exceeded}||#{I18n.t('error.usage_limit_exceeded')}"
      )
    end
  end

  def check_storage_limit(usage_data)
    return unless usage_data&.dig(:storage)

    storage = usage_data[:storage]
    used = storage[:used]
    total = storage[:total]

    if used >= total
      Rails.logger.info "UsageLimitValidatorService - Storage limit exceeded: #{used}/#{total} for tenant #{@tenant_id}"
      raise(
        ExceptionHandler::UsageLimitExceededError,
        "#{ErrorCode.usage_limit_exceeded}||#{I18n.t('error.usage_limit_exceeded')}"
      )
    end
  end
end
