# frozen_string_literal: true

class UsageLimitValidatorService < ApplicationService
  def initialize(tenant_id = nil)
    @tenant_id = tenant_id || Thread.current[:user]&.tenant_id
  end

  def call
    validate_usage_limits
  end

  private

  def validate_usage_limits
    usage_data = fetch_usage_data
    
    check_records_limit(usage_data)
    check_storage_limit(usage_data)

    true
  end

  def fetch_usage_data
    TenantUsageService.new(@tenant_id).call
  rescue => e
    Rails.logger.error "UsageLimitValidatorService tenant_id: #{@tenant_id} - Failed to fetch usage data: #{e.message}"
    return nil
  end

  def check_records_limit(usage_data)
    return unless usage_data&.dig(:records)

    records = usage_data[:records]
    used = records[:used]
    total = records[:total]

    if used >= total
      Rails.logger.info "UsageLimitValidatorService - Records limit exceeded: #{used}/#{total} for tenant #{@tenant_id}"
      raise(
        ExceptionHandler::UsageLimitExceededError,
        "#{ErrorCode.usage_limit_exceeded}||#{I18n.t('error.records_limit_exceeded')}"
      )
    end
  end

  def check_storage_limit(usage_data)
    return unless usage_data&.dig(:storage)

    storage = usage_data[:storage]
    used = storage[:used]
    total = storage[:total]

    if used >= total
      Rails.logger.info "UsageLimitValidatorService - Storage limit exceeded: #{used}/#{total} for tenant #{@tenant_id}"
      raise(
        ExceptionHandler::UsageLimitExceededError,
        "#{ErrorCode.usage_limit_exceeded}||#{I18n.t('error.storage_limit_exceeded')}"
      )
    end
  end
end
