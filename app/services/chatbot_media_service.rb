# frozen_string_literal: true

class ChatbotMediaService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def upload
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless @params[:media_file].present?

    connected_account = validate_connected_account_and_agent

    file_type = File.extname(@params[:media_file].original_filename)
    file_name = @params[:media_file].original_filename

    file_name = "tenant_#{@current_user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/#{file_name.gsub(file_type, '')}_#{SecureRandom.uuid}#{file_type}"

    S3::UploadFile.new(@params[:media_file].path, file_name, S3_ATTACHMENT_BUCKET, skip_delete: true).call
    chatbot_media = ChatbotMedia.create(
      file_name: file_name,
      file_size: @params[:media_file].size,
      tenant_id: @current_user.tenant_id,
      file_type: @params[:media_type],
      chatbot_id: @params[:chatbot_id],
      connected_account_id: connected_account.id
    )

    File.delete(@params[:media_file].path)
    chatbot_media
  end

  def list
    connected_account = validate_connected_account_and_agent

    chatbot_medias = ChatbotMedia.where(tenant_id: @auth_data.tenant_id, chatbot_id: @params[:chatbot_id], connected_account_id: connected_account.id)

    chatbot_medias.map do |chatbot_media|
      {
        id: chatbot_media.id,
        fileName: chatbot_media.extract_file_name,
        fileSize: chatbot_media.file_size,
        fileType: chatbot_media.file_type,
        mediaUrl: chatbot_media.media_url
      }
    end
  end

  private

  def validate_media
    unless ALLOWED_FILE_TYPES[@params[:media_type]].include?(File.extname(@params[:media_file].original_filename).downcase)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_type')}")
    end

    if @params[:media_file].size > MAX_FILE_SIZE[@params[:media_type]]
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_size')}")
    end
  end

  def validate_connected_account_and_agent
    connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id])

    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless AgentUser.where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, connected_account_id: @params[:connected_account_id]).exists?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_connected_account')}")
    end

    connected_account
  end
end
