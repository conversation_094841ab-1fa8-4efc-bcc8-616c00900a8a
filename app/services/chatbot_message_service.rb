# frozen_string_literal: true

class ChatbotMessageService
  def initialize(conversation, connected_account, payload)
    @conversation = conversation
    @connected_account = connected_account
    @payload = payload
  end

  def send_message
    case @payload['chatbotType']
    when AI_BASED_CHATBOT
      send_ai_based_chatbot_messages
    when RULE_BASED_CHATBOT
      send_rule_based_chatbot_messages
    end
  end

  private

  def send_ai_based_chatbot_messages
    if @payload['welcomeMessage'].present?
      send_chatbot_response_message(@conversation, @connected_account, TEXT_MESSAGE, @payload['welcomeMessage'])
    end

    if @payload['message'].present?
      send_chatbot_response_message(@conversation, @connected_account, TEXT_MESSAGE, @payload['message'])
    end
  end

  def send_rule_based_chatbot_messages
    @payload['nodeDetails'] = @payload['nodeDetails'].is_a?(Array) ? @payload['nodeDetails'] : [@payload['nodeDetails']]
    @payload['nodeDetails'].each do |node_detail|
      node_detail['type'] = INTERACTIVE_CTA_URL if node_detail['type'] == 'url'
      case node_detail['type']
      when QUESTION
        send_question_node_response(node_detail)
      when SEND_MESSAGE
        send_message_node_response(node_detail)
      when INTERACTIVE_BUTTON, INTERACTIVE_LIST, INTERACTIVE_CTA_URL
        send_interactive_response(node_detail)
      end
    end
  end

  def send_question_node_response(node_detail)
    return unless node_detail.dig('data', 'text').present?

    Rails.logger.info "ChatbotMessageService | Sending chatbot response for conversation #{@conversation.id}, Node Type: question"

    send_chatbot_response_message(@conversation, @connected_account, TEXT_MESSAGE, node_detail.dig('data', 'text'))
  end

  def send_message_node_response(node_detail)
    node_detail['data'].each do |message|
      if message['type'] == TEXT_MESSAGE
        Rails.logger.info "ChatbotMessageService | Sending chatbot response for conversation #{@conversation.id}, Node Type: sendMessage, Message type: text"
        send_chatbot_response_message(@conversation, @connected_account, TEXT_MESSAGE, message['text'])
      elsif message['type'] == MEDIA_MESSAGE
        Rails.logger.info "ChatbotMessageService | Sending chatbot response for conversation #{@conversation.id}, Node Type: sendMessage, Message type: media"

        chatbot_media = ChatbotMedia.find_by(id: message.dig('mediaFile', 'fileId'))
        media_caption = message.dig('mediaFile', 'fileCaption')

        if chatbot_media.present?
          file_name = chatbot_media.file_name.split('/', 4)[-1]
          S3::DownloadFileFromS3.new(chatbot_media.file_name, chatbot_attachment: true).call

          file_path = Rails.root.join(file_name)
          Rails.logger.info "ChatbotMessageService | File path: #{file_path}"
          uploaded_file = ActionDispatch::Http::UploadedFile.new(
            filename: chatbot_media.extract_file_name,
            type: message.dig('mediaFile', 'fileType'),
            tempfile: File.open(file_path, "rb")
          )

          Rails.logger.info "ChatbotMessageService | Downloaded media file #{file_name} from S3 for conversation #{@conversation.id}"
          media_details = [
            {
              type: map_mime_type_to_media_type(message.dig('mediaFile', 'fileType')),
              caption: media_caption,
              file: uploaded_file
            }
          ]
          send_chatbot_response_message(@conversation, @connected_account, MEDIA_MESSAGE, media_details)

          File.delete(file_path) if File.exist?(file_path)
        end
      end
    end
  end

  def send_interactive_response(node_detail)
    node_data = node_detail['data']
    return unless node_data.present?

    node_type = node_detail['type']
    header = node_data['header']
    body = node_data['body']
    footer = node_data['footer']
    buttons = node_data['buttons']
    sections = node_data['sections']
    menu_button_text = node_data['menuButton']
    button_text = node_data['buttonText']
    cta_url = node_data['ctaURL']
    header_payload = nil
    
    if header.present?
      if header['format'] == 'text'
        header_payload = { format: header['format'], text: header['text'] }
      elsif header.dig('mediaFile', 'fileId').present?
        chatbot_media = ChatbotMedia.find_by(id: header.dig('mediaFile', 'fileId'))
        if chatbot_media.present?
          file_name = chatbot_media.file_name.split('/', 4)[-1]
          S3::DownloadFileFromS3.new(chatbot_media.file_name, chatbot_attachment: true).call

          file_path = Rails.root.join(file_name)
          Rails.logger.info "ChatbotMessageService | File path: #{file_path}"
          uploaded_file = ActionDispatch::Http::UploadedFile.new(
            filename: chatbot_media.extract_file_name,
            type: map_mime_type_to_media_type(header['format']),
            tempfile: File.open(file_path, "rb")
          )

          Rails.logger.info "ChatbotMessageService | Downloaded media file #{file_name} from S3 for conversation #{@conversation.id}"
          header_payload = { format: map_mime_type_to_media_type(header['format'])}
          header_payload[:mediaDetails] = {
            type: map_mime_type_to_media_type(header['format']),
            file: uploaded_file
          }
        end
      end
    end

    message_payload = {
      header: header_payload,
      body: body,
      footer: footer
    }.compact

    message_payload[:buttons] = buttons if buttons.present? && node_type == INTERACTIVE_BUTTON
    message_payload[:sections] = sections if sections.present? && node_type == INTERACTIVE_LIST
    message_payload[:menu_button_text] = menu_button_text if menu_button_text.present? && node_type == INTERACTIVE_LIST

    if node_type == INTERACTIVE_CTA_URL
      message_payload[:button_text] = button_text
      message_payload[:cta_url] = cta_url
    end
    node_type = 'button' if node_type == INTERACTIVE_BUTTON
    node_type = 'cta_url' if node_type == INTERACTIVE_CTA_URL
    Rails.logger.info "ChatbotMessageService | Sending interactive response for conversation #{@conversation.id}, payload: #{message_payload.inspect}"
    send_chatbot_response_message(@conversation, @connected_account, node_type, message_payload)

    File.delete(file_path) if file_path.present? && File.exist?(file_path)
  end

  def send_chatbot_response_message(conversation, connected_account, message_type, message_body)
    session_message_params = {
      id: connected_account.id,
      conversation_id: conversation.id,
      message_type: message_type
    }

    entity_details = get_entity_details_for_conversation(conversation)
    if entity_details.present?
      first_entity = entity_details.first
      session_message_params[:entity_id] = first_entity[:id]
      session_message_params[:entity_type] = first_entity[:entityType]
    end

    if message_type == MEDIA_MESSAGE
      session_message_params[:media] = message_body
    elsif ['button', 'list', 'cta_url'].include?(message_type)
      session_message_params.merge!(message_body)
    else
      session_message_params[:message_body] = message_body
    end
    
    Rails.logger.info "ChatbotMessageService | sending chatbot session message for conversation #{conversation.id}, Message Type: #{message_type}, payload: #{session_message_params}"
    begin
      session_message = Message::SessionMessage.new(session_message_params)

      send_session_message(session_message, message_type)
      Rails.logger.info "ChatbotMessageService | Chatbot response message sent for conversation #{conversation.id}"
    rescue ExceptionHandler::EntityNotFound => e
      Rails.logger.warn "ChatbotMessageService | EntityNotFound exception: #{e.message}. Retrying after delay for conversation #{conversation.id}"

      sleep(5)

      begin
        send_session_message(session_message, message_type)
        Rails.logger.info "ChatbotMessageService | Chatbot response message sent after retry for conversation #{conversation.id}"
      rescue ExceptionHandler::EntityNotFound => e2
        Rails.logger.error "ChatbotMessageService | Error after retry: #{e2.message} for conversation #{conversation.id}"
      end
    rescue StandardError => e
      Rails.logger.error "ChatbotMessageService | Error sending chatbot response message for conversation #{conversation.id}: #{e.message}"
    end
  end

  def send_session_message(session_message, message_type)
    if message_type == MEDIA_MESSAGE
      session_message.send_media
    elsif ['button', 'list', 'cta_url'].include?(message_type)
      session_message.send_interactive_message
    else
      session_message.send_text
    end
  end

  def get_entity_details_for_conversation(conversation)
    conversation_lookups = ConversationLookUp.where(conversation_id: conversation.id)

    entity_details = conversation_lookups.map do |lookup|
      {
        id: lookup.look_up.entity_id,
        entityType: lookup.look_up.entity_type
      }
    end

    entity_details
  end

  def map_mime_type_to_media_type(mime_type)
    case mime_type
    when /^audio\//
      'audio'
    when /^image\//
      'image'
    when /^video\//
      'video'
    when /^application\//
      'document'
    else
      # Default to document for unknown types
      'document'
    end
  end
end
