# frozen_string_literal: true

class ChatbotUserResponseService
  def self.call(message:, conversation:, interactive_message_reply_id: nil)
    new(message: message, conversation: conversation, interactive_message_reply_id: interactive_message_reply_id).call
  end

  def initialize(message:, conversation:, interactive_message_reply_id: nil)
    @message = message
    @conversation = conversation
    @interactive_message_reply_id = interactive_message_reply_id
  end

  def call
    return unless should_publish_chatbot_user_response?

    Rails.logger.info "ChatbotUserResponseService | Publishing chatbot user response for conversation #{@conversation.id}"

    message_content = @interactive_message_reply_id.present? ? @interactive_message_reply_id : @message.content
    is_media_message = @message.attachments.any? ? true : false
    Publishers::ChatbotUserResponsePublisher.call(
      message: message_content,
      chatbot_conversation_id: @conversation.chatbot_conversation_id,
      completed: @conversation.chatbot_conversation_completed,
      is_media_message: is_media_message
    )
  rescue StandardError => e
    Rails.logger.error "ChatbotUserResponseService | Error processing chatbot user response for conversation #{@conversation.id}: #{e.message}"
  end

  private

  def should_publish_chatbot_user_response?
    return false unless @message.direction == INCOMING

    return false unless @conversation.chatbot_conversation_id.present?

    return false if @conversation.chatbot_conversation_completed?

    true
  end
end
