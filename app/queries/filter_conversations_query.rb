class FilterConversationsQuery
  def initialize(auth_data, filter_params)
    @auth_data = auth_data
    @filter_params = filter_params
    @current_user = Thread.current[:user]
  end

  def call
    sql = conversations_for_tenant
    scope = paginate(sql)
    scope
  end

  private

  def conversations_for_tenant
    processed_conditions = conditions

    query = <<~END_SQL
      SELECT * FROM (
        SELECT
          conversations.id,
          conversations.phone_number,
          conversations.last_activity_at,
          conversations.connected_account_id,
          conversations.last_message_received_at,
          conversations.is_read,
          ROW_NUMBER()
          OVER (
            PARTITION BY conversations.phone_number
            ORDER BY conversations.last_activity_at DESC NULLS LAST
          ) as rownum
          FROM conversations
          LEFT OUTER JOIN conversation_look_ups clp ON conversations.id = clp.conversation_id
          LEFT OUTER JOIN look_ups lp ON lp.id = clp.look_up_id
          WHERE #{processed_conditions} and conversations.deleted_at is null
        ) tmp
        WHERE rownum=1
        ORDER BY tmp.#{sorting_order}
        NULLS LAST
    END_SQL
    query
  end

  def sorting_order
    field = @filter_params[:sort]&.split(',')&.first&.underscore || 'last_activity_at'
    order = @filter_params[:sort]&.split(',')&.last&.upcase || 'DESC'

    if field != 'last_activity_at' || %w[ASC DESC].exclude?(order)
      Rails.logger.error "Invalid sort params #{@filter_params[:sort]}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end

    "#{field} #{order}"
  end

  def paginate(sql)
    page = @filter_params[:page] || 1
    size = @filter_params[:size] || 10
    Conversation.paginate_by_sql(sql, page: page.to_i, per_page: size.to_i)
  end

  def conditions
    and_conditions_array = ["conversations.tenant_id = #{@auth_data.tenant_id}"]

    unless @auth_data.can_access?('sms', 'read_all')
      or_conditions_array = [
        "conversations.owner_id = #{@auth_data.user_id}"
      ]

      [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
        if @auth_data.can_access?(entity_type, 'read') && @auth_data.can_access?(entity_type, 'sms')
          or_conditions_array << "(lp.owner_id = #{@auth_data.user_id} AND lp.entity_type = '#{entity_type}')"
        end
      end

      shared_entities = @current_user.get_shared_entity_records(['LEAD', 'CONTACT'])

      [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
        owner_ids = shared_entities[entity_type.upcase][:entity_owner_ids].join(',')
        if owner_ids.present?
          or_conditions_array << "(lp.entity_type = '#{entity_type}' AND lp.owner_id IN (#{owner_ids}))"
        end

        entity_ids = shared_entities[entity_type.upcase][:entity_ids].join(',')

        if entity_ids.present?
          or_conditions_array << "(lp.entity_type = '#{entity_type}' AND lp.entity_id IN (#{entity_ids}))"
        end
      end

      and_conditions_array << "(#{or_conditions_array.join(' OR ')})"
    end

    filter_conversations(and_conditions_array)

    and_conditions_array.join(' AND ')
  end

  def filter_conversations(and_conditions_array)
    json_rule = @filter_params[:json_rule] || @filter_params[:jsonRule]
    return and_conditions_array unless json_rule&.dig(:rules).present?

    rules = json_rule[:rules]
    return and_conditions_array if rules.blank?

    rules.each do |rule|
      rule_id = rule[:id]
      value = rule[:value]
      operator = rule[:operator]

      next unless rule_id == 'isRead' && !value.nil?

      if operator == 'equal'
        and_conditions_array << "conversations.is_read = #{value}"
      elsif operator == 'not_equal'
        and_conditions_array << "conversations.is_read != #{value}"
      end
    end
    
    and_conditions_array
  end
end
