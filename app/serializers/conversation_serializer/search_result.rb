# frozen_string_literal: true

module ConversationSerializer
  class SearchResult
    extend MessageSerializer::Helper

    def self.serialize(conversations)
      json = Jbuilder.new

      current_user = Thread.current[:user]
      conversation_ids = conversations.map(&:id)

      all_lookups = ConversationLookupFilterService.new(current_user)
        .filter_all_lookups(conversation_ids)

      lookups_by_conversation = all_lookups.group_by(&:conversation_id)

      contact_ids = all_lookups.select { |lookup| lookup.entity_type == LOOKUP_CONTACT }
                               .map(&:entity_id)

      related_deals = fetch_related_deals_for_contacts(contact_ids, current_user.tenant_id)

      json.body do
        json.content(conversations) do |conversation|
          last_message = Message.where(conversation_id: conversation.id).order(sent_at: :desc).first

          
          conversation_lookups = lookups_by_conversation[conversation.id] || []
          related_entities = conversation_lookups.map do |related_entity|
            LookUpSerializer::Details.serialize(related_entity)
          end

          related_entities.sort_by! { |related_entity| related_entity['entity'] == LOOKUP_CONTACT ? 0 : 1 }
          parsed_phone = Phonelib.parse(conversation.phone_number)
    
          masking_enabled_entity = should_mask_phone_number?(related_entities)
          
          json.(conversation, :id)
          json.entityId related_entities.first&.dig('id')
          json.entityName related_entities.first&.dig('name')
          json.entityType related_entities.first&.dig('entity')
          json.connectedAccountId conversation.connected_account_id
          json.relatedTo related_entities
          json.isRead conversation.is_read
          last_message_received_at = conversation.last_message_received_at
      
          json.phoneNumber do
            json.value masking_enabled_entity ? mask_number(conversation.phone_number) : conversation.phone_number
  
            if last_message_received_at.present?
              json.session last_message_received_at < 24.hours.ago ? INACTIVE : ACTIVE
            else
              json.session INACTIVE
            end
          end

          json.lastMessageAt last_message&.sent_at
          json.lastMessageStatus last_message&.status
          json.lastMessage last_message&.plain_text_content
          json.lastMessageStatusInfo last_message&.status_message

          contact_ids = conversation_lookups.select { |lookup| lookup.entity_type == LOOKUP_CONTACT }.map(&:entity_id)
          related_deals_for_conversation = []

          contact_ids.each do |contact_id|
            contact_deals = related_deals[contact_id]
            if contact_deals.present? && contact_deals.is_a?(Array)
              related_deals_for_conversation.concat(contact_deals)
            end
          end

          unique_deals = related_deals_for_conversation.uniq { |deal| deal[:id] }
          json.relatedDeals unique_deals
        end

        json.page do
          json.no conversations.current_page.to_i
          json.size conversations.per_page
        end

        json.totalElements conversations.total_entries
        json.totalPages conversations.total_pages
        json.first conversations.previous_page.nil?
        json.last conversations.next_page.nil?
      end
    end

    def self.should_mask_phone_number?(related_entities)
      related_entities_for_masking = []
      lead_present = false
      contact_present = false

      related_entities.each do |entity|
        lead_present ||= entity['entity'] == LOOKUP_LEAD
        contact_present ||= entity['entity'] == LOOKUP_CONTACT
        break if lead_present && contact_present
      end

      related_entities_for_masking << LOOKUP_LEAD if lead_present
      related_entities_for_masking << LOOKUP_CONTACT if contact_present

      masking_enabled_entity = false

      related_entities_for_masking.each do |entity|
        masked_fields = GetMaskedFields.new(entity).call
        phone_field_masked = masked_fields.select { |field| field['name'] == 'phoneNumbers' }
        if phone_field_masked.present?
          masking_enabled_entity = true
          break
        end
      end

      masking_enabled_entity
    end
  end
end
