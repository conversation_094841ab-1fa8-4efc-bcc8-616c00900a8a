# frozen_string_literal: true

module MessageSerializer
  class Details
    extend MessageSerializer::Helper

    def self.serialize(message, deleted_by = nil, related_entity_for_masking = nil, related_to = [], options = {})
      return nil unless message

      masking_enabled = false
      if [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(related_entity_for_masking)
        related_entity_for_masking = LOOKUP_CONTACT if related_entity_for_masking == LOOKUP_DEAL

        masked_fields = GetMaskedFields.new(related_entity_for_masking).call
        masking_enabled = masked_fields.find { |field| field['name'] == 'phoneNumbers' }.present?
      end

      JSON(
        Jbuilder.new do |msg|
          related_entities = related_to.present? ? related_to : message.related_to
          message = mask_details(message) if masking_enabled

          msg.call(message, :id, :tenant_id, :content, :medium, :direction, :sent_at, :delivered_at, :read_at,
                   :recipient_number, :sender_number, :created_at, :updated_at, :message_type, :status_message, :conversation_id)
          msg.status message.status&.capitalize
          msg.subConversationId message.sub_conversation_id
          msg.owner UserSerializer::Details.serialize(message.owner)
          msg.relatedTo message.message_type == WHATSAPP_BUSINESS && options[:add_deals_in_related_to] ? get_related_to_for_native_whatsapp_message(message, add_owner: true) : related_entities.collect { |r| LookUpSerializer::Details.serialize(r, add_owner: true) }
          msg.components message.component_wise_content
          msg.recipients message.recipients.collect { |r| LookUpSerializer::Details.serialize(r, add_owner: true) }
          msg.attachments message.attachments.collect { |a| AttachmentSerializer::Details.serialize(a) }
          if deleted_by.present?
            msg.deletedBy UserSerializer::Details.serialize(deleted_by)
            msg.deletedAt DateTime.now.utc
          end
        end.target!
      )
    end

    def self.serialize_whatsapp_message(message)
      return nil unless message

      connected_account = ConnectedAccount.find(message.connected_account_id)
      whatsapp_template = WhatsappTemplate.find_by(id: message.whatsapp_template_id)

      if whatsapp_template.present?
        whatsapp_template_header_component = whatsapp_template.components.find_by(component_type: HEADER)
        whatsapp_template_header_component_format = [IMAGE, VIDEO, DOCUMENT].include?(whatsapp_template_header_component&.component_format) ? 'media' : 'text'
      end
      conversation = Conversation.find_by(id: message.conversation_id)
      whatsapp_template_header_component_format = whatsapp_template_header_component.present? ? whatsapp_template_header_component_format : 'none'

      Jbuilder.new do |msg|
        msg.id message.id
        msg.tenantId message.tenant_id
        msg.status message.status
        msg.sentFromAccount do
          msg.id connected_account.id
          msg.name connected_account.display_name
        end

        msg.direction message.direction
        msg.messageBody message.plain_text_content
        msg.sentAt message.sent_at.present? ? Time.at(message.sent_at).utc.strftime("%FT%T.%LZ") : nil
        msg.deliveredAt message.delivered_at.present? ? Time.at(message.delivered_at).utc.strftime("%FT%T.%LZ") : nil
        msg.readAt message.read_at.present? ? Time.at(message.read_at).utc.strftime("%FT%T.%LZ") : nil
        msg.receivedAt message.sent_at.present? && message.direction == 'incoming' ? Time.at(message.sent_at).utc.strftime("%FT%T.%LZ") : nil
        msg.template do 
          msg.id whatsapp_template&.id.present? ? whatsapp_template&.id : nil
          msg.name whatsapp_template&.name.present? ? whatsapp_template&.name : nil
        end

        msg.messageConversationId message.conversation_id
        msg.messageConversationStatus  conversation.status
        msg.owner UserSerializer::Details.serialize(message.owner)
        msg.templateType whatsapp_template_header_component_format
        msg.relatedTo get_related_to_for_native_whatsapp_message(message)
        msg.isMedia message.attachments.any? ? true : false
      end
    end

    def self.serialize_whatsapp_message_status_update_v2_payload(message, old_message = nil)
      JSON(
        Jbuilder.new do |msg|
          msg.entity self.serialize_whatsapp_message(message)
          msg.oldEntity old_message
          msg.metadata do
            msg.entityAction "UPDATED"
            msg.tenantId message.tenant_id
            msg.userId message.owner_id
            msg.entityType "WHATSAPP_MESSAGE"
            msg.workflowId nil
            msg.entityId message.id
            msg.executedWorkflows message.metadata&.dig('executedWorkflows') || []
          end
        end.target!
      )
    end

    def self.serialize_whatsapp_entity_message_status_track_payload(message)
      JSON(
        Jbuilder.new do |msg|
          msg.id message.id
          msg.userId message.owner_id
          msg.tenantId message.tenant_id
          msg.status message.status
          msg.sentAt message.sent_at.present? ? Time.at(message.sent_at).strftime("%FT%T.%LZ") : nil
          msg.deliveredAt message.delivered_at.present? ? Time.at(message.delivered_at).strftime("%FT%T.%LZ") : nil
          msg.readAt message.read_at.present? ? Time.at(message.read_at).strftime("%FT%T.%LZ") : nil
          msg.receivedAt (message.sent_at.present? && message.direction == 'incoming') ? Time.at(message.sent_at).strftime("%FT%T.%LZ") : nil
          msg.failedAt message.failed_at.present? ? Time.at(message.failed_at).strftime("%FT%T.%LZ") : nil
          msg.statusMessage message.status_message if message.failed_at.present? && message.status_message.present?
          msg.campaignInfo message.campaign_info if message.campaign_info.present?
        end.target!
      )
    end

    def self.serialize_whatsapp_message_created_v2_payload(message)
      JSON(
        Jbuilder.new do |msg|
          msg.entity self.serialize_whatsapp_message(message)
          msg.oldEntity nil
          msg.metadata do
            msg.entityAction "CREATED"
            msg.tenantId message.tenant_id
            msg.userId message.owner_id
            msg.entityType "WHATSAPP_MESSAGE"
            msg.workflowId nil
            msg.entityId message.id
            msg.executedWorkflows message.metadata&.dig('executedWorkflows') || []
          end
        end.target!
      )
    end
  end
end
