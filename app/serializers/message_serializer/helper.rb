# frozen_string_literal: true

module MessageSerializer::Helper
  def mask_details(message)
    if message.direction == 'incoming'
      message.sender_number = mask_number(message.sender_number)
    else
      message.recipient_number = mask_number(message.recipient_number)
    end

    message
  end

  def mask_number(phone_number, with_country_code: true)
    parsed_phone = Phonelib.parse(phone_number)
    with_country_code ?  "+#{parsed_phone.country_code}#{'*' * 4}#{phone_number[-3, 3]}" :  "#{'*' * 4}#{phone_number[-3, 3]}"
  end

  def get_related_to_for_native_whatsapp_message(message, add_owner: false)
    conversation = Conversation.unscoped.find(message.conversation_id)
    conversation_lookups = conversation.look_ups

    contact_ids = conversation_lookups.select { |lookup| lookup.entity_type == LOOKUP_CONTACT }.map(&:entity_id)
    related_deals = fetch_related_deals_for_contacts(contact_ids, message.tenant_id)
    related_deals_for_conversation = []

    contact_ids.each do |contact_id|
      contact_deals = related_deals[contact_id]
      if contact_deals.present? && contact_deals.is_a?(Array)
        related_deals_for_conversation.concat(contact_deals)
      end
    end

    related_deals_for_conversation = related_deals_for_conversation.uniq { |deal| deal[:id] }
    conversation_lookups.collect { |r| LookUpSerializer::Details.serialize(r, add_owner: add_owner) }.concat(related_deals_for_conversation.map { |deal| { id: deal[:id], name: deal[:name], entity: LOOKUP_DEAL } })
  end

  def fetch_related_deals_for_contacts(contact_ids, tenant_id)
    return {} if contact_ids.blank? || tenant_id.blank?

    GetDealsForContacts.new(contact_ids, tenant_id).call
  end

  def get_conversation_and_sub_conversations(messages)
    sub_conversation_ids = messages.map(&:sub_conversation_id).uniq.compact
    return [nil, nil] unless sub_conversation_ids.present?

    sub_conversations = SubConversation.where(id: sub_conversation_ids).index_by(&:id)
    conversation = Conversation.find_by(id: messages&.first&.conversation_id)

    [conversation, sub_conversations]
  end
end
