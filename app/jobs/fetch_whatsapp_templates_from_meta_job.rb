# frozen_string_literal: true

class FetchWhatsappTemplatesFromMetaJob < ApplicationJob
  queue_as :default

  def perform(connected_account_id, tenant_id, user_id, entity_types)
    connected_account = ConnectedAccount.find_by(id: connected_account_id)
    user = User.find_by(id: user_id)

    unless connected_account && user
      Rails.logger.error "FetchWhatsappTemplatesFromMetaJob | Invalid connected_account_id #{connected_account_id} or user_id #{user_id} in FetchWhatsappTemplatesFromMetaJob"
      return
    end

    fetch_templates_ids(connected_account, tenant_id, user_id, entity_types)
  end

  private

  def fetch_templates_ids(connected_account, tenant_id, user_id, entity_types)
    Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Fetching templates from Meta for WABA ID: #{connected_account.waba_id}"

    url = "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.waba_id}/message_templates?fields=id,name,status,category&limit=100"
    total_templates = 0
    approved_templates = 0
    template_ids = []

    begin
      loop do
        request_parameters = {
          url: url,
          request_type: :get
        }

        response = Facebook::Request.process(request_parameters, connected_account)
        parsed_response = response.body.is_a?(Hash) ? response.body : JSON.parse(response.body)

        if parsed_response['data']
          parsed_response['data'].each do |template|
            if template['status'] == APPROVED && TEMPLATE_CATEGORIES.include?(template['category'])
              template_ids << template['id']
              approved_templates += 1

              all_exist = entity_types.all? do |entity_type|
                WhatsappTemplate.exists?(whatsapp_template_id: template['id'], tenant_id: tenant_id, entity_type: entity_type)
              end

              if all_exist
                Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Skipping template with id: #{template['id']} as it already exists for all entity types: #{entity_types.join(', ')}"
              else
                connected_account.increment!(:sync_whatsapp_template_active_job_count)
                begin
                  Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Scheduling job to fetch details of new whatsapp template with id: #{template['id']} for WABA ID: #{connected_account.waba_id} and entity types: #{entity_types.join(', ')}"
                  FetchWhatsappTemplateDetailsFromMetaJob.perform_later(template['id'], connected_account.id, tenant_id, user_id, entity_types)
                rescue
                  Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Failed Scheduling job to fetch details of new whatsapp template with id: #{template['id']} for WABA ID: #{connected_account.waba_id} and entity types: #{entity_types.join(', ')}"
                  connected_account.decrement!(:sync_whatsapp_template_active_job_count)
                end
              end
            end
            total_templates += 1
          end
        end
      
        break unless parsed_response.dig('paging', 'next')

        url = parsed_response['paging']['next']
      end

      Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Total templates: #{total_templates} and approved templates: #{approved_templates} and skipped templates: #{total_templates - approved_templates} templates for WABA ID: #{connected_account.waba_id}"
      template_ids
    rescue StandardError => e
      Rails.logger.error "FetchWhatsappTemplatesFromMetaJob | Error While Fetching Templates from Meta for WABA ID: #{connected_account.waba_id}: #{e.message}"
      []
    ensure
      Rails.logger.info "FetchWhatsappTemplatesFromMetaJob | Completed fetching templates from Meta for WABA ID: #{connected_account.waba_id}."
    end
  end
end
