# frozen_string_literal: true

class UploadMediaAttachmentToS3Job < ApplicationJob
  queue_as :default
  sidekiq_options retry: 3

  def perform(connected_account_id, message_id, media_id, raise_message_created_events)
    connected_account = ConnectedAccount.find_by(id: connected_account_id)

    unless connected_account.present?
      Rails.logger.error "UploadMediaAttachmentToS3Job | Connected account not found with given id: #{connected_account_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}")
    end

    message = Message.find_by(id: message_id, tenant_id: connected_account.tenant_id)

    unless message.present?
      Rails.logger.error "UploadMediaAttachmentToS3Job | Message not found with given message id: #{message_id}, tenant_id: #{connected_account.tenant_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Message')}")
    end

    Rails.logger.info "UploadMediaAttachmentToS3Job | Fetch media from meta started, Time: #{Time.now}, Media id: #{media_id}, Message id: #{message.id}, Connected Account: #{connected_account.id}, Tenant id: #{connected_account.tenant_id}"

    media_url_response = Interakt::Media.new(connected_account).get_media_url(media_id)

    if media_url_response.body['url'].present?
      fetch_media_response = Interakt::Media.new(connected_account).download_from_media_url(media_url_response.body['url'].gsub('\/', '/'))

      Rails.logger.info "UploadMediaAttachmentToS3Job | Fetch media from meta completed, Time: #{Time.now}, Media id: #{media_id}, Message id: #{message.id}, Connected Account: #{connected_account.id}, Tenant id: #{connected_account.tenant_id}"

      media_file_name = fetch_media_response['content-disposition'].split('=')
      media_file_name =
        if media_file_name.count > 2
          media_file_name[1].split(';').first
        else
          media_file_name.last
        end

      file_name = media_file_name.split('.').first
      file_ext = media_file_name.split('.').last

      name = "#{message.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"

      file_name = "tenant_#{message.tenant_id}/user_#{message.owner_id}/attachments/#{name}"

      incoming_media_file =
        open("#{Rails.root}/tmp/#{name}", 'wb') do |file|
          file << fetch_media_response.body
        end

      file_data = open(incoming_media_file)
      file_size = file_data.try(:size)
      file_path = file_data.path

      Rails.logger.info "File path: #{file_path}"
      Rails.logger.info "File name: #{file_name}"

      S3::UploadFile.new(file_path, file_name, S3_ATTACHMENT_BUCKET).call

      message_attachment = message.attachments.last
      message_attachment.update(file_name: file_name, size: file_size)
    end

    if raise_message_created_events
      Publishers::MessageReceivedFromEntityPublisher.call(message) if message.direction == 'incoming'
      Publishers::MessageCreated.call(message.id)
      Publishers::WhatsappMessageCreatedV2Publisher.call(message) if message.message_type == WHATSAPP_BUSINESS
    end
  end
end
