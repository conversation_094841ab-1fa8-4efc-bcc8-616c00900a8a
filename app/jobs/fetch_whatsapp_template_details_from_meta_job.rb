# frozen_string_literal: true

class FetchWhatsappTemplateDetailsFromMetaJob < ApplicationJob
  queue_as :default

  def perform(template_id, connected_account_id, tenant_id, user_id, entity_types)
    connected_account = ConnectedAccount.find_by(id: connected_account_id)
    user = User.find_by(id: user_id)

    unless connected_account 
      Rails.logger.error "FetchWhatsappTemplateDetailsFromMetaJob | Invalid connected_account_id #{connected_account_id}"
      return
    end

    unless user
      Rails.logger.error "FetchWhatsappTemplateDetailsFromMetaJob | Invalid user_id #{user_id}"
      connected_account.decrement!(:sync_whatsapp_template_active_job_count)
      return
    end

    begin
      url = "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}"
      request_parameters = {
        url: url,
        request_type: :get
      }

      Rails.logger.info "FetchWhatsappTemplateDetailsFromMetaJob | Fetching Whatsapp Template Details for template ID: #{template_id}"

      response = Facebook::Request.process(request_parameters, connected_account)
      parsed_response = response.body.is_a?(Hash) ? response.body : JSON.parse(response.body)
      parsed_response = parsed_response.deep_symbolize_keys

      Rails.logger.info "FetchWhatsappTemplateDetailsFromMetaJob | Response received for Whatsapp Template ID: #{template_id}| Response: #{response.inspect}, Response Body: #{response.body.inspect}, Parsed_Response: #{parsed_response.inspect}"

      if contains_named_params?(parsed_response)
        Rails.logger.info "Skipping template #{template_id} because it contains named parameters."
        return
      end

      # Sync WhatsappTemplate data from Meta API response to the Kylas db.

      unless AgentUser.where(tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id).exists?
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_connected_account')}")
      end

      entity_types.each do |entity_type|
         create_whatsapp_template(parsed_response, tenant_id, connected_account, user, entity_type, template_id)
      end
    rescue StandardError => e
      Rails.logger.error "FetchWhatsappTemplateDetailsFromMetaJob | Error while Fetching Whatsapp Template Details for template #{template_id}: #{e.message}"
    ensure
      Rails.logger.info "FetchWhatsappTemplateDetailsFromMetaJob | Whatsapp Template synced successfully for template #{template_id} for tenant #{tenant_id}"
      connected_account.decrement!(:sync_whatsapp_template_active_job_count)
    end
  end

  private

  def contains_named_params?(parsed_response)
    parsed_response[:components].any? do |component|
      component[:text].to_s.match?(WHATSAPP_TEMPLATE_NAMED_PARAM_REGEX)
    end
  end

  def create_whatsapp_template(parsed_response, tenant_id, connected_account, user, entity_type, template_id)

    if WhatsappTemplate.exists?(whatsapp_template_id: template_id, tenant_id: tenant_id, entity_type: entity_type)
      Rails.logger.info "FetchWhatsappTemplateDetailsFromMetaJob | Skipping creation for template #{template_id}, entity #{entity_type} - already exists."
      return
    end

    template_attributes = Facebook::WhatsappTemplateHelper.parse_template_body(parsed_response, tenant_id, connected_account, user)

    whatsapp_template = WhatsappTemplate.new(template_attributes.merge(tenant_id: tenant_id, connected_account_id: connected_account.id, created_by: user, updated_by: user, entity_type: entity_type ))

    whatsapp_template.whatsapp_template_namespace = WhatsappTemplate.generate_namespace(parsed_response[:name])
    whatsapp_template.assign_attributes(variable_mappings_attributes: VariableMappingsService.new({ whatsapp_template: whatsapp_template }).prepare_variables_for_template)

    unless whatsapp_template.valid?
      raise(ExceptionHandler::InvalidDataError,"#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.model_errors', error: whatsapp_template.errors.full_messages.to_sentence)}")
    end

    whatsapp_template.save!
    Rails.logger.info "FetchWhatsappTemplateDetailsFromMetaJob | Whatsapp Template synced successfully for template #{template_id}, entity #{entity_type}, tenant #{tenant_id}"
  rescue StandardError => e
    Rails.logger.error "FetchWhatsappTemplateDetailsFromMetaJob | Error while saving template #{template_id} for entity #{entity_type}: #{e.message}"
  end
end
