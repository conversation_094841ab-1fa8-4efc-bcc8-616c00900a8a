class WhatsappMessageStatusRetryJob < ApplicationJob
  queue_as :default
  MAX_RETRIES = 3

  def perform(field_entry, retry_count)
    message = Message.find_by(remote_id: field_entry.dig('value', 'statuses', 0, 'id'))

    if message.present?
      WhatsappMessageStatusTransitionService.new(
        message,
        field_entry
      ).call
    elsif retry_count < MAX_RETRIES

      wait_time = 15 * (2**(retry_count))
      Rails.logger.error "Whatsapp Message with remote_id #{field_entry.dig('value', 'statuses', 0, 'id')} and sender_number #{field_entry.dig('value', 'metadata', 'display_phone_number')&.delete(' ')} still not found for message status update. Retrying in #{wait_time} seconds (attempt #{retry_count + 1})"
      WhatsappMessageStatusRetryJob.set(wait: wait_time.seconds).perform_later(field_entry, retry_count + 1)
    else
      Rails.logger.error "Whatsapp Message with remote_id #{field_entry.dig('value', 'statuses', 0, 'id')} and sender_number #{field_entry.dig('value', 'metadata', 'display_phone_number')&.delete(' ')} still not found for message status update after #{retry_count} attempts"
    end
  end
end
