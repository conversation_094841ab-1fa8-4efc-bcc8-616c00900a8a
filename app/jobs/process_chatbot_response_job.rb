# frozen_string_literal: true

class ProcessChatbotResponseJob < ApplicationJob
  queue_as :default

  def perform(payload)
    Rails.logger.info "ProcessChatbotResponseJob | Processing chatbot response for chatbot_conversation_id: #{payload['chatbotConversationId']}"
    @payload = payload.with_indifferent_access
    chatbot_conversation_id = @payload['chatbotConversationId']
    message_content = @payload['message']
    completed = @payload['completed']
    charge = @payload['charge']

    conversation = Conversation.find_by(chatbot_conversation_id: chatbot_conversation_id)

    if conversation.blank? && @payload['messageConversationId'].present?
      sleep(5) # Wait for 5 seconds before triggering first chatbot response to avoid entity not found issue
      conversation = Conversation.find_by(id: @payload['messageConversationId']) 
    end

    unless conversation
      Rails.logger.error "ProcessChatbotResponseJob | Conversation not found for chatbot_conversation_id: #{chatbot_conversation_id}"
      return
    end

    if conversation.chatbot_conversation_id.blank? && @payload['messageConversationId'].present?
      conversation.update!(chatbot_conversation_id: chatbot_conversation_id)
      conversation.update!(chatbot_conversation_completed: false)
      Rails.logger.info "ProcessChatbotResponseJob | Updated conversation #{conversation.id} with chatbot_conversation_id: #{chatbot_conversation_id} and chatbot_conversation_completed: false, Tenant: #{conversation.tenant_id}"
    end

    if conversation.chatbot_conversation_id.present? && conversation.chatbot_conversation_id != chatbot_conversation_id
      conversation.update!(chatbot_conversation_id: chatbot_conversation_id)
      conversation.update!(chatbot_conversation_completed: false)
      Rails.logger.info "ProcessChatbotResponseJob | New chatbot conversation id #{chatbot_conversation_id} detected Updated conversation #{conversation.id} with chatbot_conversation_id: #{chatbot_conversation_id} and chatbot_conversation_completed: false, Tenant: #{conversation.tenant_id}"
    end

    connected_account = ConnectedAccount.find_by(id: conversation.connected_account_id)
    unless connected_account
      Rails.logger.error "ProcessChatbotResponseJob | Connected account not found for conversation: #{conversation.id}"
      return
    end

    unless should_process_chatbot_response?(conversation)
      Rails.logger.info "ProcessChatbotResponseJob | Chatbot response processing skipped for conversation #{conversation.id}"
      return
    end

    if @payload['chatbotType'] == RULE_BASED_CHATBOT && @payload['nodeDetails'].blank?
      mark_as_completed(conversation) if completed
      Rails.logger.info "ProcessChatbotResponseJob | Chatbot response processing skipped for conversation #{conversation.id}, Error: Node details are missing"
      return
    end

    setup_thread_context(connected_account)

    ChatbotMessageService.new(conversation, connected_account, @payload).send_message

    if charge.to_f > 0
      handle_chatbot_billing(connected_account.tenant_id, connected_account, charge.to_f)
    end

    if completed
      mark_as_completed(conversation)
    end

    Rails.logger.info "ProcessChatbotResponseJob | Successfully processed chatbot response for conversation #{conversation.id}"
  rescue StandardError => e
    Rails.logger.error "ProcessChatbotResponseJob | Error processing chatbot response for chatbot_conversation_id #{chatbot_conversation_id}: #{e.message}"
  end

  private
  
  def mark_as_completed(conversation)
    if conversation.chatbot_conversation_completed == false
      conversation.ongoing_sub_conversation.update!(status: COMPLETED)
      conversation.update!(status: COMPLETED, chatbot_conversation_completed: true, chatbot_conversation_id: nil)
      Rails.logger.info "ProcessChatbotResponseJob | Marked chatbot conversation as completed for conversation #{conversation.id}"
    else
      Rails.logger.info "ProcessChatbotResponseJob | Chatbot conversation already marked as completed or conversation status is not in progress for conversation #{conversation.id}"
    end
  end
  
  def should_process_chatbot_response?(conversation)
    return false unless conversation.reload.chatbot_conversation_id.present?

    true
  end

  def send_chatbot_response_message(conversation, connected_account, message_type, message_body)
    session_message_params = {
      id: connected_account.id,
      conversation_id: conversation.id,
      message_type: message_type
    }

    if message_type == MEDIA_MESSAGE
      session_message_params[:media] = message_body
    else
      session_message_params[:message_body] = message_body
    end

    entity_details = get_entity_details_for_conversation(conversation)
    if entity_details.present?
      first_entity = entity_details.first
      session_message_params[:entity_id] = first_entity[:id]
      session_message_params[:entity_type] = first_entity[:entityType]
    end

    session_message = Message::SessionMessage.new(session_message_params)
    session_message.send_text

    Rails.logger.info "ProcessChatbotResponseJob | Chatbot response message sent for conversation #{conversation.id}"
  rescue StandardError => e
    Rails.logger.error "ProcessChatbotResponseJob | Error sending chatbot response message for conversation #{conversation.id}: #{e.message}"
  end

  def get_entity_details_for_conversation(conversation)
    conversation_lookups = ConversationLookUp.where(conversation_id: conversation.id)
    
    entity_details = conversation_lookups.map do |lookup|
      {
        id: lookup.look_up.entity_id,
        entityType: lookup.look_up.entity_type
      }
    end

    entity_details
  end

  def handle_chatbot_billing(tenant_id, connected_account, charge)
    WhatsappCreditsService.new.deduct_chatbot_credits(tenant_id, connected_account, charge)
    Rails.logger.info "ProcessChatbotResponseJob | Deducted chatbot credits: #{charge} for tenant: #{tenant_id}"
  rescue StandardError => e
    Rails.logger.error "ProcessChatbotResponseJob | Error handling chatbot billing for tenant #{tenant_id}: #{e.message}"
  end

  def setup_thread_context(connected_account)
    connected_account_created_by_user = connected_account.created_by
    admin_permissions = [
      {
        id: 1,
        name: 'lead',
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 2,
        name: 'contact',
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 3,
        name: 'sms',
        description: 'has access to sms',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          write: true,
          readAll: true
        }
      },
      {
        id: 4,
        name: 'user',
        description: 'has access to user',
        limits: -1,
        units: 'count',
        action: {
          read: true
        }
      }
    ]

    admin_token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
    thread = Thread.current
    thread[:token] = admin_token
    thread[:auth] = User::TokenParser.parse(admin_token)
    thread[:user] = connected_account_created_by_user
  end
end
