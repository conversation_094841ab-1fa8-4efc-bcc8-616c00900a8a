# frozen_string_literal: true

class InitiateChatbotConversationJob < ApplicationJob
  queue_as :default

  def perform(conversation_id, message_content, entity_details)
    Rails.logger.info "Starting chatbot conversation initiation for conversation #{conversation_id}"

    conversation = Conversation.find_by(id: conversation_id)
    unless conversation
      Rails.logger.error "InitiateChatbotConversationJob | Conversation not found: #{conversation_id}"
      return
    end

    connected_account = ConnectedAccount.find_by(id: conversation.connected_account_id)
    unless connected_account
      Rails.logger.error "InitiateChatbotConversationJob |Connected account not found: #{conversation.connected_account_id}"
      return
    end

    unless should_initiate_chatbot?(connected_account, conversation)
      Rails.logger.info "InitiateChatbotConversationJob | Chatbot initiation skipped for conversation #{conversation_id}"
      return
    end

    unless entity_details.present?
      Rails.logger.error "InitiateChatbotConversationJob | No entity details found for conversation #{conversation_id}"
      return
    end

    setup_thread_context(connected_account)

    chatbot_response = ChatbotService.new(
      message_content,
      entity_details,
      connected_account,
      @admin_token,
      conversation_id
    ).initiate_conversation

    if chatbot_response[:success]
      # We are storing chatbot conversation id and sending the chatbot generated response via CHATBOT_CONVERSATION_RESPONSE_EVENT
      Rails.logger.info "InitiateChatbotConversationJob | Chatbot conversation initiated successfully for conversation #{conversation_id}, Entity Details #{entity_details}"
    else
      Rails.logger.error "InitiateChatbotConversationJob | Failed to initiate chatbot conversation for conversation #{conversation_id}: #{chatbot_response[:error]}"
    end
  rescue StandardError => e
    Rails.logger.error "InitiateChatbotConversationJob | Error in InitiateChatbotConversationJob for conversation #{conversation_id}: #{e.message}"
  end

  private

  def should_initiate_chatbot?(connected_account, conversation)
    return false unless connected_account.is_chatbot_configured

    return false if conversation.chatbot_conversation_completed

    return false if conversation.chatbot_conversation_id.present?

    true
  end

  def get_entity_details_for_conversation(conversation)
    entity_details = []

    conversation.conversation_look_ups.includes(:look_up).each do |conversation_lookup|
      look_up = conversation_lookup.look_up
      entity_details << {
        id: look_up.entity_id,
        entityType: look_up.entity_type
      }
    end

    entity_details
  end

  # Note: Earlier in case of AI based chatbot, we were sending welcome message and first question here, but now we are sending the entire response via CHATBOT_CONVERSATION_RESPONSE_EVENT
  def send_chatbot_response_message(conversation, connected_account, message_content, entity_details)
    session_params = {
      id: connected_account.id,
      conversation_id: conversation.id,
      message_type: 'text',
      message_body: message_content
    }

    if entity_details.present?
      first_entity = entity_details.first
      session_params[:entity_id] = first_entity[:id]
      session_params[:entity_type] = first_entity[:entityType]
    end

    Message::SessionMessage.new(session_params).send_text

    Rails.logger.info "Chatbot response message sent for conversation #{conversation.id}"
  rescue StandardError => e
    Rails.logger.error "Error sending chatbot response message for conversation #{conversation.id}: #{e.message}"
  end

  def setup_thread_context(connected_account)
    connected_account_created_by_user = connected_account.created_by
    admin_permissions = [
      {
        id: 1,
        name: 'lead',
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 2,
        name: 'contact',
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 3,
        name: 'sms',
        description: 'has access to sms',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          write: true,
          readAll: true
        }
      },
      {
        id: 4,
        name: 'user',
        description: 'has access to user',
        limits: -1,
        units: 'count',
        action: {
          read: true
        }
      }
    ]

    @admin_token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
    thread = Thread.current
    thread[:token] = @admin_token
    thread[:auth] = User::TokenParser.parse(@admin_token)
    thread[:user] = connected_account_created_by_user
  end
end
