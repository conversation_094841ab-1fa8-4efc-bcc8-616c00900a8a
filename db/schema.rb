# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_09_25_121725) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "agent_users", force: :cascade do |t|
    t.bigint "connected_account_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.string "entity_type"
    t.index ["connected_account_id"], name: "index_agent_users_on_connected_account_id"
    t.index ["user_id"], name: "index_agent_users_on_user_id"
  end

  create_table "attachments", force: :cascade do |t|
    t.text "url"
    t.bigint "message_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "file_name"
    t.integer "size"
    t.index ["message_id"], name: "index_attachments_on_message_id"
  end

  create_table "chatbot_media", force: :cascade do |t|
    t.string "file_name"
    t.bigint "file_size"
    t.string "file_type"
    t.string "tenant_id", null: false
    t.string "chatbot_id"
    t.string "facebook_media_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "connected_account_id"
  end

  create_table "connected_accounts", force: :cascade do |t|
    t.string "access_token"
    t.string "name"
    t.string "waba_number"
    t.string "waba_id"
    t.string "status"
    t.string "phone_number_id"
    t.string "display_name"
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.text "entities_to_create", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_verified", default: true
    t.string "interakt_onboarding_status"
    t.string "interakt_isv_token"
    t.string "interakt_message"
    t.bigint "credits_revised_upto"
    t.bigint "deactivated_at"
    t.bigint "last_message_received_at", default: 0
    t.boolean "is_chatbot_configured", default: false
    t.integer "sync_whatsapp_template_active_job_count", default: 0
    t.index ["tenant_id"], name: "index_connected_accounts_on_tenant_id"
  end

  create_table "contact_deal_associations", force: :cascade do |t|
    t.bigint "contact_id", null: false
    t.bigint "deal_id", null: false
    t.bigint "tenant_id", null: false
    t.string "deal_name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["tenant_id", "contact_id", "deal_id"], name: "idx_contact_deal_associations_tenant_contact_deal_unique", unique: true
    t.index ["tenant_id", "contact_id"], name: "idx_contact_deal_associations_tenant_contact"
  end

  create_table "conversation_look_ups", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.bigint "look_up_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["conversation_id", "look_up_id"], name: "index_conversation_look_ups_unique_conversation_lookup", unique: true
    t.index ["conversation_id"], name: "index_conversation_look_ups_on_conversation_id"
    t.index ["look_up_id"], name: "index_conversation_look_ups_on_look_up_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "connected_account_id", null: false
    t.string "phone_number", null: false
    t.bigint "owner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "last_message_received_at"
    t.datetime "last_activity_at"
    t.datetime "deleted_at"
    t.string "chatbot_conversation_id"
    t.boolean "chatbot_conversation_completed", default: false
    t.string "status"
    t.index ["tenant_id"], name: "conversations_tenantId_idx"
  end

  create_table "field_mappings", force: :cascade do |t|
    t.string "entity_type"
    t.bigint "campaign"
    t.bigint "source"
    t.string "sub_source"
    t.string "utm_campaign"
    t.string "utm_content"
    t.string "utm_medium"
    t.string "utm_source"
    t.string "utm_term"
    t.bigint "connected_account_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["connected_account_id"], name: "index_field_mappings_on_connected_account_id"
  end

  create_table "look_ups", force: :cascade do |t|
    t.string "entity_type"
    t.bigint "entity_id"
    t.string "phone_number"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "name"
    t.bigint "tenant_id"
    t.bigint "owner_id"
  end

  create_table "message_look_ups", force: :cascade do |t|
    t.bigint "message_id", null: false
    t.bigint "look_up_id", null: false
    t.boolean "references"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "related", default: false
    t.boolean "recipient", default: false
    t.index ["look_up_id"], name: "index_message_look_ups_on_look_up_id"
    t.index ["message_id"], name: "index_message_look_ups_on_message_id"
  end

  create_table "messages", force: :cascade do |t|
    t.string "medium"
    t.bigint "owner_id", null: false
    t.integer "direction"
    t.text "content"
    t.datetime "sent_at"
    t.string "recipient_number"
    t.string "sender_number"
    t.integer "status"
    t.datetime "delivered_at"
    t.datetime "read_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.string "remote_id"
    t.string "message_type"
    t.string "status_message"
    t.bigint "connected_account_id"
    t.bigint "conversation_id"
    t.bigint "sub_conversation_id"
    t.text "plain_text_content"
    t.bigint "whatsapp_template_id"
    t.jsonb "metadata", default: {}
    t.datetime "deleted_at"
    t.jsonb "campaign_info", default: {}
    t.datetime "failed_at"
    t.jsonb "component_wise_content", default: []
    t.index ["connected_account_id"], name: "index_messages_on_connected_account_id"
    t.index ["conversation_id"], name: "idx_messages_conversation_not_deleted", where: "(deleted_at IS NULL)"
  end

  create_table "share_rules", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.bigint "entity_id"
    t.string "entity_type"
    t.bigint "from_id"
    t.string "from_type"
    t.bigint "to_id"
    t.string "to_type"
    t.bigint "share_rule_id"
    t.boolean "share_all_records", default: false
    t.jsonb "actions"
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "index_share_rules_on_created_by_id"
    t.index ["share_rule_id", "entity_type"], name: "index_share_rules_on_share_rule_id_and_entity_type", unique: true
    t.index ["updated_by_id"], name: "index_share_rules_on_updated_by_id"
  end

  create_table "sub_conversations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "connected_account_id", null: false
    t.bigint "conversation_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "status"
    t.index ["conversation_id"], name: "index_sub_conversations_on_conversation_id"
  end

  create_table "teams", force: :cascade do |t|
    t.string "name"
    t.bigint "tenant_id", null: false
    t.bigint "user_ids", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "template_media", force: :cascade do |t|
    t.string "whatsapp_file_handle"
    t.string "file_name"
    t.bigint "file_size"
    t.string "file_type"
    t.string "tenant_id", null: false
    t.bigint "whatsapp_template_component_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["whatsapp_template_component_id"], name: "index_template_media_on_whatsapp_template_component_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name"
    t.bigint "tenant_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "phone_number", limit: 20
    t.string "timezone"
    t.string "date_format"
  end

  create_table "variable_mappings", force: :cascade do |t|
    t.string "component_type"
    t.integer "template_variable"
    t.string "entity"
    t.string "internal_name"
    t.string "fallback_value"
    t.bigint "tenant_id", null: false
    t.bigint "whatsapp_template_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "parent_entity"
    t.string "field_type"
    t.index ["whatsapp_template_id"], name: "index_variable_mappings_on_whatsapp_template_id"
  end

  create_table "whatsapp_credit_histories", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "connected_account_id"
    t.string "entry_type"
    t.string "conversation_category"
    t.bigint "start_time"
    t.bigint "end_time"
    t.bigint "conversation_count"
    t.string "conversation_type"
    t.string "phone_number"
    t.string "country"
    t.float "value"
    t.float "balance"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["tenant_id", "conversation_category"], name: "whatsappcredithistories_tenantIdAndConversationCategory_idx"
    t.index ["tenant_id"], name: "whatsappcredithistories_tenantId_idx"
  end

  create_table "whatsapp_credits", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.float "total"
    t.float "consumed"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "credits_revised_at"
    t.float "parked", default: 0.0
    t.boolean "is_low_credits_email_sent", default: false
  end

  create_table "whatsapp_template_components", force: :cascade do |t|
    t.string "component_type"
    t.string "component_format"
    t.string "component_text"
    t.string "component_value"
    t.jsonb "content", default: {}
    t.bigint "tenant_id"
    t.integer "position"
    t.bigint "whatsapp_template_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "media_type"
    t.index ["whatsapp_template_id"], name: "index_whatsapp_template_components_on_whatsapp_template_id"
  end

  create_table "whatsapp_templates", force: :cascade do |t|
    t.bigint "connected_account_id", null: false
    t.string "entity_type"
    t.string "name"
    t.string "category"
    t.string "language"
    t.string "status"
    t.string "whatsapp_template_namespace"
    t.string "whatsapp_template_id"
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "reason"
    t.jsonb "additional_info"
    t.index ["connected_account_id"], name: "index_whatsapp_templates_on_connected_account_id"
    t.index ["tenant_id"], name: "whatsapptemplates_tenantId_idx"
  end

  add_foreign_key "agent_users", "connected_accounts"
  add_foreign_key "agent_users", "users"
  add_foreign_key "attachments", "messages"
  add_foreign_key "conversation_look_ups", "conversations"
  add_foreign_key "conversation_look_ups", "look_ups"
  add_foreign_key "field_mappings", "connected_accounts"
  add_foreign_key "message_look_ups", "look_ups"
  add_foreign_key "message_look_ups", "messages"
  add_foreign_key "messages", "connected_accounts"
  add_foreign_key "share_rules", "users", column: "created_by_id"
  add_foreign_key "share_rules", "users", column: "updated_by_id"
  add_foreign_key "sub_conversations", "conversations"
  add_foreign_key "template_media", "whatsapp_template_components"
  add_foreign_key "variable_mappings", "whatsapp_templates"
  add_foreign_key "whatsapp_template_components", "whatsapp_templates"
  add_foreign_key "whatsapp_templates", "connected_accounts"
end
