# frozen_string_literal: true

LOOKUP_LEAD = 'lead'
LOOKUP_DEAL = 'deal'
LOOKUP_CONTACT = 'contact'
LOOKUP_USER = 'user'
LOOKUP_TYPES = [LOOKUP_LEAD, <PERSON><PERSON><PERSON><PERSON>_DEAL, <PERSON><PERSON><PERSON><PERSON>_CONTACT, <PERSON><PERSON><PERSON><PERSON>_USER].freeze
MESSAGE_EXCHANGE = 'ex.message'
SCHEDULER_EXCHANGE = 'ex.scheduler'
DEAL_EXCHANGE = 'ex.deal'
USER_EXCHANGE = 'ex.iam'
CONFIG_EXCHANGE = 'ex.config'
WORKFLOW_EXCHANGE = 'ex.workflow'
SMS_ACTION = 'sms'.freeze
BULK_ACTIONS_EXCHANGE = 'ex.bulkActions'

SALES_EXCHANGE = 'ex.sales'
LEAD_DELETED_EVENT = 'sales.lead.deleted'
LEAD_DELETED_QUEUE = 'q.message.sales.lead.deleted'
CONTACT_DELETED_EVENT = 'sales.contact.deleted'
CONTACT_DELETED_QUEUE = 'q.message.sales.contact.deleted'
DEAL_DELETED_EVENT = 'deal.deleted'
DEAL_DELETED_QUEUE = 'q.message.deal.deleted'
DEAL_NAME_UPDATED_EVENT = 'deal.name.updated'
DEAL_NAME_UPDATED_QUEUE = 'q.deal.name.updated.message'
DEAL_UPDATED_V2_EVENT = 'deal.updated.v2'
DEAL_UPDATED_V2_QUEUE = 'q.deal.updated.v2.message'
DEAL_CREATED_V2_EVENT = 'deal.created.v2'
DEAL_CREATED_V2_QUEUE = 'q.deal.created.v2.message'
USER_PHONE_UPDATED_EVENT = 'user.phone.updated'
USER_PHONE_UPDATED_QUEUE = 'q.message.user.phone.updated'
SCHEDULER_3AM_EVENT = 'scheduler.3am'
SCHEDULER_3AM_QUEUE = 'q.scheduler.3am.message'
SCHEDULER_1AM_EVENT = 'scheduler.1am'
SCHEDULER_1AM_QUEUE = 'q.scheduler.1am.message'

LEAD_OWNER_UPDATED_EVENT = 'sales.lead.owner_updated'
LEAD_OWNER_UPDATED_QUEUE = 'q.sales.lead.owner_updated.message'
CONTACT_OWNER_UPDATED_EVENT = 'sales.contact.owner_updated'
CONTACT_OWNER_UPDATED_QUEUE = 'q.sales.contact.owner_updated.message'
DEAL_OWNER_UPDATED_EVENT = 'deal.reassigned'
DEAL_OWNER_UPDATED_QUEUE = 'q.deal.reassigned.message'

USAGE_LIMIT_CHANGED_EVENT = 'usage.limit.changed'
USAGE_LIMIT_CHANGED_QUEUE = 'q.message.usage.limit.changed'

USAGE_PUBLISHER_EVENT = 'tenant.usage.collected'
USAGE_SCHEDULER_EVENT = 'scheduler.collect.usage'
USAGE_SCHEDULER_QUEUE = 'q.message.scheduler.collect.usage'
MESSAGE_RECEIVED_FROM_ENTITY_EVENT = 'message.received.from.entity'
WHATSAPP_MESSAGE_STATUS_UPDATED = 'whatsapp.message.status.updated'
MESSAGE_WHATSAPP_CREDITS_ABOUT_TO_EXPIRE = 'message.whatsapp.credits.about.to.expire'
MESSAGE_WORKFLOW_EXECUTION_STATUS_UPDATE = 'message.workflow_execution.status.update'
MESSAGE_WHATSAPP_TEMPLATE_NAME_UPDATED = 'message.whatsapp.template.name.updated'
WHATSAPP_MESSAGE_STATUS_UPDATED_V2 = 'whatsapp.message.status.updated.v2'
WHATSAPP_MESSAGE_CREATED_V2 = 'whatsapp.message.created.v2'

USER_NAME_UPDATED_EVENT = 'user.name.updated'
USER_NAME_UPDATED_QUEUE = 'q.message.user.name.updated'

JWT_KEY = 'test'
MESSAGE_CREATED = 'message.created'
MESSAGE_DELETED = 'message.deleted'
WHATSAPP_TEMPLATE_STATUS_UPDATED = 'whatsapp.template.status.updated'

WHATSAPP_WEBHOOK_EVENT = 'message.whatsapp.webhook'
WHATSAPP_WEBHOOK_QUEUE = 'q.message.whatsapp.webhook.message'

INTERAKT_WEBHOOK_EVENT = 'message.interakt.webhook'
INTERAKT_WEBHOOK_QUEUE = 'q.message.interakt.webhook.message'

WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT = 'workflow.whatsapp.message.send'
WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE = 'q.workflow.whatsapp.message.send'

SMS = 'sms'
WHATSAPP = 'whatsapp'
CHAT = 'chat'
WHATSAPP_BUSINESS = 'whatsapp_business'

NEW = 'NEW'
IN_PROGRESS = 'IN_PROGRESS'
COMPLETED = 'COMPLETED'

if Rails.env.development? || Rails.env.test?
  SERVICE_IAM = 'http://localhost:8081'
  SERVICE_SALES = 'http://localhost:8082'
  SERVICE_SEARCH = 'http://localhost:8083'
  S3_ATTACHMENT_BUCKET = 'qa-message-attachment'
  SERVICE_CONFIG = 'http://localhost:8086'
  SERVICE_DEAL = 'http://localhost:8090'
  SERVICE_WHATSAPP_CHATBOT = 'http://localhost:8091'
  FACEBOOK_WEBHOOK_CHALLENGE_RESPONSE = 'challenge_response'
elsif Rails.env.staging?
  SERVICE_IAM = 'http://sd-iam'
  SERVICE_SALES = 'http://sd-sales'
  SERVICE_SEARCH = 'http://sd-search'
  S3_ATTACHMENT_BUCKET = ENV['S3_ATTACHMENT_BUCKET']
  SERVICE_CONFIG = 'http://sd-config'
  SERVICE_DEAL = 'http://sd-deal'
  SERVICE_WHATSAPP_CHATBOT = 'http://sd-whatsapp-chatbot'
  FACEBOOK_WEBHOOK_CHALLENGE_RESPONSE = 'e422514f-706e-4fad-9e9e-e274416b2ae2'
end

FACEBOOK_HOST = 'https://graph.facebook.com'
FACEBOOK_API_VERSION = 'v19.0'
DRAFT = 'draft'
PENDING = 'pending'
ACTIVE = 'active'
INACTIVE = 'inactive'

INTERAKT_HOST = 'https://api.interakt.ai'
INTERAKT_API_HOST = 'https://amped-express.interakt.ai'
INTERAKT_API_VERSION = 'v17.0'

DOWNGRADED_PLANS = ['embark', 'explore-monthly', 'explore-annual'].freeze

LEAD_USER_LOOKUP_FIELDS = %w[createdBy updatedBy convertedBy importedBy ownerId].freeze
CONTACT_USER_LOOKUP_FIELDS = %w[createdBy updatedBy importedBy ownerId].freeze
DEAL_USER_LOOKUP_FIELDS = %w[createdBy updatedBy ownedBy importedBy].freeze

LEAD_EXCLUDED_FIELDS = ['forecastingType', 'isNew', 'recordActions', 'metaData', 'dnd', 'photoUrls', 'convertedAt', 'timezone', 'requirementCurrency'].freeze
CONTACT_EXCLUDED_FIELDS =  ['forecastingType', 'isNew', 'recordActions', 'metaData', 'dnd', 'timezone', 'stakeholder'].freeze
DEAL_EXCLUDED_FIELDS = ['forecastingType', 'isNew', 'recordActions', 'metaData', 'taskDueOn', 'price', 'quantity', 'discount', 'units'].freeze

EXCLUDED_VARIABLE_FIELD_TYPES = ['CHECKBOX', 'RICH_TEXT'].freeze

RELATIVE_FILTERS_LIST = [
  'today', 'yesterday', 'tomorrow',
  'last_seven_days', 'next_seven_days',
  'last_fifteen_days', 'next_fifteen_days',
  'last_thirty_days', 'next_thirty_days',
  'current_week', 'last_week', 'next_week',
  'current_month', 'last_month', 'next_month',
  'current_quarter', 'last_quarter', 'next_quarter',
  'current_year', 'last_year', 'next_year',
  'week_to_date', 'month_to_date', 'quarter_to_date', 'year_to_date',
  'before_current_date_and_time', 'after_current_date_and_time',
  'last_n_days', 'next_n_days'
].freeze

USER = 'USER'.freeze
TEAM = 'TEAM'.freeze
TEAM_UPDATED_V2_EVENT = 'team.updated.v2'.freeze
TEAM_UPDATED_V2_QUEUE = 'q.team.updated.v2.message'.freeze
SHARE_RULE_CREATED_V2_EVENT = 'sharerule.created.v2'.freeze
SHARE_RULE_CREATED_V2_QUEUE = 'q.sharerule.created.message'.freeze
VALIDATION_FAILED_ERROR = 'Validation failed: '
SHARE_RULE_UPDATED_V2_EVENT = 'sharerule.updated.v2'.freeze
SHARE_RULE_UPDATED_V2_QUEUE = 'q.sharerule.updated.message'.freeze
SHARE_RULE_DELETED_V2_EVENT = 'sharerule.deleted.v2'.freeze
SHARE_RULE_DELETED_V2_QUEUE = 'q.sharerule.deleted.message'.freeze

LEAD_CREATED_V2_EVENT = 'sales.lead.created.v2'.freeze
LEAD_CREATED_V2_QUEUE = 'q.sales.lead.created.v2.message'.freeze
CONTACT_CREATED_V2_EVENT = 'sales.contact.created.v2'.freeze
CONTACT_CREATED_V2_QUEUE = 'q.sales.contact.created.v2.message'.freeze
LEAD_PHONE_NUMBERS_UPDATED_V2_EVENT = 'sales.lead.phoneNumbers.updated.v2'.freeze
LEAD_PHONE_NUMBERS_UPDATED_V2_QUEUE = 'q.sales.lead.phoneNumbers.updated.v2.message'.freeze
CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT = 'sales.contact.phoneNumbers.updated.v2'.freeze
CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE = 'q.sales.contact.phoneNumbers.updated.v2.message'.freeze

LEAD_NAME_UPDATED_EVENT = 'sales.lead.name.updated'.freeze
LEAD_NAME_UPDATED_QUEUE = 'q.sales.lead.name.updated.message'.freeze
CONTACT_NAME_UPDATED_EVENT = 'contact.name.updated'.freeze
CONTACT_NAME_UPDATED_QUEUE = 'q.contact.name.updated.message'.freeze
USER_UPDATED_V2_EVENT = 'user.updated.v2'.freeze
USER_UPDATED_V2_QUEUE = 'q.user.updated.v2.message'.freeze
SUCCESS = 'SUCCESS'
FAILED = 'FAILED'
WHATSAPP_ENTITY_MESSAGE_STATUS_TRACK = 'whatsapp.entity.message.status.track'.freeze
BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_EVENT = 'bulkActions.whatsapp.campaign.activity.aborted'.freeze
BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_QUEUE = 'q.bulkActions.whatsapp.campaign.activity.aborted.message'.freeze

# Chatbot events
CHATBOT_EXCHANGE = 'ex.whatsappChatbot'.freeze
CHATBOT_STATUS_UPDATED_EVENT = 'chatbot.status.updated'.freeze
CHATBOT_STATUS_UPDATED_QUEUE = 'q.chatbot.status.updated.message'.freeze
CHATBOT_CONVERSATION_RESPONSE_EVENT = 'chatbot.conversation.response'.freeze
CHATBOT_CONVERSATION_RESPONSE_QUEUE = 'q.chatbot.conversation.response.message'.freeze
CHATBOT_USER_RESPONSE_EVENT = 'message.chatbot.user.response'.freeze
CHATBOT_CONVERSATION_COMPLETE_EVENT = 'message.chatbot.conversation.completed'.freeze
INCOMING = 'incoming'.freeze
CHATBOT_MEDIA_DELETED_EVENT = 'chatbot.media.deleted'.freeze
CHATBOT_MEDIA_DELETED_QUEUE = 'q.chatbot.media.deleted.message'.freeze
QUESTION = 'question'.freeze
SEND_MESSAGE = 'sendMessage'.freeze
INTERACTIVE_BUTTON = 'buttons'.freeze
INTERACTIVE_LIST = 'list'.freeze
INTERACTIVE_CTA_URL = 'cta_url'.freeze
