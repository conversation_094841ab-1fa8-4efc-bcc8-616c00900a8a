# frozen_string_literal: true

MARKETING = 'MARKETING'
UTILITY = 'UTILITY'
SERVICE = 'SERVICE'
CHATBOT = 'CHATBOT'

DRAFT_STATUS = 'DRAFT'
SUBMITTING = 'SUBMITTING'
PENDING_STATUS = 'PENDING'
APPROVED = 'APPROVED'
REJECTED = 'REJECTED'
FLAGGED = 'FLAGGED'
PAUSED = 'PAUSED'
PENDING_DELETION = 'PENDING_DELETION'
INACTIVE_TEMPLATE = 'INACTIVE'
TEMPLATE_CATEGORIES = [MARKETING, UTILITY]

CREDITS_ADDED = 'CREDITS_ADDED'
CREDITS_DEDUCTED = 'CREDITS_DEDUCTED'
CREDITS_PARKED = 'CREDITS_PARKED'
CREDITS_UNPARKED = 'CREDITS_UNPARKED'

REQUEST_SENT = 'REQUEST_SENT'
WABA_ONBOARDED = 'WABA_ONBOARDED'
WABA_ONBOARDING_FAILED = 'WABA_ONBOARDING_FAILED'

HEADER = 'HEADER'
BODY = 'BODY'
FOOTER = 'FOOTER'
BUTTON = 'BUTTON'
BUTTONS = 'BUTTONS'

BUTTON_URL = 'BUTTON_URL'
BUTTON_COPY_CODE = 'BUTTON_COPY_CODE'

TEXT = 'TEXT'
PHONE_NUMBER = 'PHONE_NUMBER'
URL = 'URL'
QUICK_REPLY = 'QUICK_REPLY'
COPY_CODE = 'COPY_CODE'
AUDIO = 'AUDIO'
IMAGE = 'IMAGE'
VIDEO = 'VIDEO'
DOCUMENT = 'DOCUMENT'
HEADER_COMPONENT_FORMATS = [TEXT, IMAGE, VIDEO, DOCUMENT]
BUTTON_COMPONENT_FORMATS = [PHONE_NUMBER, URL, QUICK_REPLY, COPY_CODE]
TEMPLATE_MEDIA_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/png', 'video/mp4']
TEMPLATE_MEDIA_FILE_EXTENSIONS = ['.pdf', '.jpeg', '.jpg', '.png', '.mp4']
STATIC = 'STATIC'
DYNAMIC = 'DYNAMIC'

ALLOWED_FILE_TYPES = {
  audio: ['.aac', '.amr', '.mp3', '.m4a', '.ogg'],
  document: ['.txt', '.xls', '.xlsx', '.doc', '.docx', '.ppt', '.pptx', '.pdf'],
  image: ['.jpeg', '.png', '.jpg'],
  video: ['.3gp', '.mp4']
}.with_indifferent_access

MAX_FILE_SIZE = {
  audio: 1_67_77_216,
  document: 10_48_57_600,
  image: 52_42_880,
  video: 1_67_77_216
}.with_indifferent_access

WHATSAPP_TEMPLATE_FILTERABLE_FIELDS = %w[id name category connectedAccount language status entityType createdAt updatedAt createdBy updatedBy multi_field]
WHATSAPP_TEMPLATE_SORTABLE_FIELDS = %w[id createdAt updatedAt name category status language entityType]

LEAD_VARIABLE_ENTITIES = %w[createdBy updatedBy convertedBy importedBy ownerId lead tenant marketplace].freeze
CONTACT_VARIABLE_ENTITIES = %w[createdBy updatedBy importedBy ownerId contact tenant marketplace].freeze
DEAL_VARIABLE_ENTITIES = %w[createdBy updatedBy ownedBy deal tenant marketplace].freeze
VARIABLE_FIELD_TYPES = %w[TEXT_FIELD PICK_LIST PHONE EMAIL TOGGLE URL DATE_PICKER DATETIME_PICKER LOOK_UP NUMBER MULTI_PICKLIST CHECKBOX PIPELINE PARAGRAPH_TEXT MONEY PIPELINE_STAGE].freeze

WHATSAPP_TEMPLATE_VARIABLE_REGEX = /\{\{[1-9]+[0-9]*\}\}/
WHATSAPP_TEMPLATE_NAMED_PARAM_REGEX = /\{\{[a-zA-Z_]+\}\}/
WHATSAPP_CREDIT_HISTORY_FILTERABLE_FIELDS = %w[connectedAccount startTime]
SUPPORTED_MEDIA_TYPES = ['image', 'video', 'audio', 'document'].freeze

MARKUP = 0.1
MIN_BALANCE_FOR_BULK = 100
RULE_BASED_CHATBOT = 'RULE'
AI_BASED_CHATBOT = 'AI'
TEXT_MESSAGE = 'text'
MEDIA_MESSAGE = 'media'

WHATSAPP_RETRYABLE_ERROR_CODES = [131049, 131000, 131016, 133004]
