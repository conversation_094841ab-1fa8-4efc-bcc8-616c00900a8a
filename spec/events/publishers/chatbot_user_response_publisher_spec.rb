# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::ChatbotUserResponsePublisher do
  describe '.call' do
    let(:message) { 'Hi, I need your help!' }
    let(:chatbot_conversation_id) { '1234567890abcdef' }
    let(:completed) { false }
    let(:is_media_message) { true }

    before do
      allow(PublishEvent).to receive(:new).and_return(double(call: true))
      allow(Rails.logger).to receive(:info)
    end

    context 'with all parameters' do
      it 'creates and publishes the correct event' do
        expect(Event::ChatbotUserResponse).to receive(:new).with(
          message: message,
          chatbot_conversation_id: chatbot_conversation_id,
          completed: completed,
          is_media_message: is_media_message
        ).and_call_original

        expect(PublishEvent).to receive(:new).with(
          an_instance_of(Event::ChatbotUserResponse),
          MESSAGE_EXCHANGE
        )

        described_class.call(
          message: message,
          chatbot_conversation_id: chatbot_conversation_id,
          completed: completed,
          is_media_message: is_media_message
        )
      end

      it 'logs successful publishing' do
        expect(Rails.logger).to receive(:info).with(/Publishing chatbot user response event/)
        expect(Rails.logger).to receive(:info).with(/Successfully published chatbot user response event/)

        described_class.call(
          message: message,
          chatbot_conversation_id: chatbot_conversation_id,
          completed: completed,
          is_media_message: is_media_message
        )
      end
    end

    context 'with minimal parameters' do
      it 'creates event with default values' do
        expect(Event::ChatbotUserResponse).to receive(:new).with(
          message: message,
          chatbot_conversation_id: nil,
          completed: false,
          is_media_message: false
        ).and_call_original

        described_class.call(message: message)
      end
    end

    context 'when an error occurs' do
      before do
        allow(PublishEvent).to receive(:new).and_raise(StandardError.new('Test error'))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Error publishing chatbot user response event/)

        described_class.call(message: message)
      end
    end
  end

  describe '#call' do
    let(:message) { 'Hi, I need your help!' }
    let(:chatbot_conversation_id) { '1234567890abcdef' }
    let(:completed) { false }
    let(:publisher) { described_class.new(message: message, chatbot_conversation_id: chatbot_conversation_id, completed: completed) }
    let(:is_media_message) { false }
    
    before do
      allow(PublishEvent).to receive(:new).and_return(double(call: true))
      allow(Rails.logger).to receive(:info)
    end

    it 'creates the correct event object' do
      event_double = double('Event::ChatbotUserResponse')
      expect(Event::ChatbotUserResponse).to receive(:new).with(
        message: message,
        chatbot_conversation_id: chatbot_conversation_id,
        completed: completed,
        is_media_message: is_media_message
      ).and_return(event_double)

      expect(PublishEvent).to receive(:new).with(event_double, MESSAGE_EXCHANGE)

      publisher.call
    end

    it 'uses the correct exchange' do
      expect(PublishEvent).to receive(:new).with(
        an_instance_of(Event::ChatbotUserResponse),
        MESSAGE_EXCHANGE
      )

      publisher.call
    end
  end
end
