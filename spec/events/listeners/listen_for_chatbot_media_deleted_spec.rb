# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForChatbotMediaDeleted do
  describe '.listen' do
    context 'when listening to chatbot media deleted event' do
      let(:payload) do
        {
          'chatbotId' => 123,
          'tenantId' => 3440,
          'mediaIds' => [1, 2, 3]
        }.to_json
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(CHATBOT_EXCHANGE, CHATBOT_MEDIA_DELETED_EVENT, CHATBOT_MEDIA_DELETED_QUEUE)
          .and_yield(payload)
      end

      it 'logs the received message and processes the payload' do
        expect(Rails.logger).to receive(:info).with("Received message for #{CHATBOT_MEDIA_DELETED_EVENT} #{payload}")
        expect_any_instance_of(described_class).to receive(:process)
        described_class.listen
      end
    end
  end

  describe '#process' do
    let(:user){ create(:user) }
    let(:connected_account) { create(:connected_account, is_chatbot_configured: true, tenant_id: user.tenant_id) }
    let(:chatbot_media) { create(:chatbot_media, connected_account_id: connected_account.id, chatbot_id: 123, tenant_id: user.tenant_id) }
    let(:valid_payload) do
      {
        'chatbotId' => 123,
        'tenantId' => user.tenant_id,
        'mediaIds' => [1111, 2222, 3333]
      }
    end

    context 'when payload is valid' do
      context 'when unused chatbot media exists' do
        before do
          valid_payload['mediaIds'] = [chatbot_media.id, 4444, 5555]
          s3_instance = instance_double(S3::DeleteFileFromS3)
          allow(S3::DeleteFileFromS3).to receive(:new).with([chatbot_media.file_name], S3_ATTACHMENT_BUCKET).and_return(s3_instance)
          allow(s3_instance).to receive(:call).and_return(nil)
        end

        it 'deletes that particular chatbot media from s3 and DB' do
          expect(ChatbotMedia.count).to eq(1)
          described_class.new(valid_payload).process
          expect(ChatbotMedia.count).to eq(0)
        end
      end

      context 'when unused chatbot media does not exist' do
        before do
          chatbot_media
          valid_payload['mediaIds'] = [3333, 4444, 5555]
        end

        it 'does not delete any chatbot media from s3 or DB' do
          expect(ChatbotMedia.count).to eq(1)
          described_class.new(valid_payload).process
          expect(ChatbotMedia.count).to eq(1)
        end
      end
    end
  end
end
