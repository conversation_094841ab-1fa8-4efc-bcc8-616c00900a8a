# frozen_string_literal: true

require 'rails_helper'

Dir["#{Rails.root}/app/events/listeners/*"].each do |dir_or_file|
  if !dir_or_file.ends_with?('rb')
    Dir["#{dir_or_file}/*.rb"].each do |f|
      require(f)
    end
  else
   require(dir_or_file)
  end
end

RSpec.describe Listeners::Base do
  describe '.call' do
    it 'calls all listeners' do
      expect(Listeners::ListenForContactDelete).to receive(:listen)
      expect(Listeners::ListenForDealDelete).to receive(:listen)
      expect(Listeners::ListenForLeadDelete).to receive(:listen)
      expect(Listeners::ListenForUsageLimitChanged).to receive(:listen)
      expect(Listeners::ListenForUsageScheduler).to receive(:listen)
      expect(Listeners::ListenForUserNameUpdate).to receive(:listen)
      expect(Listeners::ListenForUserPhoneUpdate).to receive(:listen)
      expect(Listeners::ListenForWhatsappWebhook).to receive(:listen)
      expect(Listeners::ListenForInteraktWebhook).to receive(:listen)
      expect(Listeners::ListenForLeadReassign).to receive(:listen)
      expect(Listeners::ListenForContactReassign).to receive(:listen)
      expect(Listeners::ListenForDealReassign).to receive(:listen)
      expect(Listeners::ListenForDailyScheduler).to receive(:listen)
      expect(Listeners::ListenFor1amScheduler).to receive(:listen)
      expect(Listeners::ListenForTeamUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForShareRuleCreatedV2).to receive(:listen)
      expect(Listeners::ListenForShareRuleUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForShareRuleDeletedV2).to receive(:listen)
      expect(Listeners::ListenForLeadCreatedV2).to receive(:listen)
      expect(Listeners::ListenForContactCreatedV2).to receive(:listen)
      expect(Listeners::ListenForLeadPhoneNumbersUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForContactPhoneNumbersUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForLeadNameUpdated).to receive(:listen)
      expect(Listeners::ListenForContactNameUpdated).to receive(:listen)
      expect(Listeners::ListenForWorkflowSendWhatsappMessage).to receive(:listen)
      expect(Listeners::ListenForUserUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForDealNameUpdated).to receive(:listen)
      expect(Listeners::ListenForDealUpdatedV2).to receive(:listen)
      expect(Listeners::ListenForDealCreatedV2).to receive(:listen)
      expect(Listeners::ListenForChatbotStatusUpdated).to receive(:listen)
      expect(Listeners::ListenForChatbotConversationResponse).to receive(:listen)
      expect(Listeners::ListenForChatbotMediaDeleted).to receive(:listen)
      expect(Listeners::ListenForBulkActionsWhatsappCampaignActivityAborted).to receive(:listen)
      described_class.call
    end
  end
end
