# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForWorkflowSendWhatsappMessage do
  describe '#call' do
    before do
      @user = create(:user, tenant_id: 75, timezone: 'Asia/Calcutta', date_format: 'MMM D, YYYY [at] h:mm a')
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic DEAL_EXCHANGE
    end

    def stub_user_search_request(user_ids, valid_auth_token)
      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][2]['id'] = @user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: user_ids.join(',')
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})
    end

    def stub_interakt_message_request(to, connected_account, whatsapp_template)
      stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
        body: {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'template',
          template: {
            name: whatsapp_template.whatsapp_template_namespace,
            language: {
              code: 'en'
            },
            components: [
              {
                type: 'header',
                parameters: [
                  {
                    type: 'text',
                    text: 'lead first name'
                  }
                ]
              },
              {
                type: 'body',
                parameters: [
                  {
                    type: 'text',
                    text: 'lead first name'
                  },
                  {
                    type: 'text',
                    text: '+91 **********, +91 **********'
                  },
                  {
                    type: 'text',
                    text: 'Mr'
                  },
                  {
                    type: 'text',
                    text: '<EMAIL>, <EMAIL>'
                  },
                  {
                    type: 'text',
                    text: 'https://linkedin.url'
                  },
                  {
                    type: 'text',
                    text: 'Default Lead Pipeline'
                  },
                  {
                    type: 'text',
                    text: 'Open'
                  },
                  {
                    type: 'text',
                    text: '2102'
                  },
                  {
                    type: 'text',
                    text: 'Jun 04,2024'
                  },
                  {
                    type: 'text',
                    text: 'Jun 03,2024 at12:00 pm IST'
                  },
                  {
                    type: 'text',
                    text: 'pick3, asdb, pick2'
                  },
                  {
                    type: 'text',
                    text: 'FALLEN_BACK'
                  }
                ]
              },
              {
                type: 'button',
                sub_type: 'copy_code',
                index: 2,
                parameters: [
                  {
                    type: 'coupon_code',
                    coupon_code: 'Shubham'
                  }
                ]
              },
              {
                type: 'button',
                sub_type: 'url',
                index: 3,
                parameters: [
                  {
                    type: 'text',
                    text: 'Account%20name'
                  }
                ]
              },
              {
                type: 'button',
                sub_type: 'quick_reply',
                index: 4,
                parameters: [
                  {
                    type: 'payload',
                    payload: 'Unsubcribe from Promos'
                  }
                ]
              }
            ]
          }
        }.to_json,
        headers: {
          'x-access-token': 'partner-token',
          'x-waba-id': connected_account.waba_id,
          'Content-Type'=>'application/json',
        }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
    end

    def stub_interakt_message_request_for_deal(to, connected_account, whatsapp_template)
      stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
        body: {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'template',
          template: {
            name: whatsapp_template.whatsapp_template_namespace,
            language: {
              code: 'en'
            },
            components: [
              {
                type: 'header',
                parameters: [
                  {
                    type: 'text',
                    text: 'Neuro link'
                  }
                ]
              },
              {
                type: 'body',
                parameters: [
                  {
                    type: 'text',
                    text: 'Open'
                  }
                ]
              }
            ]
          }
        }.to_json,
        headers: {
          'x-access-token': 'partner-token',
          'x-waba-id': connected_account.waba_id,
          'Content-Type'=>'application/json',
        }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
    end

    def stub_usage_api(token)
      stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{token}",
        }
      ).to_return(status: 200, body: {
            records: { used: 100, total: 1000 },
            storage: { used: 0.5, total: 2.0 }
          }.to_json, headers: {})
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(WORKFLOW_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'workflow send whatsapp message event' do
        let(:routing_key)              { WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE }
        let(:payload) { file_fixture('listeners/workflow-send-whatsapp-message.json').read }
        let(:metadata) { file_fixture('listeners/workflow-send-whatsapp-message-metadata.json').read }

        let(:connected_account) { create(:connected_account, created_by: @user) }
        let(:whatsapp_template) { create(:whatsapp_template, id: 3232, connected_account: connected_account, status: 'APPROVED') }
        let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: @user.tenant_id, total: 1000, consumed: 0) }
        let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: @user.tenant_id, phone_number: '+91**********', owner_id: @user.id) }
        let(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: @user.tenant_id, conversation_id: conversation.id) }
        let(:valid_auth_token){ build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }

        before do
          whatsapp_template
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(WORKFLOW_EXCHANGE, WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT, WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE)
            .and_yield(payload.to_s, JSON.parse(metadata))

          stub_request(:get, "http://localhost:8081/v1/users/4010").
          with(
            headers: {
            'Authorization'=>'Bearer '+ valid_auth_token.token
            }).
          to_return(status: 200, body: {"id": @user.id, "firstName": "Jane", "lastName": "Doe", "email": {"primary": true,"value": "<EMAIL>"}, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"**********","dialCode":"+91","primary":true}], "timezone": "Asia/Calcutta", "dateFormat": "MMM D, YYYY [at] h:mm a"}.to_json, headers: {})

          stub_request(:get, "http://localhost:8081/v1/tenants").with(
            headers: {
            'Accept'=>'*/*',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

          stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
            headers: {
            'Accept'=>'*/*',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
          stub_usage_api(valid_auth_token.token)
        end

        context 'when credits are not present' do
          it 'should publish workflow execution status update event with error' do
            event_details = nil
            expect(PublishEvent).to receive(:call) do |*args|
              event_details = args.first
            end

            Listeners::ListenForWorkflowSendWhatsappMessage.listen

            expect(JSON.parse(event_details.to_json)).to eq({
              "eventId"=>74745,
              "status"=>"FAILED",
              "statusCode"=>422,
              "executionDetails"=>
              {
                "statuses"=>
                [{
                  "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                  "errorCode"=>"022028",
                  "errorMessage"=>"Insufficient whatsapp credits balance for bulk or workflow action",
                  "status"=>"FAILED"
                }]
              }
            })
          end
        end

        context 'when credits are present' do
          before do
            whatsapp_credit
            whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, entity: 'tenant', internal_name: 'accountName', parent_entity: 'lead')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
            create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.staticUrl.com', position: 1)
          end

          context 'when conversation is present' do
            before do
              conversation
              sub_conversation
              stub_user_search_request([4010, @user.id], valid_auth_token)
            end

            context 'when send to is RECORD_PRIMARY_PHONE_NUMBER' do
              before do
                stub_interakt_message_request('+91**********', connected_account, whatsapp_template)
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              end

              it 'sends message to primary phone number' do
                event_details = nil
                expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                  event_details = args.first
                end

                expect {
                  Listeners::ListenForWorkflowSendWhatsappMessage.listen
                }.to change(Message, :count).by(1)
                .and change(Conversation, :count).by(0)

                message = Message.last
                expect(message.owner_id).to eq(conversation.owner_id)
                expect(whatsapp_credit.reload.parked).to eq(0.86)

                expect(JSON.parse(event_details.to_json)).to eq({
                  "eventId"=>74745,
                  "status"=>"SUCCESS",
                  "statusCode"=>200,
                  "executionDetails"=>
                  {
                    "statuses"=>
                    [{
                      "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                      "errorCode"=>nil,
                      "errorMessage"=>nil,
                      "status"=>"SUCCESS"
                    }]
                  }
                })
              end
            end
          end

          context 'when conversation is not present' do
            context 'when send to is RECORD_ALL_PHONE_NUMBERS' do
              before do
                
                payload = file_fixture('listeners/workflow-send-whatsapp-message.json').read
                payload = JSON.parse(payload)
                payload['messageDetail']['to'] = [{
                  'type': 'RECORD_ALL_PHONE_NUMBERS',
                  'name': 'Record all phone numbers'
                  }]
                metadata = file_fixture('listeners/workflow-send-whatsapp-message-metadata.json').read
                allow(RabbitmqConnection).to receive(:subscribe)
                .with(WORKFLOW_EXCHANGE, WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT, WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE)
                .and_yield(payload.to_json, JSON.parse(metadata))
                stub_interakt_message_request('+91**********', connected_account, whatsapp_template)
                stub_interakt_message_request('+91**********', connected_account, whatsapp_template)

                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated)).twice
              end

              context 'when conversation are not present for all phone_numbers' do
                before do
                  stub_user_search_request([4010, 4010], valid_auth_token)
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
                end

                it "creates conversations & sends message to all phone numbers" do
                  event_details = nil
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                    event_details = args.first
                  end

                  expect {
                    Listeners::ListenForWorkflowSendWhatsappMessage.listen
                  }.to change(Conversation, :count).by(2)
                  .and change(SubConversation, :count).by(2)
                  .and change(Message, :count).by(2)

                  message_1 = Message.first
                  message_2 = Message.last
                  expect(message_1.owner_id).to eq(Conversation.first.owner_id)
                  expect(message_1.owner_id).to eq(4010)
                  expect(message_2.owner_id).to eq(Conversation.last.owner_id)
                  expect(message_2.owner_id).to eq(4010)

                  expect(whatsapp_credit.reload.parked).to eq(1.72)

                  expect(JSON.parse(event_details.to_json)).to eq({
                    "eventId"=>74745,
                    "status"=>"SUCCESS",
                    "statusCode"=>200,
                    "executionDetails"=>
                    {
                      "statuses"=>
                      [
                        {
                          "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                          "errorCode"=>nil,
                          "errorMessage"=>nil,
                          "status"=>"SUCCESS"
                        },
                        {
                          "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>false},
                          "errorCode"=>nil,
                          "errorMessage"=>nil,
                          "status"=>"SUCCESS"
                        }
                      ]
                    }
                  })
                end
              end

              context 'when conversation is present for first phone_number' do
                before do
                  stub_user_search_request([4010, 4010], valid_auth_token)
                  stub_user_search_request([4010, @user.id], valid_auth_token)
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
                  expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
                end

                it "creates conversations for second phone number & sends message to all phone numbers" do
                  event_details = nil
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                    event_details = args.first
                  end

                  conversation
                  sub_conversation

                  expect {
                    Listeners::ListenForWorkflowSendWhatsappMessage.listen
                  }.to change(Conversation, :count).by(1)
                  .and change(SubConversation, :count).by(1)
                  .and change(Message, :count).by(2)

                  message_1 = Message.first
                  message_2 = Message.last
                  expect(message_1.owner_id).to eq(Conversation.first.owner_id)

                  expect(message_2.owner_id).to eq(Conversation.last.owner_id)
                  expect(message_2.owner_id).to eq(4010)

                  expect(whatsapp_credit.reload.parked).to eq(1.72)

                  expect(JSON.parse(event_details.to_json)).to eq({
                    "eventId"=>74745,
                    "status"=>"SUCCESS",
                    "statusCode"=>200,
                    "executionDetails"=>
                    {
                      "statuses"=>
                      [
                        {
                          "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                          "errorCode"=>nil,
                          "errorMessage"=>nil,
                          "status"=>"SUCCESS"
                        },
                        {
                          "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>false},
                          "errorCode"=>nil,
                          "errorMessage"=>nil,
                          "status"=>"SUCCESS"
                        }
                      ]
                    }
                  })
                end
              end
            end
          end

          context 'when chat bot is in progress' do
            before do
              conversation.update!(chatbot_conversation_completed: false, chatbot_conversation_id: 1)
            end

            it 'should not send message and publish error event' do
              event_details = nil
              expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                event_details = args.first
              end

              expect {
                Listeners::ListenForWorkflowSendWhatsappMessage.listen
              }.to change(Message, :count).by(0)
              .and change(Conversation, :count).by(0)

              expect(JSON.parse(event_details.to_json)).to eq({
                "eventId"=>74745,
                "status"=>"FAILED",
                "statusCode"=>409,
                "executionDetails"=>
                {
                  "statuses"=>
                  [{
                    "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                    "errorCode"=>"022034",
                    "errorMessage"=>"Cannot send message as chatbot is in progress",
                    "status"=>"FAILED"
                  }]
                }
              })
            end
          end

          context 'when connected account is NOT active' do
            before do
              connected_account.update(status: 'inactive')
            end

            it 'should not send message' do
              event_details = nil
              expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                event_details = args.first
              end

              expect {
                Listeners::ListenForWorkflowSendWhatsappMessage.listen
              }.to change(Message, :count).by(0)
              .and change(Conversation, :count).by(0)

              expect(JSON.parse(event_details.to_json)).to eq({
                "eventId"=>74745,
                "status"=>"FAILED",
                "statusCode"=>422,
                "executionDetails"=>
                {
                  "statuses"=>
                  [{
                    "phone_number"=>{"code"=>"IN", "dialCode"=>"+91", "type"=>"MOBILE", "value"=>"**********", "primary"=>true},
                    "errorCode"=>"022030",
                    "errorMessage"=>"Connected account is not active",
                    "status"=>"FAILED"
                  }]
                }
              })
            end
          end
        end

        context 'when entity type is DEAL' do
          let(:deal_payload) { file_fixture('listeners/workflow-send-whatsapp-message-deal.json').read }
          let(:recent_contacts_response) {
            {
              'content' => [
                {
                  'id' => 381652,
                  'name' => 'Gojo Satoru',
                  'phoneNumbers' => [
                    {
                      'code' => 'IN',
                      'dialCode' => '+91',
                      'type' => 'MOBILE',
                      'value' => '**********',
                      'primary' => true
                    },
                    {
                      'code' => 'IN',
                      'dialCode' => '+91',
                      'type' => 'MOBILE',
                      'value' => '**********',
                      'primary' => false
                    }
                  ],
                  'ownerId' => 11094
                },
                {
                  'id' => 381653,
                  'name' => 'Kugisaki Nobara',
                  'phoneNumbers' => [
                    {
                      'code' => 'IN',
                      'dialCode' => '+91',
                      'type' => 'MOBILE',
                      'value' => '9942398764',
                      'primary' => true
                    }
                  ],
                  'ownerId' => 11094
                },
                {
                  'id' => 381654,
                  'name' => 'No phone contact',
                  'phoneNumbers' => nil,
                  'ownerId' => 11094
                }
              ]
            }
          }

          before do
            stub_request(:post, "http://localhost:8083/v1/search/contact?sort=updatedAt,desc&page=0&size=10")
            .with(
              body: {
                fields: %w[id phoneNumbers firstName lastName ownerId],
                jsonRule: {
                  rules: [
                    {
                      operator: 'in',
                      id: 'id',
                      field: 'id',
                      type: 'double',
                      value: "381652,381653,381654"
                    }
                  ],
                  condition: 'AND',
                  valid: true
                }
              }.to_json)
              .to_return(status: 200, body: recent_contacts_response.to_json, headers: {})

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(WORKFLOW_EXCHANGE, WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT, WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE)
              .and_yield(deal_payload.to_s, JSON.parse(metadata))

            stub_user_search_request([11094], valid_auth_token)

            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '9942398764' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '9942398764' })

            whatsapp_credit
            whatsapp_template.update(entity_type: LOOKUP_DEAL)
            whatsapp_template.components.where(component_type: ['BUTTON', 'FOOTER']).destroy_all

            whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}}')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'name')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'pipelineStage', field_type: 'PIPELINE_STAGE', fallback_value: nil)

            stub_interakt_message_request_for_deal('+91**********', connected_account, whatsapp_template)
            stub_interakt_message_request_for_deal('+************', connected_account, whatsapp_template)
          end

          it 'fetches recent contacts and sends messages to their phone numbers' do
            event_details = nil
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated)).twice
            expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
              event_details = args.first
            end

            expect {
              Listeners::ListenForWorkflowSendWhatsappMessage.listen
            }.to change(Conversation, :count).by(2)
            .and change(SubConversation, :count).by(2)
            .and change(Message, :count).by(2)

            expect(whatsapp_credit.reload.parked).to eq(1.72)

            expect(JSON.parse(event_details.to_json)).to eq({
              "eventId" => 83658,
              "status" => "SUCCESS",
              "statusCode" => 200,
              "executionDetails" => {
                "statuses" => [
                  {
                    "phone_number" => {"code" => "IN", "dialCode" => "+91", "type" => "MOBILE", "value" => "**********", "primary" => true},
                    "errorCode" => nil,
                    "errorMessage" => nil,
                    "status" => "SUCCESS"
                  },
                  {
                    "phone_number" => {"code" => "IN", "dialCode" => "+91", "type" => "MOBILE", "value" => "9942398764", "primary" => true},
                    "errorCode" => nil,
                    "errorMessage" => nil,
                    "status" => "SUCCESS"
                  }
                ]
              }
            })
          end

          context 'when sending message to all phone numbers' do
            before do
              deal_payload_with_all_numbers = JSON(deal_payload)
              deal_payload_with_all_numbers['messageDetail']['to'] = [{
                'name' => 'Record all phone numbers',
                'type' => 'RECORD_ALL_PHONE_NUMBERS'
              }]
              allow(RabbitmqConnection).to receive(:subscribe)
              .with(WORKFLOW_EXCHANGE, WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT, WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE)
              .and_yield(JSON(deal_payload_with_all_numbers).to_s, JSON.parse(metadata))

            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
            stub_interakt_message_request_for_deal('+91**********', connected_account, whatsapp_template)
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated)).thrice
            end

            it 'should send message to all phone numbers' do
              event_details = nil
              expect(PublishEvent).to receive(:call).with(instance_of(Event::WorkflowExecutionStatusUpdate), 'ex.workflow', 'workflow') do |*args|
                event_details = args.first
              end

              expect {
                Listeners::ListenForWorkflowSendWhatsappMessage.listen
              }.to change(Conversation, :count).by(3)
              .and change(SubConversation, :count).by(3)
              .and change(Message, :count).by(3)

              expect(whatsapp_credit.reload.parked).to eq(2.58)

              expect(JSON.parse(event_details.to_json)).to eq({
                "eventId" => 83658,
                "status" => "SUCCESS",
                "statusCode" => 200,
                "executionDetails" => {
                  "statuses" => [
                    {
                      "phone_number" => {"code" => "IN", "dialCode" => "+91", "type" => "MOBILE", "value" => "**********", "primary" => true},
                      "errorCode" => nil,
                      "errorMessage" => nil,
                      "status" => "SUCCESS"
                    },
                    {
                      "phone_number" => {"code" => "IN", "dialCode" => "+91", "type" => "MOBILE", "value" => "**********", "primary" => false},
                      "errorCode" => nil,
                      "errorMessage" => nil,
                      "status" => "SUCCESS"
                    },
                    {
                      "phone_number" => {"code" => "IN", "dialCode" => "+91", "type" => "MOBILE", "value" => "9942398764", "primary" => true},
                      "errorCode" => nil,
                      "errorMessage" => nil,
                      "status" => "SUCCESS"
                    }
                  ]
                }
              })
            end
          end
        end
      end
    end
  end
end
