# fronzen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForBulkActionsWhatsappCampaignActivityAborted do
  describe '#call' do
    let(:user) { create(:user) }
    let(:message) { create(:message, tenant_id: user.tenant_id, status: 'sending') }
    let(:message_2) { create(:message, tenant_id: user.tenant_id, status: 'sending') }
    let(:message_3) { create(:message, tenant_id: user.tenant_id, status: 'delivered') }

    let(:payload) { { 'campaignId' => 123, 'tenantId' => user.tenant_id, 'messageIds' => [message.id, message_2.id] }.to_json }

    context 'valid input' do
      before do
        allow(RabbitmqConnection).to receive(:subscribe)
         .with(BULK_ACTIONS_EXCHANGE, BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_EVENT, BULK_ACTIONS_WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED_QUEUE)
         .and_yield(payload.to_s)
      end

      it 'aborts sending messages' do
        described_class.listen

        expect(message.reload.status).to eq('failed')
        expect(message_2.reload.status).to eq('failed')
        expect(message_3.reload.status).to eq('delivered')

        expect(message.reload.status_message).to eq('Campaign aborted')
        expect(message_2.reload.status_message).to eq('Campaign aborted')
      end
    end
  end
end
