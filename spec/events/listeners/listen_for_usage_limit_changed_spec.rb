# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForUsageLimitChanged do
  describe '.listen' do
    let(:payload) { JSON.parse(file_fixture('usage-limit-changed-payload.json').read) }
    let(:tenant_id) { payload['tenantId'] }

    before do
      Rails.cache.clear
      # Cache some data for the tenant to test cache clearing
      Rails.cache.write("tenant_usage:#{tenant_id}", { records: { used: 100, total: 1000 } }, expires_in: 5.minutes)
    end

    after do
      Rails.cache.clear
    end

    context 'when plan downgraded' do
      before do
        payload['planId'] = 'embark'
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'deactivates connected accounts' do
        expect_any_instance_of(ConnectedAccountService).to receive(:deactivate_all_accounts)
        described_class.listen
      end

      it 'clears tenant usage cache' do
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be true
        expect(TenantUsageService).to receive(:clear_cache).with(tenant_id).and_call_original
        described_class.listen
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be false
      end
    end

    context 'when add on expired' do
      before do
        payload['usageEntityLimits'].reject! { |usage_entity| usage_entity['usageEntity'] == 'WHATSAPP_BUSINESS' }
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'deactivates connected accounts' do
        expect_any_instance_of(ConnectedAccountService).to receive(:deactivate_all_accounts)
        described_class.listen
      end
    end

    context 'when usage limit changed and some other entity changed' do
      before do
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'does not add credits to account' do
        expect_any_instance_of(ConnectedAccountService).not_to receive(:deactivate_all_accounts)
        described_class.listen
      end
    end

    context 'when credits are added' do
      before do
        payload['usageEntityLimits'].push({ usageEntity: 'WHATSAPP_CREDITS', limit: 1000, isCharged: true })
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'adds the credits to account' do
        expect_any_instance_of(WhatsappCreditsService).to receive(:add).with(4240, 1000)
        described_class.listen
      end

      it 'clears tenant usage cache' do
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be true
        expect(TenantUsageService).to receive(:clear_cache).with(tenant_id)
        described_class.listen
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be false
      end
    end

    context 'when cache clearing is tested independently' do
      before do
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'always clears cache regardless of other processing' do
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be true
        expect(Rails.logger).to receive(:info).with("TenantUsageService - Cache cleared for tenant_id: #{tenant_id}")
        described_class.listen
        expect(Rails.cache.exist?("tenant_usage:#{tenant_id}")).to be false
      end
    end
  end
end
