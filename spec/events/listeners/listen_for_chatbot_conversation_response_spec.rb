# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForChatbotConversationResponse do
  describe '#process' do
    let(:chatbot_conversation_id) { 'conv-uuid' }
    let(:message_content) { 'Thank you, <PERSON>! What\'s your email address?' }
    let(:completed) { false }
    let(:charge) { 0 }
    let(:payload) do
      {
        'chatbotConversationId' => chatbot_conversation_id,
        'message' => message_content,
        'completed' => completed,
        'charge' => charge
      }
    end

    before do
      allow(ProcessChatbotResponseJob).to receive(:perform_later)
    end

    context 'when payload is valid' do
      it 'schedules ProcessChatbotResponseJob with correct parameters' do
        expect(ProcessChatbotResponseJob).to receive(:perform_later).with(payload)

        described_class.new(payload).process
      end

      it 'logs successful scheduling' do
        expect(Rails.logger).to receive(:info).with(/Processing chatbot conversation response/)
        expect(Rails.logger).to receive(:info).with(/Scheduled ProcessChatbotResponseJob/)

        described_class.new(payload).process
      end

      context 'when charge is provided' do
        let(:charge) { 0.5 }

        it 'schedules ProcessChatbotResponseJob with charge parameter' do
          payload['charge'] = charge
          expect(ProcessChatbotResponseJob).to receive(:perform_later).with(payload)

          described_class.new(payload).process
        end
      end
    end

    context 'when chatbotConversationId is missing' do
      let(:payload) { { 'message' => message_content, 'completed' => completed, 'charge' => charge } }

      it 'does not schedule the job' do
        expect(ProcessChatbotResponseJob).not_to receive(:perform_later)

        described_class.new(payload).process
      end

      it 'logs error for missing chatbot_conversation_id' do
        expect(Rails.logger).to receive(:error).with(/Missing chatbot_conversation_id/)

        described_class.new(payload).process
      end
    end

    context 'when an error occurs during processing' do
      before do
        allow(ProcessChatbotResponseJob).to receive(:perform_later).and_raise(StandardError.new('Test error'))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Error processing chatbot conversation response/)

        described_class.new(payload).process
      end
    end
  end

  describe '.listen' do
    it 'subscribes to the correct RabbitMQ exchange and queue' do
      expect(RabbitmqConnection).to receive(:subscribe).with(
        CHATBOT_EXCHANGE,
        CHATBOT_CONVERSATION_RESPONSE_EVENT,
        CHATBOT_CONVERSATION_RESPONSE_QUEUE
      )

      described_class.listen
    end
  end
end
