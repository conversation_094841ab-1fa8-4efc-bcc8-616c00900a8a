# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForChatbotStatusUpdated do
  describe '.listen' do
    context 'when listening to chatbot status updated event' do
      let(:connected_account) { create(:connected_account) }
      let(:valid_payload) do
        {
          'status' => 'ACTIVE',
          'connectedAccount' => {
            'id' => connected_account.id,
            'name' => 'Test WhatsApp Account'
          }
        }
      end
      let(:payload) { valid_payload.to_json }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(CHATBOT_EXCHANGE, CHATBOT_STATUS_UPDATED_EVENT, CHATBOT_STATUS_UPDATED_QUEUE)
          .and_yield(payload)
      end

      it 'logs the received message and processes the payload' do
        expect(Rails.logger).to receive(:info).with("Received message for #{CHATBOT_STATUS_UPDATED_EVENT} #{payload}")
        expect_any_instance_of(described_class).to receive(:process)
        described_class.listen
      end
    end
  end

  describe '#process' do
    let(:connected_account) { create(:connected_account, is_chatbot_configured: false) }
    let(:valid_payload) do
      {
        'status' => 'ACTIVE',
        'connectedAccount' => {
          'id' => connected_account.id,
          'name' => 'Test WhatsApp Account'
        }
      }
    end

    subject { described_class.new(valid_payload) }

    context 'when payload is valid and connected account exists' do
      context 'when status is ACTIVE' do
        it 'sets is_chatbot_configured to true' do
          expect { subject.process }.to change { connected_account.reload.is_chatbot_configured }.from(false).to(true)
        end

        it 'logs successful update' do
          expect(Rails.logger).to receive(:info).with("Updated chatbot configuration for connected account #{connected_account.id} to true")
          subject.process
        end
      end

      context 'when status is DRAFT' do
        let(:valid_payload) do
          {
            'status' => 'DRAFT',
            'connectedAccount' => {
              'id' => connected_account.id,
              'name' => 'Test WhatsApp Account'
            }
          }
        end

        before { connected_account.update!(is_chatbot_configured: true) }

        it 'sets is_chatbot_configured to false' do
          expect { subject.process }.to change { connected_account.reload.is_chatbot_configured }.from(true).to(false)
        end

        it 'logs successful update' do
          expect(Rails.logger).to receive(:info).with("Updated chatbot configuration for connected account #{connected_account.id} to false")
          subject.process
        end
      end

      context 'when status is INACTIVE' do
        let(:valid_payload) do
          {
            'status' => 'INACTIVE',
            'connectedAccount' => {
              'id' => connected_account.id,
              'name' => 'Test WhatsApp Account'
            }
          }
        end

        before { connected_account.update!(is_chatbot_configured: true) }

        it 'sets is_chatbot_configured to false' do
          expect { subject.process }.to change { connected_account.reload.is_chatbot_configured }.from(true).to(false)
        end

        it 'logs successful update' do
          expect(Rails.logger).to receive(:info).with("Updated chatbot configuration for connected account #{connected_account.id} to false")
          subject.process
        end
      end
    end

    context 'when connected account does not exist' do
      let(:invalid_payload) do
        {
          'status' => 'ACTIVE',
          'connectedAccount' => {
            'id' => 99999,
            'name' => 'Non-existent Account'
          }
        }
      end

      subject { described_class.new(invalid_payload) }

      it 'does not raise an error' do
        expect { subject.process }.not_to raise_error
      end

      it 'does not update any connected account' do
        expect { subject.process }.not_to change { ConnectedAccount.count }
      end
    end

    context 'when connected account update fails' do
      let(:connected_account_with_errors) { create(:connected_account, is_chatbot_configured: false) }
      let(:error_payload) do
        {
          'status' => 'ACTIVE',
          'connectedAccount' => {
            'id' => connected_account_with_errors.id,
            'name' => 'Test WhatsApp Account'
          }
        }
      end

      subject { described_class.new(error_payload) }

      before do
        errors_double = double('errors')
        allow(errors_double).to receive(:full_messages).and_return(['Validation failed'])
        allow(connected_account_with_errors).to receive(:update).and_return(false)
        allow(connected_account_with_errors).to receive(:errors).and_return(errors_double)
        allow(ConnectedAccount).to receive(:find_by).and_return(connected_account_with_errors)
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with("Failed to update chatbot configuration for connected account #{connected_account_with_errors.id}: Validation failed")
        subject.process
      end
    end

    context 'when payload is invalid' do
      context 'when status is missing' do
        let(:invalid_payload) do
          {
            'connectedAccount' => {
              'id' => connected_account.id,
              'name' => 'Test WhatsApp Account'
            }
          }
        end

        subject { described_class.new(invalid_payload) }

        it 'does not update the connected account' do
          expect { subject.process }.not_to change { connected_account.reload.is_chatbot_configured }
        end
      end

      context 'when status is invalid' do
        let(:invalid_payload) do
          {
            'status' => 'INVALID_STATUS',
            'connectedAccount' => {
              'id' => connected_account.id,
              'name' => 'Test WhatsApp Account'
            }
          }
        end

        subject { described_class.new(invalid_payload) }

        it 'does not update the connected account' do
          expect { subject.process }.not_to change { connected_account.reload.is_chatbot_configured }
        end
      end

      context 'when connectedAccount is missing' do
        let(:invalid_payload) do
          {
            'status' => 'ACTIVE'
          }
        end

        subject { described_class.new(invalid_payload) }

        it 'does not update any connected account' do
          expect { subject.process }.not_to change { ConnectedAccount.count }
        end
      end

      context 'when connectedAccount id is missing' do
        let(:invalid_payload) do
          {
            'status' => 'ACTIVE',
            'connectedAccount' => {
              'name' => 'Test WhatsApp Account'
            }
          }
        end

        subject { described_class.new(invalid_payload) }

        it 'does not update any connected account' do
          expect { subject.process }.not_to change { ConnectedAccount.count }
        end
      end
    end
  end

  describe '#valid_payload?' do
    let(:connected_account) { create(:connected_account) }

    context 'with valid payload' do
      let(:valid_payload) do
        {
          'status' => 'ACTIVE',
          'connectedAccount' => {
            'id' => connected_account.id,
            'name' => 'Test WhatsApp Account'
          }
        }
      end

      subject { described_class.new(valid_payload) }

      it 'returns true' do
        expect(subject.send(:valid_payload?)).to be true
      end
    end

    context 'with invalid status' do
      let(:invalid_payload) do
        {
          'status' => 'UNKNOWN',
          'connectedAccount' => {
            'id' => connected_account.id,
            'name' => 'Test WhatsApp Account'
          }
        }
      end

      subject { described_class.new(invalid_payload) }

      it 'returns false' do
        expect(subject.send(:valid_payload?)).to be false
      end
    end
  end
end
