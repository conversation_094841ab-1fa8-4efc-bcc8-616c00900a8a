# frozen_string_literal: true

FactoryBot.define do
  factory :connected_account do
    access_token { ConnectedAccount.encrypt("#{rand(10000).to_s.rjust(4)}9jAin708tQSTkBlaFxAumtnkRwYGj4p0wQU/d3IlkT0=") }
    display_name { Faker::Internet.username }
    name { "Name #{display_name}" }
    waba_id { "***********#{rand(10000).to_s.ljust(4, '0')}" }
    waba_number { Faker::PhoneNumber.cell_phone_in_e164 }
    phone_number_id { Faker::Number.number }
    association :created_by, factory: :user
    updated_by { created_by }
    tenant_id { created_by.tenant_id }
    entities_to_create { ['lead', 'contact'] }
    status { ACTIVE }
    interakt_onboarding_status { 'WABA_ONBOARDED' }
    is_chatbot_configured { false }
  end
end
