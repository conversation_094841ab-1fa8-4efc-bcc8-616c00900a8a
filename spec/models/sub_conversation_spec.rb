# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SubConversation, type: :model do
  let(:conversation) { create(:conversation) }
  let(:sub_conversation) { create(:sub_conversation, conversation_id: conversation.id) }

  describe 'associations' do
    it do
      should belong_to(:conversation)
    end
  end

  describe 'validations' do
    context 'when status is not valid' do 
      it 'should be invalid' do
        sub_conversation.status = "invalid_status"
        expect(sub_conversation).to be_invalid
      end
    end
  end
end
