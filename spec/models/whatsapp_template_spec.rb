# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappTemplate do
  describe 'validations' do
    let(:whatsapp_template) { build(:whatsapp_template) }

    it 'is valid' do
      expect(whatsapp_template).to be_valid
    end

    context 'when name is blank' do
      before { whatsapp_template.name = '' }

      it 'validates presence of name' do
        expect(whatsapp_template).to be_invalid
      end
    end

    context 'when tenant id is blank' do
      before { whatsapp_template.tenant_id = '' }

      it 'validates presence of tenant id' do
        expect(whatsapp_template).to be_invalid
      end
    end

    context 'when name, entity type, connected account and whatsapp template namespace is not unique' do
      before do
        template = WhatsappTemplate.new(
          tenant_id: whatsapp_template.tenant_id,
          entity_type: whatsapp_template.entity_type,
          connected_account: whatsapp_template.connected_account,
          name: 'unique_name_123',
          category: 'MARKETING',
          language: 'en',
          status: 'DRAFT',
          whatsapp_template_namespace: 'unique_namespace_123',
          created_by: whatsapp_template.created_by,
          updated_by: whatsapp_template.updated_by
        )
        template.save
      end
      
      it 'validates name, entity type, connected account and whatsapp template namespace' do
        duplicate = WhatsappTemplate.new(
          tenant_id: whatsapp_template.tenant_id,
          entity_type: whatsapp_template.entity_type,
          connected_account: whatsapp_template.connected_account,
          name: 'unique_name_123',
          category: 'MARKETING',
          language: 'en',
          status: 'DRAFT',
          whatsapp_template_namespace: 'unique_namespace_123',
          created_by: whatsapp_template.created_by,
          updated_by: whatsapp_template.updated_by
        )
        expect(duplicate.save).to be false
        expect(duplicate.errors.full_messages.to_sentence).to eq('Name has already been taken and Whatsapp template namespace has already been taken')
      end
    end

    context 'when name is not unique but different entity_type' do
      before do
        create(:whatsapp_template,
               tenant_id: whatsapp_template.tenant_id,
               entity_type: LOOKUP_CONTACT,
               connected_account: whatsapp_template.connected_account,
               name: whatsapp_template.name)
      end

      it 'allows duplicates with different entity_type' do
        expect(whatsapp_template).to be_valid
      end
    end

    context 'when name is not unique but different connected_account_id' do
      before do
        create(:whatsapp_template,
               tenant_id: whatsapp_template.tenant_id,
               entity_type: whatsapp_template.entity_type,
               connected_account: create(:connected_account),
               name: whatsapp_template.name)
      end

      it 'allows duplicates with different connected_account_id' do
        expect(whatsapp_template).to be_valid
      end
    end

    context 'when whatsapp template namespace is not unique but different entity_type' do
      before do
        create(:whatsapp_template,
               tenant_id: whatsapp_template.tenant_id,
               entity_type: 'contact',
               connected_account: whatsapp_template.connected_account,
               whatsapp_template_namespace: whatsapp_template.whatsapp_template_namespace)
      end

      it 'allows duplicates with different entity_type' do
        expect(whatsapp_template).to be_valid
      end
    end

    context 'when whatsapp template namespace is not unique but different connected_account_id' do
      before do
        create(:whatsapp_template,
               tenant_id: whatsapp_template.tenant_id,
               entity_type: whatsapp_template.entity_type,
               connected_account: create(:connected_account),
               whatsapp_template_namespace: whatsapp_template.whatsapp_template_namespace)
      end

      it 'allows duplicates with different connected_account_id' do
        expect(whatsapp_template).to be_valid
      end
    end

    context 'when invalid template namespace format' do
      before { whatsapp_template.whatsapp_template_namespace = 'A' }

      it 'validates format' do
        expect(whatsapp_template).to be_invalid
        expect(whatsapp_template.errors.full_messages.to_sentence).to eq('Whatsapp template namespace invalid format. Only alphanumeric and underscore allowed.')
      end
    end

    context 'when entity type is invalid' do
      before { whatsapp_template.entity_type = 'company' }

      it 'validates entity type inclusion' do
        expect(whatsapp_template).to be_invalid
        expect(whatsapp_template.errors.full_messages.to_sentence).to eq('Entity type is not included in the list')
      end
    end

    context 'when category is invalid' do
      before { whatsapp_template.category = 'AUTHENTICATION' }

      it 'validates category inclusion' do
        expect(whatsapp_template).to be_invalid
        expect(whatsapp_template.errors.full_messages.to_sentence).to eq('Category is not included in the list')
      end
    end

    context 'when status is invalid' do
      before { whatsapp_template.status = 'invalid' }

      it 'validates status inclusion' do
        expect(whatsapp_template).to be_invalid
        expect(whatsapp_template.errors.full_messages.to_sentence).to eq('Status is not included in the list')
      end
    end
  end

  describe 'associations' do
    it 'has many components' do
      whatsapp_template_components = described_class.reflect_on_association(:components)
      expect(whatsapp_template_components.macro).to eq(:has_many)
      expect(whatsapp_template_components.options).to eq({ class_name: 'WhatsappTemplateComponent', autosave: true })
    end

    it 'has many variable_mappings' do
      variable_mappings_relation = described_class.reflect_on_association(:variable_mappings)
      expect(variable_mappings_relation.macro).to eq(:has_many)
    end

    it 'belongs to connected account' do
      whatsapp_template_connected_accounts = described_class.reflect_on_association(:connected_account)
      expect(whatsapp_template_connected_accounts.macro).to eq(:belongs_to)
    end

    it 'belongs to created by' do
      whatsapp_template_created_by = described_class.reflect_on_association(:created_by)
      expect(whatsapp_template_created_by.macro).to eq(:belongs_to)
      expect(whatsapp_template_created_by.options).to eq({ class_name: 'User' })
    end

    it 'belongs to updated by' do
      whatsapp_template_updated_by = described_class.reflect_on_association(:updated_by)
      expect(whatsapp_template_updated_by.macro).to eq(:belongs_to)
      expect(whatsapp_template_updated_by.options).to eq({ class_name: 'User' })
    end
  end

  describe '.generate_namespace' do
    it 'returns only alphanumeric with underscore' do
      expect(described_class.generate_namespace('some text $%^3456')).to eq('some_text_3456')
    end
  end

  describe '#extract_variables' do
    let(:whatsapp_template) { create(:whatsapp_template) }

    context 'when only header and body have variables' do
      before { whatsapp_template.components.where(component_format: [COPY_CODE, URL]).delete_all }

      it 'returns variable hash' do
        expect(whatsapp_template.extract_variables).to eq({ 'HEADER' => [1], 'BODY' => [1, 2, 3] })
      end
    end

    context 'when copy code button is present' do
      before { whatsapp_template.components.find_by(component_format: URL).delete }

      it 'returns variable hash with copy code key' do
        expect(whatsapp_template.extract_variables).to eq({ 'HEADER' => [1], 'BODY' => [1, 2, 3], 'BUTTON_COPY_CODE' => [1] })
      end
    end

    context 'when url button is present with variable' do
      before { whatsapp_template.components.find_by(component_format: COPY_CODE).delete }

      it 'returns variable hash with url key' do
        expect(whatsapp_template.extract_variables).to eq({ 'HEADER' => [1], 'BODY' => [1, 2, 3], 'BUTTON_URL' => [1] })
      end
    end

    context 'when two url buttons are present with one variable each' do
      before do
        whatsapp_template.components.find_by(component_format: COPY_CODE).delete
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 2)
      end

      it 'returns variable hash with url key and 2 variables' do
        expect(whatsapp_template.extract_variables).to eq({ 'HEADER' => [1], 'BODY' => [1, 2, 3], 'BUTTON_URL' => [1, 2] })
      end
    end

    context 'when header text is blank' do
      before do
        whatsapp_template.components.find_by(component_type: HEADER).update_column(:component_text, nil)
      end

      it 'returns no response for header' do
        expect(whatsapp_template.extract_variables).to eq({ 'BODY' => [1, 2, 3], 'BUTTON_COPY_CODE' => [1], 'BUTTON_URL' => [1] })
      end
    end

    context 'when url text is blank' do
      before do
        whatsapp_template.components.find_by(component_format: COPY_CODE).delete
        whatsapp_template.components.find_by(component_format: URL).update_column(:component_value, nil)
      end

      it 'returns no response for url' do
        expect(whatsapp_template.extract_variables).to eq({ 'HEADER' => [1], 'BODY' => [1, 2, 3] })
      end
    end
  end
end
