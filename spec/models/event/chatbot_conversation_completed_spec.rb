# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::ChatbotConversationCompleted do
  let(:message_conversation_id) { '123' }
  let(:chatbot_conversation_id) { '456' }
  let(:event) { described_class.new(messageConversationId: message_conversation_id, chatbotConversationId: chatbot_conversation_id) }

  describe '#initialize' do
    it 'initializes with message_conversation_id and chatbot_conversation_id' do
      expect(event.instance_variable_get(:@conversation_id)).to eq(message_conversation_id)
      expect(event.instance_variable_get(:@chatbot_conversation_id)).to eq(chatbot_conversation_id)
    end

    it 'raises ArgumentError when required arguments are missing' do
      expect { described_class.new(messageConversationId: message_conversation_id) }.to raise_error(ArgumentError)
      expect { described_class.new(chatbotConversationId: chatbot_conversation_id) }.to raise_error(ArgumentError)
    end
  end

  describe '#routing_key' do
    it 'returns the correct routing key' do
      expect(event.routing_key).to eq('message.chatbot.conversation.completed')
    end
  end

  describe '#to_json' do
    it 'returns valid JSON with message_conversation_id and chatbot_conversation_id' do
      json = JSON.parse(event.to_json)
      expect(json['messageConversationId']).to eq(message_conversation_id)
      expect(json['chatbotConversationId']).to eq(chatbot_conversation_id)
    end
  end
end
