# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::ChatbotUserResponse do
  describe '#initialize' do
    context 'with all parameters' do
      let(:message) { 'Hi, I need your help!' }
      let(:chatbot_conversation_id) { '1234567890abcdef' }
      let(:completed) { false }

      subject { described_class.new(message: message, chatbot_conversation_id: chatbot_conversation_id, completed: completed) }

      it 'sets all attributes correctly' do
        expect(subject.message).to eq(message)
        expect(subject.chatbot_conversation_id).to eq(chatbot_conversation_id)
        expect(subject.completed).to eq(completed)
      end
    end

    context 'with minimal parameters' do
      let(:message) { 'Hi, I need your help!' }

      subject { described_class.new(message: message) }

      it 'sets default values for optional parameters' do
        expect(subject.message).to eq(message)
        expect(subject.chatbot_conversation_id).to be_nil
        expect(subject.completed).to eq(false)
      end
    end
  end

  describe '#routing_key' do
    let(:event) { described_class.new(message: 'test message') }

    it 'returns the correct routing key' do
      expect(event.routing_key).to eq(CHATBOT_USER_RESPONSE_EVENT)
    end
  end

  describe '#to_json' do
    context 'with all parameters' do
      let(:message) { 'Hi, I need your help!' }
      let(:chatbot_conversation_id) { '1234567890abcdef' }
      let(:completed) { false }

      subject { described_class.new(message: message, chatbot_conversation_id: chatbot_conversation_id, completed: completed) }

      it 'returns correct JSON structure' do
        expected_json = {
          message: message,
          chatbotConversationId: chatbot_conversation_id,
          completed: completed,
          isMedia: false
        }.to_json

        expect(subject.to_json).to eq(expected_json)
      end
    end

    context 'with nil chatbot_conversation_id' do
      let(:message) { 'Hi, I need your help!' }

      subject { described_class.new(message: message) }

      it 'includes nil chatbot_conversation_id in JSON' do
        expected_json = {
          message: message,
          chatbotConversationId: nil,
          completed: false,
          isMedia: false
        }.to_json

        expect(subject.to_json).to eq(expected_json)
      end
    end

    context 'with completed true' do
      let(:message) { 'Thank you for your help!' }
      let(:chatbot_conversation_id) { '1234567890abcdef' }
      let(:completed) { true }

      subject { described_class.new(message: message, chatbot_conversation_id: chatbot_conversation_id, completed: completed) }

      it 'returns JSON with completed true' do
        expected_json = {
          message: message,
          chatbotConversationId: chatbot_conversation_id,
          completed: completed,
          isMedia: false
        }.to_json

        expect(subject.to_json).to eq(expected_json)
      end
    end
  end
end
