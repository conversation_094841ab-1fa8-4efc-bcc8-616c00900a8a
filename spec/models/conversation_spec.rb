# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Conversation, type: :model do
  let(:conversation) { create(:conversation) }

  describe 'associations' do
    it do
      should have_many(:conversation_look_ups)
    end

    it do
      should have_many(:look_ups)
        .through(:conversation_look_ups)
    end

    it do
      should have_many(:sub_conversations)
    end
  end

  describe 'validations' do
    context 'when phone number is repeated for a particular tenant_id and connected_account_id' do

      it 'should be invalid' do
        new_conversation = build(:conversation, connected_account_id: conversation.connected_account_id, tenant_id: conversation.tenant_id, phone_number: conversation.phone_number)

        expect(new_conversation).to be_invalid
        expect(new_conversation.errors.messages[:phone_number])
      end
    end

    context 'when status is not valid' do 
      it 'should be invalid' do
        conversation.status = "invalid_status"
        expect(conversation).to be_invalid
      end
    end
  end

  describe '.ongoing_sub_conversation' do
    context 'when there are no sub_conversations' do
      it 'returns nil' do
        expect(conversation.ongoing_sub_conversation).to be_nil
      end
    end

    context 'when there is an ongoing sub_conversation with status NEW' do
      it 'returns the id of the sub_conversation' do
        sub_conversation = create(:sub_conversation, conversation: conversation, status: NEW)
        expect(conversation.ongoing_sub_conversation).to eq(sub_conversation)
      end
    end

    context 'when there is an ongoing sub_conversation with status IN_PROGRESS' do
      it 'returns the id of the sub_conversation' do
        sub_conversation = create(:sub_conversation, conversation: conversation, status: IN_PROGRESS)
        expect(conversation.ongoing_sub_conversation).to eq(sub_conversation)
      end
    end

    context 'when there are multiple ongoing sub_conversations' do
      it 'returns the id of the most recent one' do
        older_sub = create(:sub_conversation, conversation: conversation, status: NEW)
        newer_sub = create(:sub_conversation, conversation: conversation, status: IN_PROGRESS)
        expect(conversation.ongoing_sub_conversation).to eq(newer_sub)
      end
    end

    context 'when the sub_conversation is completed' do
      it 'returns nil' do
        create(:sub_conversation, conversation: conversation, status: COMPLETED)
        expect(conversation.ongoing_sub_conversation).to be_nil
      end
    end
  end
end
