# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChatbotMedia, type: :model do
  describe 'validations' do
    let(:chatbot_media) { create(:chatbot_media) }

    it 'is valid' do
      expect(chatbot_media).to be_valid
    end

    it 'validates presence of file_name' do
      chatbot_media.file_name = nil
      expect(chatbot_media).to be_invalid
      expect(chatbot_media.errors.full_messages.to_sentence).to eq('File name can\'t be blank')
    end

    it 'validates presence of file_type' do
      chatbot_media.file_type = nil
      expect(chatbot_media).to be_invalid
      expect(chatbot_media.errors.full_messages.to_sentence).to eq('File type can\'t be blank')
    end

    it 'validates presence of file_size' do
      chatbot_media.file_size = nil
      expect(chatbot_media).to be_invalid
      expect(chatbot_media.errors.full_messages.to_sentence).to eq('File size can\'t be blank')
    end

    it 'validates presence of tenant_id' do
      chatbot_media.tenant_id = nil
      expect(chatbot_media).to be_invalid
      expect(chatbot_media.errors.full_messages.to_sentence).to eq('Tenant can\'t be blank')
    end
  end
end
