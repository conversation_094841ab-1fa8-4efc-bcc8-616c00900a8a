require 'rails_helper'

RSpec.describe Message::SyncService do
  describe '#call' do
    before do
      @user = create(:user, id: 12, tenant_id: 99)
      auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token

      thread[:user] = @user
    end

    context 'with valid parameters' do
      before do
          @data = {
            content: 'This is test message',
            direction: 'outgoing',
            medium: 'whatsapp',
            owner_id: @user.id,
            sent_at: DateTime.now,
            delivered_at: DateTime.now,
            read_at: DateTime.now,
            sender_number: '122121221',
            recipient_number: '1221212121',
            status: 'read',
          }
      end

      context 'when the related entity is lead' do
        let(:related_to) { create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD) }
        let(:permission) { :lead_with_sms_permission }

        before do
          search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: @user.tenant_id, phone_number: related_to.phone_number }] }

          allow_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
          allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })

          allow_any_instance_of(GetLead).to receive(:call).and_return({"id": related_to.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"1231231233","dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead" })
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        end

        it 'returns correct details for message' do
          res = Message::SyncService.new(@data).call

          message = Message.first
          expect(res[:id]).to eq(message.id)
          expect(res[:recipients].first[:entityId]).to eq(message.recipients.first.entity_id)
          expect(res[:recipients].first[:entityType]).to eq(message.recipients.first.entity_type)
        end
      end

      context 'when the message is already present for the remote id' do
        let(:related_to) { create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD) }
        let(:permission) { :lead_with_sms_permission }

        before do
          create(:message, remote_id: '1112', tenant_id: @user.tenant_id)
          search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'contact 1 test', tenant_id: @user.tenant_id, phone_number: related_to.phone_number }] }

          allow_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
          allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
          allow_any_instance_of(GetLead).to receive(:call).and_return({"id": related_to.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"1231231233","dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead" })
        end

        it 'returns correct details for message' do
          message_with_remote_id = {
            content: 'This is test message',
            direction: 'outgoing',
            medium: 'whatsapp',
            owner_id: @user.id,
            sent_at: DateTime.now,
            delivered_at: DateTime.now,
            read_at: DateTime.now,
            sender_number: '122121221',
            recipient_number: '1221212121',
            status: 'read',
            remote_id: '1112'
          }

          res = Message::SyncService.new(message_with_remote_id).call

          message = Message.first
          expect(res[:id]).to eq(message.id)
          expect(res[:recipients].first[:entityId]).to eq(message.recipients.first.entity_id)
          expect(res[:recipients].first[:entityType]).to eq(message.recipients.first.entity_type)
        end
      end

      context 'when new conversation created for whatsapp and related entities are present' do
        context 'when message is outgoing' do
          let(:related_to) { create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD) }
          let(:permission) { :lead_with_sms_permission }
          let(:connected_account){ create(:connected_account, tenant_id: @user.tenant_id) }

          before do
            search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'contact 1 test', tenant_id: @user.tenant_id, phone_number: related_to.phone_number }] }

            allow_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
            allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
            allow_any_instance_of(GetLead).to receive(:call).and_return({"id": related_to.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"**********","dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead" })

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(123, 'lead', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(123, 'contact', { dialCode: '+91', value: '**********' })
          end

          it 'returns schedules jobs to associated existing entities by conversation phone_number' do
            message_with_conversation_id = {
              content: 'This is test message',
              direction: 'outgoing',
              medium: 'whatsapp',
              owner_id: @user.id,
              sent_at: DateTime.now,
              delivered_at: DateTime.now,
              read_at: DateTime.now,
              sender_number: '91**********',
              recipient_number: '91**********',
              status: 'read',
              conversation_id: 123
            }

            options = {
              connected_account: connected_account,
              native_whatsapp_webhook: true,
              is_new_conversation: true
            }
            res = Message::SyncService.new(message_with_conversation_id, options).call
          end
        end

        context 'when message is incoming' do
          let(:related_to) { create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD) }
          let(:permission) { :lead_with_sms_permission }
          let(:connected_account){ create(:connected_account, tenant_id: @user.tenant_id, is_chatbot_configured: true) }
          let(:message_conversation){ create(:conversation, phone_number: '+91**********', tenant_id: @user.tenant_id, connected_account_id:  connected_account.id, owner_id: @user.id) }

          before do
            search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'contact 1 test', tenant_id: @user.tenant_id, phone_number: related_to.phone_number }] }

            allow_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
            allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
            allow_any_instance_of(GetLead).to receive(:call).and_return({"id": related_to.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"**********","dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead" })

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageReceivedFromEntity))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(message_conversation.id, 'lead', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(message_conversation.id, 'contact', { dialCode: '+91', value: '**********' })
            expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappMessageCreatedV2))

            #allow(InitiateChatbotConversationJob).to receive(:perform_later)

            ActiveJob::Base.queue_adapter = :test
          end

          it 'returns schedules jobs to associated existing entities by conversation phone_number' do
            message_with_conversation_id = {
              content: 'This is test message',
              direction: 'incoming',
              medium: 'whatsapp',
              owner_id: @user.id,
              sent_at: DateTime.now,
              delivered_at: DateTime.now,
              read_at: DateTime.now,
              sender_number: '91**********',
              recipient_number: '91**********',
              status: 'read',
              conversation_id: message_conversation.id,
              connected_account_id: connected_account.id,
              message_type: WHATSAPP_BUSINESS
            }

            options = {
              connected_account: connected_account,
              native_whatsapp_webhook: true,
              is_new_conversation: true
            }
            res = Message::SyncService.new(message_with_conversation_id, options).call
          end

          it 'initiates chatbot conversation for existing entities as the conversation is new' do
            message_with_conversation_id = {
              content: 'This is test message',
              direction: 'incoming',
              medium: 'whatsapp',
              owner_id: @user.id,
              sent_at: DateTime.now,
              delivered_at: DateTime.now,
              read_at: DateTime.now,
              sender_number: '91**********',
              recipient_number: '91**********',
              status: 'read',
              conversation_id: message_conversation.id,
              connected_account_id: connected_account.id,
              message_type: WHATSAPP_BUSINESS
            }

            options = {
              connected_account: connected_account,
              native_whatsapp_webhook: true,
              is_new_conversation: true
            }

            expect(InitiateChatbotConversationJob).not_to receive(:perform_later).with(
              message_conversation.id,
              'This is test message',
              [{id: 1, entityType: 'lead' }]
            )

            Message::SyncService.new(message_with_conversation_id, options).call
          end
        end
      end
    end

    context 'when native whatsapp webhook is true' do
      let(:permission) { :sms_with_write_permission }
      let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id, created_by: @user, entities_to_create: []) }
      let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, owner_id: @user.id, connected_account_id: connected_account.id) }

      before do
        @params = {
          medium: 'whatsapp',
          sent_at: "2020-11-20T01:50:00.000Z",
          delivered_at: "2020-11-20T01:52:00.000Z",
          read_at: "2020-11-20T01:53:00.000Z",
          content: 'Test content',
          direction: 'incoming',
          recipient_number: '+**********',
          sender_number: '+**********',
          status: 'read',
          message_type: 'whatsapp_business',
          conversation_id: conversation.id,
          connected_account_id: connected_account.id
        }
        @options = { native_whatsapp_webhook: true }
        @options[:connected_account] = connected_account

        allow_any_instance_of(SearchLeads).to receive(:call).and_return(matched: [])
        allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
        expect(Publishers::MessageCreated).to receive(:call).and_return(nil)
        expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).and_return(nil)
        expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).and_return(nil)
      end

      it 'updates connected account last_message_received_at' do
        expect {
          Message::SyncService.new(@params, @options).call
        }.to change { connected_account.reload.last_message_received_at }.to(@params[:sent_at].to_i)
      end

      it 'creates a new message' do
        expect {
          Message::SyncService.new(@params, @options).call
        }.to change(Message, :count).by(1)
      end
    end

    context 'when chatbot conversation should be triggered for new entities' do
      let(:permission) { :sms_with_write_permission }
      let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id, created_by: @user, entities_to_create: ['lead', 'contact'], is_chatbot_configured: true) }
      let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, owner_id: @user.id, connected_account_id: connected_account.id, chatbot_conversation_completed: false) }

      before do
        @params = {
          medium: 'whatsapp',
          sent_at: "2020-11-20T01:50:00.000Z",
          content: 'Hello, I need help',
          direction: INCOMING,
          recipient_number: '+**********',
          sender_number: '+**********',
          status: 'received',
          message_type: 'whatsapp_business',
          conversation_id: conversation.id,
          connected_account_id: connected_account.id
        }
        @options = {
          native_whatsapp_webhook: true,
          is_new_conversation: true,
          connected_account: connected_account
        }

        # Mock entity search to return no matches (new entities)
        allow_any_instance_of(SearchLeads).to receive(:call).and_return(matched: [])
        allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })

        allow_any_instance_of(EntityService).to receive(:create) do |instance|
          entity_type = instance.instance_variable_get(:@params)[:entity_type]
          if entity_type == 'lead'
            { 'id' => 12345 }
          else
            { 'id' => 67890 }
          end
        end

        # Create a real message to avoid complex mocking
        message = create(:message,
          conversation_id: conversation.id,
          content: 'Hello, I need help',
          tenant_id: @user.tenant_id,
          owner_id: @user.id
        )

        # Mock the message service to return the message ID
        allow_any_instance_of(Message::MessageService).to receive(:call).and_return(message.id)

        # Mock publishers
        allow(Publishers::MessageCreated).to receive(:call).and_return(nil)
        allow(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).and_return(nil)
        allow(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).and_return(nil)

        # Mock job scheduling
        allow(InitiateChatbotConversationJob).to receive(:perform_later)

        ActiveJob::Base.queue_adapter = :test
      end

      it 'schedules InitiateChatbotConversationJob when new entities are created' do
        expect(InitiateChatbotConversationJob).not_to receive(:perform_later).with(
          conversation.id,
          'Hello, I need help',
          [{ id: 12345, entityType: 'lead' }, { id: 67890, entityType: 'contact' }]
        )

        Message::SyncService.new(@params, @options).call
      end

      it 'does not schedule chatbot job when direction is not incoming' do
        @params[:direction] = 'outgoing'

        expect(InitiateChatbotConversationJob).not_to receive(:perform_later)

        Message::SyncService.new(@params, @options).call
      end

      it 'does not schedule chatbot job when not native whatsapp webhook' do
        @options[:native_whatsapp_webhook] = false

        expect(InitiateChatbotConversationJob).not_to receive(:perform_later)

        # This will raise phone number validation error, so we need to rescue it
        expect { Message::SyncService.new(@params, @options).call }.to raise_error(ExceptionHandler::PhoneNumbersNotMatchedError)
      end

      it 'does not schedule chatbot job when not new conversation' do
        @options[:is_new_conversation] = false

        expect(InitiateChatbotConversationJob).not_to receive(:perform_later)

        Message::SyncService.new(@params, @options).call
      end

      it 'schedules chatbot job when related entities already exist' do
        allow_any_instance_of(SearchLeads).to receive(:call).and_return(
          matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: @user.tenant_id, phone_number: '+**********' }]
        )

        allow_any_instance_of(GetLead).to receive(:call).and_return({
          "id": 1,
          "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "**********", "dialCode": "+91", "primary": true}],
          "firstName": "Test",
          "lastName": "Lead"
        })

        expect(InitiateChatbotConversationJob).not_to receive(:perform_later)
        Message::SyncService.new(@params, @options).call
      end
    end

    context 'when chatbot user response should be triggered for existing entities' do
      let(:permission) { :sms_with_write_permission }
      let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id, created_by: @user, is_chatbot_configured: true) }
      let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, owner_id: @user.id, connected_account_id: connected_account.id, chatbot_conversation_id: 'conv-123', chatbot_conversation_completed: false) }

      before do
        @params = {
          medium: 'whatsapp',
          sent_at: "2020-11-20T01:50:00.000Z",
          content: 'Hi, I need your help!',
          direction: INCOMING,
          recipient_number: '+**********',
          sender_number: '+**********',
          status: 'received',
          message_type: 'whatsapp_business',
          conversation_id: conversation.id,
          connected_account_id: connected_account.id
        }
        @options = {
          native_whatsapp_webhook: true,
          connected_account: connected_account
        }

        allow_any_instance_of(SearchLeads).to receive(:call).and_return(
          matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: @user.tenant_id, phone_number: '+**********' }]
        )
        allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })

        allow_any_instance_of(GetLead).to receive(:call).and_return({
          "id": 1,
          "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "**********", "dialCode": "+91", "primary": true}],
          "firstName": "Test",
          "lastName": "Lead"
        })

        message = create(:message,
          conversation_id: conversation.id,
          content: 'Hi, I need your help!',
          tenant_id: @user.tenant_id,
          owner_id: @user.id,
          direction: INCOMING
        )

        allow_any_instance_of(Message::MessageService).to receive(:call).and_return(message.id)

        allow(Publishers::MessageCreated).to receive(:call).and_return(nil)
        allow(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).and_return(nil)
        allow(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).and_return(nil)

        allow(ChatbotUserResponseService).to receive(:call)

        ActiveJob::Base.queue_adapter = :test
      end

      it 'calls ChatbotUserResponseService when existing related entities are present' do
        expect(ChatbotUserResponseService).to receive(:call).with(
          message: an_instance_of(Message),
          conversation: conversation,
          interactive_message_reply_id: nil
        )

        Message::SyncService.new(@params, @options).call
      end

      it 'does not call ChatbotUserResponseService when direction is not incoming' do
        @params[:direction] = 'outgoing'

        expect(ChatbotUserResponseService).not_to receive(:call)

        Message::SyncService.new(@params, @options).call
      end

      it 'does not call ChatbotUserResponseService when not native whatsapp webhook' do
        @options[:native_whatsapp_webhook] = false

        expect(ChatbotUserResponseService).not_to receive(:call)

        Message::SyncService.new(@params, @options).call
      end

      it 'does not call ChatbotUserResponseService when new entities are created' do
        allow_any_instance_of(SearchLeads).to receive(:call).and_return(matched: [])
        allow_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })

        allow_any_instance_of(EntityService).to receive(:create) do |instance|
          entity_type = instance.instance_variable_get(:@params)[:entity_type]
          if entity_type == 'lead'
            { 'id' => 12345 }
          else
            { 'id' => 67890 }
          end
        end

        expect(ChatbotUserResponseService).not_to receive(:call)

        Message::SyncService.new(@params, @options).call
      end

      it 'does not call ChatbotUserResponseService when conversation has no chatbot_conversation_id' do
        conversation.update!(chatbot_conversation_id: nil)

        expect(ChatbotUserResponseService).not_to receive(:call)

        Message::SyncService.new(@params, @options).call
      end

      it 'does not call ChatbotUserResponseService when chatbot conversation is completed' do
        conversation.update!(chatbot_conversation_completed: true)

        expect(ChatbotUserResponseService).not_to receive(:call)

        Message::SyncService.new(@params, @options).call
      end
    end
  end
end
