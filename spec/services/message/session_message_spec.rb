# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message::SessionMessage do
  let(:user) { create(:user) }
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
  let(:audio_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3') }
  let(:text_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_text_file.txt', 'text/plain') }
  let(:video_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_video.mp4', 'video/mp4') }
  let(:image_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
  let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
  let(:token_without_sms) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data_without_sms) { User::TokenParser.parse(token_without_sms.token) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
  let(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
  let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }
  let(:admin_user) { create(:user, tenant_id: user.tenant_id) }
  let(:valid_admin_user_auth_token) { build(:auth_token, :with_meesage_and_lead_read_all_permission, user_id: admin_user.id, tenant_id: admin_user.tenant_id, username: admin_user.name) }
  let(:admin_user_auth_data) { User::TokenParser.parse(valid_admin_user_auth_token.token) }

  before do
    # Stub usage API calls for all tests
    stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
      .to_return(status: 200, body: {
        records: { used: 100, total: 1000 },
        storage: { used: 0.5, total: 2.0 }
      }.to_json)
  end

  describe '#send_text' do
    let(:params) do
      ActionController::Parameters.new({
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'text',
        message_body: 'This is sample session message text.'
      }).permit!
    end

  let(:params_with_manual) { params.merge(is_manual: true) }

    context 'when user is not admin user' do
      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
      end

      context 'when user does not have sufficient whatsapp credits balance' do
        it 'raises error' do
          expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
        end
      end

      context 'when usage limits are exceeded' do
        before do
          whatsapp_credit
          allow_any_instance_of(UsageLimitValidatorService).to receive(:call)
            .and_raise(ExceptionHandler::UsageLimitExceededError, "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support.")
        end

        it 'raises UsageLimitExceededError' do
          expect { described_class.new(params).send_text }.to raise_error(
            ExceptionHandler::UsageLimitExceededError,
            "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support."
          )
        end
      end

      context 'when user has sufficient whatsapp credits balance' do
        before(:each) { whatsapp_credit }

        context 'when valid entity and message' do
          before do
            stub_request(:post, 'http://localhost:8083/v1/search/lead')
              .with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

            stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
              .with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'text',
                  text: {
                    body: 'This is sample session message text.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
          end

          context 'when conversation is not found for given phone number and connected account' do
            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
            end
          end

          context 'when conversation is found for given phone number and connected account' do
            before(:each) do
              conversation.update(last_message_received_at: 10.hours.ago)
              sub_conversation
            end

            context 'when user does not have required conversation permission' do
              it 'raises error' do
                expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
              end
            end

            context 'when user has required conversation permission' do
              before(:each) { user_share_rule }

              it 'sends and creates message' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                expect { described_class.new(params).send_text }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)

                message = Message.last
                expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
                expect(message.direction).to eq('outgoing')
                expect(message.content).to eq('This is sample session message text.')
                expect(message.recipient_number).to eq('+************')
                expect(message.sender_number).to eq(connected_account.waba_number)
                expect(message.message_type).to eq('whatsapp_business')
                expect(message.tenant_id).to eq(user.tenant_id)
                expect(message.sent_at.present?).to be_truthy
                expect(message.medium).to eq('whatsapp')
                expect(message.conversation_id).to eq(conversation.id)
                expect(message.sub_conversation_id).to eq(sub_conversation.id)
                expect(message.component_wise_content).to eq(
                  [
                    { 
                      "text" => "This is sample session message text.", 
                      "type"=>"BODY", 
                      "value"=>nil, 
                      "format"=>"TEXT", 
                      "position"=>0
                    }
                  ]
                )

                lookup = message.related_to.first
                expect(lookup.entity_type).to eq('lead')
                expect(lookup.entity_id).to eq(123)
                expect(lookup.phone_number).to eq('************')
                expect(lookup.name).to eq('Lead Name')
                expect(lookup.owner_id).to eq(4010)
              end

              it 'adds connected account id on message' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                described_class.new(params).send_text
                expect(Message.last.connected_account_id).to eq(connected_account.id)
              end
            end
          end
        end

        context 'when invalid request' do
          context 'when invalid entity type' do
            before { params[:entity_type] = 'deal' }

            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
            end
          end

          context 'when invalid phone number id' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 123 }] }] }.to_json)
            end

            context 'when missing entity id' do
              before { params.delete(:entity_id) }

              it 'raises error' do
                expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
              end
            end

            context 'when user does not have sms permission on entity' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead')
                  .with(
                    headers: {
                      Authorization: "Bearer #{@token_without_pid}",
                      content_type: 'application/json'
                    },
                    body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                  ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: false } }] }.to_json)
              end

              context 'when missing phone number id' do
                before { params.delete(:phone_id) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Phone number or conversation id missing.')
                end
              end

              context 'when message body is not present' do
                before { params.delete(:message_body) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Please ensure that message is present and less than 4096 characters.')
                end
              end

              context 'when message body exceeds allowed length' do
                before { params[:message_body] = 4097.times.map { 'a' }.join }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Please ensure that message is present and less than 4096 characters.')
                end
              end

              context 'when connected account not found' do
                before { params[:id] = -1 }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found.')
                end
              end

              context 'when inactive connected account' do
                before { connected_account.update(status: INACTIVE) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
                end
              end

              context 'when unverified account' do
                before { connected_account.update(is_verified: false) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
                end
              end

              context 'when user is not an agent on connected account' do
                before { AgentUser.where(connected_account_id: connected_account.id).delete_all }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
                end
              end

              context 'when user does not have access to entity' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::EntityNotFound, '022032||lead not found')
                end
              end

              context 'when invalid phone number id' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 123 }] }] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.")
                end
              end

              context 'when user does not have sms permission on entity' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: false } }] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::MessageNotAllowedError, "022007")
                end
              end
            end
          end

          context 'when session is inactive' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              conversation.update(last_message_received_at: 30.hours.ago)
              sub_conversation
              user_share_rule
            end

            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Session is inactive.")
            end
          end
        end
      end
    end

    context 'when user is admin user with read_all permission on entity and conversation' do
      context 'and user is not owner of this entity or entity is not shared with user' do
        before do
          create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
          Thread.current[:auth] = admin_user_auth_data
          Thread.current[:token] = valid_admin_user_auth_token.token
          Thread.current[:user] = admin_user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          whatsapp_credit
          conversation.update(last_message_received_at: 10.hours.ago)
          sub_conversation

          @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(@token_without_pid)

          stub_request(:post, 'http://localhost:8083/v1/search/lead')
            .with(
              headers: {
                Authorization: "Bearer #{@token_without_pid}",
                content_type: 'application/json'
              },
              body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
            ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
            .with(
              headers: {
                'x-access-token': 'partner-token',
                'x-waba-id': connected_account.waba_id,
                content_type: 'application/json'
              },
              body: {
                messaging_product: 'whatsapp',
                recipient_type: 'individual',
                to: '+************',
                type: 'text',
                text: {
                  body: 'This is sample session message text.'
                }
              }.to_json
            ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
        end

        context 'when phone_id is present' do
          it 'sends and creates message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            expect { described_class.new(params).send_text }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)

            message = Message.last
            expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
            expect(message.direction).to eq('outgoing')
            expect(message.content).to eq('This is sample session message text.')
            expect(message.recipient_number).to eq('+************')
            expect(message.sender_number).to eq(connected_account.waba_number)
            expect(message.message_type).to eq('whatsapp_business')
            expect(message.tenant_id).to eq(user.tenant_id)
            expect(message.sent_at.present?).to be_truthy
            expect(message.medium).to eq('whatsapp')
            expect(message.conversation_id).to eq(conversation.id)
            expect(message.sub_conversation_id).to eq(sub_conversation.id)

            lookup = message.related_to.first
            expect(lookup.entity_type).to eq('lead')
            expect(lookup.entity_id).to eq(123)
            expect(lookup.phone_number).to eq('************')
            expect(lookup.name).to eq('Lead Name')
            expect(lookup.owner_id).to eq(4010)
          end
        end

        context 'when conversation_id is present' do
          before { params[:conversation_id] = conversation.id, params[:phone_id] = nil }

          it 'sends and creates message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            expect { described_class.new(params).send_text }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)
          end

          it 'throws error if phone number is not present on entity' do
            conversation.update(phone_number: '+919898988776')
            expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.")
          end

          it 'throws error if conversation is not found' do
            params[:conversation_id] = 333
            expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::ConversationNotFoundError, "022026||Conversation not found")
          end
        end
      end
    end

    context 'when conversation has no lookups and entity_id, entity_type are null' do
      let(:params) do
        ActionController::Parameters.new({
          id: connected_account.id,
          entity_type: nil,
          entity_id: nil,
          conversation_id: conversation.id,
          message_type: 'text',
          message_body: 'This is a test message without entity details.'
        }).permit!
      end

      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = admin_user_auth_data
        Thread.current[:token] = valid_admin_user_auth_token.token
        Thread.current[:user] = admin_user

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        allow_any_instance_of(Interakt::Message).to receive(:send_session_message).and_return(OpenStruct.new(body: { 'messages' => [{ 'id' => 'wamid.test123' }] }))
      end

      it 'sends a text message successfully' do
        expect { described_class.new(params).send_text }
          .to change(Message, :count).by(1)

        message = Message.last
        expect(message.remote_id).to eq('wamid.test123')
        expect(message.direction).to eq('outgoing')
        expect(message.content).to eq('This is a test message without entity details.')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.message_type).to eq('whatsapp_business')
        expect(message.tenant_id).to eq(user.tenant_id)
        expect(message.sent_at.present?).to be_truthy
        expect(message.medium).to eq('whatsapp')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end

    context 'when session text message send' do
      it 'should properly update a status of conversation and sub conversation' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        user_share_rule

        # Create an incoming message to trigger status update for conversation and sub conversation
        create(:message, sub_conversation_id: sub_conversation.id, direction: 'incoming', conversation_id: conversation.id, tenant_id: user.tenant_id, connected_account_id: conversation.connected_account_id)

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{@token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'text',
              text: {
                body: 'This is sample session message text.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(params).send_text
        }.to change { sub_conversation.reload.status }.from(NEW).to(IN_PROGRESS)
         .and change { conversation.reload.status }.from(NEW).to(IN_PROGRESS)
      end

      it 'should create a new sub conversation and update conversation status to NEW when no ongoing sub conversation exists' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        # Create conversation without ongoing sub_conversation
        conversation.update(last_message_received_at: 10.hours.ago, status: COMPLETED)
        # Ensure no ongoing sub_conversation
        SubConversation.where(conversation_id: conversation.id).update_all(status: COMPLETED)
        user_share_rule

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              content_type: 'application/json'
            }
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'text',
              text: {
                body: 'This is sample session message text.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(params).send_text
        }.to change(SubConversation, :count).by(1)
         .and change { conversation.reload.status }.from(COMPLETED).to(NEW)

        new_sub_conversation = SubConversation.last
        expect(new_sub_conversation.tenant_id).to eq(connected_account.tenant_id)
        expect(new_sub_conversation.connected_account_id).to eq(connected_account.id)
        expect(new_sub_conversation.conversation_id).to eq(conversation.id)
        expect(new_sub_conversation.status).to eq(NEW)
      end

      it 'should call chatbot conversation completed publisher when manual message is sent and chatbot is in progress' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago, chatbot_conversation_id: 123, chatbot_conversation_completed: false)
        sub_conversation
        user_share_rule

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{@token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'text',
              text: {
                body: 'This is sample session message text.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        expect(Publishers::ChatbotConversationCompletedPublisher).to receive(:call).with(conversation)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        described_class.new(params_with_manual).send_text

        expect(conversation.reload.chatbot_conversation_completed).to be_truthy
      end
    end
  end

  describe '#send_media' do
    let(:audio_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: audio_file,
            type: 'audio'
          }
        ]
      }.with_indifferent_access
    end

    let(:text_document_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: text_file,
            type: 'document',
            caption: 'Here is the requeseted document attached.'
          }
        ]
      }.with_indifferent_access
    end

    let(:video_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: video_file,
            type: 'video',
            caption: 'Here is the requeseted video attached.'
          }
        ]
      }.with_indifferent_access
    end

    let(:image_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: image_file,
            type: 'image',
            caption: 'Here is the requeseted image attached.'
          }
        ]
      }.with_indifferent_access
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when user does not have sufficient whatsapp credits balance' do
      it 'raises error' do
        captioned_audio_params = audio_params
        captioned_audio_params[:media][0][:caption] = 'some text'
        expect { described_class.new(captioned_audio_params).send_media }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
      end
    end

    context 'when usage limits are exceeded' do
      before do
        whatsapp_credit
        allow_any_instance_of(UsageLimitValidatorService).to receive(:call)
          .and_raise(ExceptionHandler::UsageLimitExceededError, "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support.")
      end

      it 'raises UsageLimitExceededError' do
        expect { described_class.new(audio_params).send_media }.to raise_error(
          ExceptionHandler::UsageLimitExceededError,
          "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support."
        )
      end
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user

      permissions = Thread.current[:auth].permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

      conversation.update(last_message_received_at: 10.hours.ago)
      sub_conversation
    end

    context 'when user has sufficient whatsapp credits balance' do
      before(:each) { whatsapp_credit }

      context 'when invalid request' do
        context 'when invalid entity is passed' do
          let(:invalid_entity_params) do
            ActionController::Parameters.new({
              entity_type: 'deal'
            })
          end

          it 'raises invalid entity error' do
            expect { described_class.new(invalid_entity_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
          end
        end

        context 'when phone_id is not present' do
          let(:phone_id_missing_params) do
            ActionController::Parameters.new({
              entity_type: 'lead',
              entity_id: 123
            })
          end

           it 'raises missing phone id error' do
            expect { described_class.new(phone_id_missing_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Phone number or conversation id missing.')
          end
        end

        context 'when message type is not media' do
          let(:invalid_message_type_params) do
            ActionController::Parameters.new({
              entity_type: 'lead',
              entity_id: 123,
              phone_id: 111,
              message_type: 'text',
            }).permit!
          end

          it 'raises invalid message type error' do
            expect { described_class.new(invalid_message_type_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid message type.')
          end
        end

        context 'when invalid media is passed' do
          context 'when file type is invalid' do
            let(:invalid_file_type_params) do
              ActionController::Parameters.new({
                entity_type: 'lead',
                entity_id: 123,
                phone_id: 111,
                message_type: 'media',
                media: [
                  {
                    file: Rack::Test::UploadedFile.new('spec/fixtures/files/invalid_type.text', 'txt'),
                    type: 'audio'
                  }
                ]
              }).permit!
            end

            it 'raises error' do
              expect { described_class.new(invalid_file_type_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid media type.')
            end
          end

          context 'when file size is large' do
            let(:large_file_params) do
              ActionController::Parameters.new({
                entity_type: 'lead',
                entity_id: 123,
                phone_id: 111,
                message_type: 'media',
                media: [
                  {
                    file: audio_file,
                    type: 'audio'
                  }
                ]
              }).permit!
            end

            before { allow(audio_file).to receive(:size).and_return(1_67_77_217) }

            it 'raises error' do
              expect { described_class.new(large_file_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||File size exceeds limit.')
            end
          end
        end

        context 'when session is inactive' do
          before do
            create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
            Thread.current[:auth] = auth_data
            Thread.current[:token] = valid_auth_token.token
            Thread.current[:user] = user
            stub_request(:post, 'http://localhost:8083/v1/search/lead')
              .with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

            conversation.update(last_message_received_at: 30.hours.ago)
            sub_conversation
            user_share_rule
          end

          it 'raises error' do
            expect { described_class.new(audio_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Session is inactive.")
          end
        end
      end

      context 'with valid request' do
        before do
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token.token
          Thread.current[:user] = user
        end

        context 'when media type is audio' do
          context 'and caption is passed' do
            it 'raises invalid data error' do
              captioned_audio_params = audio_params
              captioned_audio_params[:media][0][:caption] = 'some text'

              expect {
                described_class.new(captioned_audio_params).send_media
              }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Caption is not allowed for this message.')
            end
          end

          context 'when valid params are passed for audio' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'audio',
                  audio: {
                    id: '<MEDIA_ID>'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              user_share_rule
            end

            it 'uploads media and creates media message' do
              expect { described_class.new(audio_params).send_media }
                .to change(Message, :count).by(1)
                .and change(MessageLookUp, :count).by(1)
                .and change(LookUp, :count).by(1)
                .and change(Attachment, :count).by(1)

              message = Message.last
              expect(message.attachments.count).to eq(1)
              expect(message.conversation_id).to eq(conversation.id)
              expect(message.sub_conversation_id).to eq(sub_conversation.id)
            end

            it 'adds connected account id on message' do
              described_class.new(audio_params).send_media
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end
        end

        context 'when media type is document' do
          context 'with valid params' do
            context 'when user does not have required conversation permission' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', ownerId: 2323, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

                stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                  headers: {
                    'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                  }
                ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

                stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    recipient_type: 'individual',
                    to: '+************',
                    type: 'document',
                    document: {
                      id: '<MEDIA_ID>',
                      caption: 'Here is the requeseted document attached.'
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
              end

              it 'raises error' do
                expect { described_class.new(text_document_params).send_media }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
              end
            end

            context 'when user has required conversation permission' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

                stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                  headers: {
                    'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                  }
                ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

                stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    recipient_type: 'individual',
                    to: '+************',
                    type: 'document',
                    document: {
                      id: '<MEDIA_ID>',
                      caption: 'Here is the requeseted document attached.',
                      filename: 'sample_text_file.txt'
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

                s3_instance = instance_double(S3::UploadFile)
                allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
                expect(s3_instance).to receive(:call)

                expect(File).to receive(:delete)
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                user_share_rule
              end

              it 'uploads media and creates media message' do
                expect { described_class.new(text_document_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)

                message = Message.last
                expect(message.content).to eq('Here is the requeseted document attached.')
                expect(message.component_wise_content).to eq(
                  [
                    {
                      "text"=>"Here is the requeseted document attached.", 
                      "type"=>"BODY", 
                      "value"=>nil, 
                      "format"=>"TEXT", 
                      "position"=>0
                    }
                  ]
                )
              end
            end
          end
        end

        context 'when media type is video' do
          context 'with valid params' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'video',
                  video: {
                    id: '<MEDIA_ID>',
                    caption: 'Here is the requeseted video attached.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              user_share_rule
            end

            it 'uploads media and creates media message' do
              expect { described_class.new(video_params).send_media }
                .to change(Message, :count).by(1)
                .and change(MessageLookUp, :count).by(1)
                .and change(LookUp, :count).by(1)
                .and change(Attachment, :count).by(1)

              message = Message.last
              expect(message.content).to eq('Here is the requeseted video attached.')
              expect(message.component_wise_content).to eq(
                  [
                    {
                      "text"=>"Here is the requeseted video attached.", 
                      "type"=>"BODY", 
                      "value"=>nil, 
                      "format"=>"TEXT", 
                      "position"=>0
                    }
                  ]
                )
            end
          end
        end

        context 'when media type is image' do
          context 'with valid params' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'image',
                  image: {
                    id: '<MEDIA_ID>',
                    caption: 'Here is the requeseted image attached.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              user_share_rule
            end

            context 'when phone_id is present' do
              it 'uploads media and creates media message' do
                expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)

                message = Message.last
                expect(message.content).to eq('Here is the requeseted image attached.')
                expect(message.component_wise_content).to eq(
                  [
                    {
                      "text"=>"Here is the requeseted image attached.", 
                      "type"=>"BODY", 
                      "value"=>nil, 
                      "format"=>"TEXT", 
                      "position"=>0
                    }
                  ]
                )
              end
            end

            context 'when conversation_id is present' do
              before { image_params[:conversation_id] = conversation.id, image_params[:phone_id] = nil }

              it 'uploads media and creates media message' do
                expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)
              end
            end
          end
        end
      end
    end

    context 'when message sending fails due to error from meta or interakt'do
      before(:each) { whatsapp_credit }

      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          headers: {
            Authorization: "Bearer #{@token_without_pid}",
            content_type: 'application/json'
          },
          body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
        ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            content_type: 'application/json'
          },
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'image',
            image: {
              id: '<MEDIA_ID>',
              caption: 'Here is the requeseted image attached.'
            }
          }.to_json
        ).to_return(status: 400, body: file_fixture('facebook/message/session-message-failure-response.json'))

        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        user_share_rule
      end

      it 'should mark message as failed' do
        expect { described_class.new(image_params).send_media }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)
            .and change(Attachment, :count).by(1)

          message = Message.last
          expect(message.status).to eq('failed')
          expect(message.failed_at).to be_present
      end
    end

    context 'when conversation has no lookups and entity_id, entity_type are null' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = admin_user_auth_data
        Thread.current[:token] = valid_admin_user_auth_token.token
        Thread.current[:user] = admin_user

        image_params['entity_type'] = nil
        image_params['entity_id'] = nil
        image_params['phone_id'] = nil
        image_params['conversation_id'] = conversation.id
        
        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        allow_any_instance_of(Interakt::Message).to receive(:send_session_message).and_return(OpenStruct.new(body: { 'messages' => [{ 'id' => 'wamid.test123' }] }))
        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})
      end

      it 'sends a media message successfully' do
        expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(Attachment, :count).by(1)
      end
    end

    context 'when session media message send' do
      it 'should properly update a status of conversation and sub conversation' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        user_share_rule

        # Create an incoming message to trigger status update for conversation and sub conversation
        create(:message, sub_conversation_id: sub_conversation.id, direction: 'incoming', conversation_id: conversation.id, tenant_id: user.tenant_id, connected_account_id: conversation.connected_account_id)

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{@token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'image',
              image: {
                id: '<MEDIA_ID>',
                caption: 'Here is the requeseted image attached.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(image_params).send_media
        }.to change { sub_conversation.reload.status }.from(NEW).to(IN_PROGRESS)
         .and change { conversation.reload.status }.from(NEW).to(IN_PROGRESS)
      end

      it 'should create a new sub conversation and update conversation status to NEW when no ongoing sub conversation exists' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        # Create conversation without ongoing sub_conversation
        conversation.update(last_message_received_at: 10.hours.ago, status: COMPLETED)
        # Ensure no ongoing sub_conversation
        SubConversation.where(conversation_id: conversation.id).update_all(status: COMPLETED)
        user_share_rule

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              content_type: 'application/json'
            }
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'image',
              image: {
                id: '<MEDIA_ID>',
                caption: 'Here is the requeseted image attached.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(image_params).send_media
        }.to change(SubConversation, :count).by(1)
         .and change { conversation.reload.status }.from(COMPLETED).to(NEW)

        new_sub_conversation = SubConversation.last
        expect(new_sub_conversation.tenant_id).to eq(connected_account.tenant_id)
        expect(new_sub_conversation.connected_account_id).to eq(connected_account.id)
        expect(new_sub_conversation.conversation_id).to eq(conversation.id)
        expect(new_sub_conversation.status).to eq(NEW)
      end

      it 'should call chatbot conversation completed publisher when manual message is sent and chatbot is in progress' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago, chatbot_conversation_id: 123, chatbot_conversation_completed: false)
        sub_conversation
        user_share_rule

        image_params[:is_manual] = true

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{@token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'image',
              image: {
                id: '<MEDIA_ID>',
                caption: 'Here is the requeseted image attached.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)
        expect(Publishers::ChatbotConversationCompletedPublisher).to receive(:call).with(conversation)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        described_class.new(image_params).send_media

        expect(conversation.reload.chatbot_conversation_completed).to be_truthy
      end
    end
  end

  describe '#send_interactive_message' do
    let(:params) do
      ActionController::Parameters.new({
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'button',
        header: {
          format: 'text',
          text: 'Header text'
        },
        body: 'Question to be asked',
        footer: 'Footer value',
        buttons: [
          {
            id: 'opt1',
            text: 'What is your name?',
            position: 1
          }
        ]
      }).permit!
    end

    let(:interactive_list_params) do
      ActionController::Parameters.new({
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'list',
        header: {
          format: 'text',
          text: 'Header text'
        },
        body: 'Question to be asked',
        footer: 'Footer value',
        menu_button: 'Click me',
        sections: [
          { title: 'Section 1', rows: [
              { id: 'row1', title: 'Row 1', description: 'Description 1' },
              { id: 'row2', title: 'Row 2', description: 'Description 2' }
            ]
          },
          { title: 'Section 2', rows: [
              { id: 'row3', title: 'Row 3', description: 'Description 3' },
              { id: 'row4', title: 'Row 4', description: 'Description 4' }
            ]
          }
        ]
      }).permit!
    end

    let(:cta_url_params) do
      ActionController::Parameters.new({
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'cta_url',
        header: {
          format: 'text',
          text: 'Header text'
        },
        body: 'Question to be asked',
        button_text: 'Display Text',
        cta_url: 'https://example.com',
        footer: 'Footer value'
      }).permit!
    end

    context 'when user is not admin user' do
      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        allow_any_instance_of(GenerateToken).to receive(:call).and_return(@token_without_pid)
      end

      context 'when user does not have sufficient whatsapp credits balance' do
        it 'raises error' do
          expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
        end
      end

      context 'when usage limits are exceeded' do
        before do
          whatsapp_credit
          allow_any_instance_of(UsageLimitValidatorService).to receive(:call)
            .and_raise(ExceptionHandler::UsageLimitExceededError, "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support.")
        end

        it 'raises UsageLimitExceededError' do
          expect { described_class.new(params).send_interactive_message }.to raise_error(
            ExceptionHandler::UsageLimitExceededError,
            "#{ErrorCode.usage_limit_exceeded}||Usage limit exceeded. Please upgrade your plan or contact support."
          )
        end
      end

      context 'when user has sufficient whatsapp credits balance' do
        before(:each) { whatsapp_credit }

        context 'when invalid request' do
          context 'when invalid entity type' do
            before { params[:entity_type] = 'deal' }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
            end
          end

          context 'when entity id is not present' do
            before { params[:entity_id] = nil }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
            end
          end

          context 'when message type is not button' do
            before { params[:message_type] = 'text' }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid message type.')
            end
          end

          context 'when header text is blank or too long' do
            it 'raises error for blank header text' do
              params[:header]['text'] = ''
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Header text cannot be empty or more than 60 characters.')
            end

            it 'raises error for header text > 60 chars' do
              params[:header]['text'] = 'a' * 61
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Header text cannot be empty or more than 60 characters.')
            end
          end

          context 'when body text is blank or too long' do
            it 'raises error for blank body text' do
              params[:body] = ''
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Body text cannot be empty or more than 1024 characters.')
            end

            it 'raises error for body text > 1024 chars' do
              params[:body] = 'a' * 1025
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Body text cannot be empty or more than 1024 characters.')
            end
          end

          context 'when footer text is too long' do
            it 'raises error' do
              params[:footer] = 'a' * 61
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Footer text cannot be empty or more than 60 characters.')
            end
          end

          context 'when too many buttons' do
            it 'raises error' do
              params[:buttons] = 4.times.map { |i| { 'name' => "opt#{i}", 'text' => "Button #{i}", 'position' => i } }
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Too many buttons. Maximum 3 buttons are allowed.')
            end
          end

          context 'when button text is blank or too long' do
            it 'raises error for blank button text' do
              params[:buttons][0]['text'] = ''
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Button title must be restricted to 20 characters.')
            end

            it 'raises error for button text > 20 chars' do
              params[:buttons][0]['text'] = 'a' * 21
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::InvalidDataError, '022014||Button title must be restricted to 20 characters.')
            end
          end

          context 'when message type is list' do
            before { params.merge!(interactive_list_params) }

            context 'when there are more than 10 sections' do
              before do
                params[:sections] = 11.times.map { |i| { title: "Section #{i}", rows: [] } }
              end

              it 'raises error for too many sections' do
                expect {
                  described_class.new(params).send_interactive_message
                }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Too many sections. Maximum 10 sections are allowed.')
              end
            end

            context 'when a section title is longer than 24 characters' do
              before do
                params[:sections][0][:title] = 'A' * 25
              end

              it 'raises error for section title too long' do
                expect {
                  described_class.new(params).send_interactive_message
                }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Section title must be restricted to 24 characters.')
              end
            end

            context 'when a section has more than 10 rows' do
              before do
                params[:sections][0][:rows] = 11.times.map { |i| { id: "row#{i}", title: "Row #{i}", description: "Description #{i}" } }
              end

              it 'raises error for too many rows in section' do
                expect {
                  described_class.new(params).send_interactive_message
                }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Too many rows. Maximum 10 rows are allowed.')
              end
            end

            context 'when a row title is longer than 24 characters' do
              before do
                params[:sections][0][:rows][0][:title] = 'A' * 25
              end

              it 'raises error for row title too long' do
                expect {
                  described_class.new(params).send_interactive_message
                }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Row title must be restricted to 24 characters.')
              end
            end

            context 'when a row description is longer than 72 characters' do
              before do
                params[:sections][0][:rows][0][:description] = 'A' * 73
              end

              it 'raises error for row description too long' do
                expect {
                  described_class.new(params).send_interactive_message
                }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Row description must be restricted to 72 characters.')
              end
            end
          end
          
          context 'when entity does not have sms permission' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: false } }] }.to_json)
            end

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }
                .to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
            end
          end
          
          context 'when connected account not found' do
            before { params[:id] = -1 }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found.')
            end
          end

          context 'when inactive connected account' do
            before { connected_account.update(status: INACTIVE) }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
            end
          end

          context 'when unverified account' do
            before { connected_account.update(is_verified: false) }

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
            end
          end

          context 'when user does not have access to entity' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [] }.to_json)
            end

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::EntityNotFound, '022032||lead not found')
            end
          end

          context 'when session is inactive' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              conversation.update(last_message_received_at: 30.hours.ago)
              sub_conversation
              user_share_rule
            end

            it 'raises error' do
              expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Session is inactive.")
            end
          end
        end

        context 'when valid entity and message' do
          context 'when message type is button' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
                .with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    to: '+************',
                    type: 'interactive',
                    interactive: {
                      type: 'button',
                      body: {
                        text: 'Question to be asked'
                      },
                      action: {
                        buttons: [
                          {
                            type: 'reply',
                            reply: { id: 'opt1', title: 'What is your name?' }
                          }
                        ]
                      },
                      header: {
                        type: 'text',
                        text: 'Header text'
                      },
                      footer: {
                        text: 'Footer value'
                      }
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
            end

            context 'when conversation is not found for given phone number and connected account' do
              it 'raises error' do
                expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
              end
            end

            context 'when conversation is found for given phone number and connected account' do
              before(:each) do
                conversation.update(last_message_received_at: 10.hours.ago)
                sub_conversation
              end

              context 'when user does not have required conversation permission' do
                it 'raises error' do
                  expect { described_class.new(params).send_interactive_message }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
                end
              end

              context 'when user has required conversation permission' do
                before(:each) { user_share_rule }

                it 'sends and creates message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
                  expect { described_class.new(params).send_interactive_message }
                    .to change(Message, :count).by(1)
                    .and change(MessageLookUp, :count).by(1)
                    .and change(LookUp, :count).by(1)

                  message = Message.last
                  expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
                  expect(message.direction).to eq('outgoing')
                  expect(message.content).to eq("Header text\nQuestion to be asked\nFooter value\nWhat is your name?")
                  expect(message.recipient_number).to eq('+************')
                  expect(message.sender_number).to eq(connected_account.waba_number)
                  expect(message.message_type).to eq('whatsapp_business')
                  expect(message.tenant_id).to eq(user.tenant_id)
                  expect(message.sent_at.present?).to be_truthy
                  expect(message.medium).to eq('whatsapp')
                  expect(message.conversation_id).to eq(conversation.id)
                  expect(message.sub_conversation_id).to eq(sub_conversation.id)
                  expect(message.component_wise_content).to eq(
                    [
                      { "text"=>"What is your name?", "type"=>"BUTTON", "value"=>nil, "format"=>"QUICK_REPLY", "position"=>0 },
                      { "text"=>"Question to be asked", "type"=>"BODY", "value"=>nil, "format"=>"TEXT", "position"=>0 },
                      { "text"=>"Header text", "type"=>"HEADER", "value"=>nil, "format"=>"TEXT", "position"=>0 },
                      { "text"=>"Footer value", "type"=>"FOOTER", "value"=>nil, "format"=>"TEXT", "position"=>0 }
                    ]
                  )
                  lookup = message.related_to.first
                  expect(lookup.entity_type).to eq('lead')
                  expect(lookup.entity_id).to eq(123)
                  expect(lookup.phone_number).to eq('************')
                  expect(lookup.name).to eq('Lead Name')
                  expect(lookup.owner_id).to eq(4010)
                end

                it 'adds connected account id on message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
                  
                  described_class.new(params).send_interactive_message
                  expect(Message.last.connected_account_id).to eq(connected_account.id)
                end
              end

              # TODO: Add test case for header media interactive message
            end
          end

          context 'when message type is list' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
                .with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    to: '+************',
                    type: 'interactive',
                    interactive: {
                      type: 'list',
                      body: {
                        text: 'Question to be asked'
                      },
                      action: {
                        button: 'Click me',
                        sections: [
                          { title: 'Section 1', rows: [
                              { id: 'row1', title: 'Row 1', description: 'Description 1' },
                              { id: 'row2', title: 'Row 2', description: 'Description 2' }
                            ]
                          },
                          { title: 'Section 2', rows: [
                              { id: 'row3', title: 'Row 3', description: 'Description 3' },
                              { id: 'row4', title: 'Row 4', description: 'Description 4' }
                            ]
                          }
                        ]
                      },
                      header: {
                        type: 'text',
                        text: 'Header text'
                      },
                      footer: {
                        text: 'Footer value'
                      }
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
            end

            context 'when conversation is not found for given phone number and connected account' do
              it 'raises error' do
                expect { described_class.new(interactive_list_params).send_interactive_message }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
              end
            end

            context 'when conversation is found for given phone number and connected account' do
              before(:each) do
                conversation.update(last_message_received_at: 10.hours.ago)
                sub_conversation
              end

              context 'when user does not have required conversation permission' do
                it 'raises error' do
                  expect { described_class.new(interactive_list_params).send_interactive_message }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
                end
              end

              context 'when user has required conversation permission' do
                before(:each) { user_share_rule }

                it 'sends and creates message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                  expect { described_class.new(interactive_list_params).send_interactive_message }
                    .to change(Message, :count).by(1)
                    .and change(MessageLookUp, :count).by(1)
                    .and change(LookUp, :count).by(1)

                  message = Message.last
                  expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
                  expect(message.direction).to eq('outgoing')
                  expect(message.content).to eq("Header text\nQuestion to be asked\nFooter value\nClick me")
                  expect(message.recipient_number).to eq('+************')
                  expect(message.sender_number).to eq(connected_account.waba_number)
                  expect(message.message_type).to eq('whatsapp_business')
                  expect(message.tenant_id).to eq(user.tenant_id)
                  expect(message.sent_at.present?).to be_truthy
                  expect(message.medium).to eq('whatsapp')
                  expect(message.conversation_id).to eq(conversation.id)
                  expect(message.sub_conversation_id).to eq(sub_conversation.id)
                  expect(message.component_wise_content).to eq(
                    [
                      {"text"=>"Click me", "type"=>"BUTTON", "value"=>nil, "format"=>"LIST_MENU_BUTTON", "position"=>0},
                      {"text"=>"Row 1", "type"=>"LIST", "value"=>nil, "format"=>"LIST_ROW", "position"=>0, "description"=>"Description 1", "section_title"=>"Section 1"},
                      {"text"=>"Row 2", "type"=>"LIST", "value"=>nil, "format"=>"LIST_ROW", "position"=>1, "description"=>"Description 2", "section_title"=>"Section 1"},
                      {"text"=>"Row 3", "type"=>"LIST", "value"=>nil, "format"=>"LIST_ROW", "position"=>2, "description"=>"Description 3", "section_title"=>"Section 2"},
                      {"text"=>"Row 4", "type"=>"LIST", "value"=>nil, "format"=>"LIST_ROW", "position"=>3, "description"=>"Description 4", "section_title"=>"Section 2"},
                      {"text"=>"Question to be asked", "type"=>"BODY", "value"=>nil, "format"=>"TEXT", "position"=>0},
                      {"text"=>"Header text", "type"=>"HEADER", "value"=>nil, "format"=>"TEXT", "position"=>0},
                      {"text"=>"Footer value", "type"=>"FOOTER", "value"=>nil, "format"=>"TEXT", "position"=>0}
                    ]
                  )
                  lookup = message.related_to.first
                  expect(lookup.entity_type).to eq('lead')
                  expect(lookup.entity_id).to eq(123)
                  expect(lookup.phone_number).to eq('************')
                  expect(lookup.name).to eq('Lead Name')
                  expect(lookup.owner_id).to eq(4010)
                end

                it 'adds connected account id on message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                  described_class.new(interactive_list_params).send_interactive_message
                  expect(Message.last.connected_account_id).to eq(connected_account.id)
                end
              end
            end
          end

          context 'when message type is cta_url' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
                .with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    to: '+************',
                    type: 'interactive',
                    interactive: {
                      type: 'cta_url',
                      body: {
                        text: 'Question to be asked'
                      },
                      action: {
                        name: 'cta_url',
                        parameters: {
                          display_text: 'Display Text',
                          url: 'https://example.com'
                        }
                      },
                      header: {
                        type: 'text',
                        text: 'Header text'
                      },
                      footer: {
                        text: 'Footer value'
                      }
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
            end

            context 'when conversation is not found for given phone number and connected account' do
              it 'raises error' do
                expect { described_class.new(cta_url_params).send_interactive_message }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
              end
            end

            context 'when conversation is found for given phone number and connected account' do
              before(:each) do
                conversation.update(last_message_received_at: 10.hours.ago)
                sub_conversation
              end

              context 'when user does not have required conversation permission' do
                it 'raises error' do
                  expect { described_class.new(cta_url_params).send_interactive_message }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
                end
              end

              context 'when user has required conversation permission' do
                before(:each) { user_share_rule }

                it 'sends and creates message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                  expect { described_class.new(cta_url_params).send_interactive_message }
                    .to change(Message, :count).by(1)
                    .and change(MessageLookUp, :count).by(1)
                    .and change(LookUp, :count).by(1)

                  message = Message.last
                  expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
                  expect(message.direction).to eq('outgoing')
                  expect(message.content).to eq("Header text\nQuestion to be asked\nFooter value\nDisplay Text")
                  expect(message.recipient_number).to eq('+************')
                  expect(message.sender_number).to eq(connected_account.waba_number)
                  expect(message.message_type).to eq('whatsapp_business')
                  expect(message.tenant_id).to eq(user.tenant_id)
                  expect(message.sent_at.present?).to be_truthy
                  expect(message.medium).to eq('whatsapp')
                  expect(message.conversation_id).to eq(conversation.id)
                  expect(message.sub_conversation_id).to eq(sub_conversation.id)
                  expect(message.component_wise_content).to eq(
                    [
                      {"text"=>"Question to be asked", "type"=>"BODY", "value"=>nil, "format"=>"TEXT", "position"=>0},
                      {"text"=>"Header text", "type"=>"HEADER", "value"=>nil, "format"=>"TEXT", "position"=>0},
                      {"text"=>"Footer value", "type"=>"FOOTER", "value"=>nil, "format"=>"TEXT", "position"=>0},
                      {"text"=>"Display Text", "type"=>"BUTTON", "value"=>"https://example.com", "format"=>"CTA_URL", "position"=>0}
                    ]
                    
                  )
                  lookup = message.related_to.first
                  expect(lookup.entity_type).to eq('lead')
                  expect(lookup.entity_id).to eq(123)
                  expect(lookup.phone_number).to eq('************')
                  expect(lookup.name).to eq('Lead Name')
                  expect(lookup.owner_id).to eq(4010)
                end

                it 'adds connected account id on message' do
                  expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

                  described_class.new(cta_url_params).send_interactive_message
                  expect(Message.last.connected_account_id).to eq(connected_account.id)
                end
              end
            end
          end
        end
      end
    end

    context 'when session interactive message send' do
      it 'should properly update a status of conversation and sub conversation' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        user_share_rule

        # Create an incoming message to trigger status update for conversation and sub conversation
        create(:message, sub_conversation_id: sub_conversation.id, direction: 'incoming', conversation_id: conversation.id, tenant_id: user.tenant_id, connected_account_id: conversation.connected_account_id)

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{@token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              to: '+************',
              type: 'interactive',
              interactive: {
                type: 'button',
                body: {
                  text: 'Question to be asked'
                },
                action: {
                  buttons: [
                    {
                      type: 'reply',
                      reply: { id: 'opt1', title: 'What is your name?' }
                    }
                  ]
                },
                header: {
                  type: 'text',
                  text: 'Header text'
                },
                footer: {
                  text: 'Footer value'
                }
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(params).send_interactive_message
        }.to change { sub_conversation.reload.status }.from(NEW).to(IN_PROGRESS)
         .and change { conversation.reload.status }.from(NEW).to(IN_PROGRESS)
      end

      it 'should create a new sub conversation and update conversation status to NEW when no ongoing sub conversation exists' do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        whatsapp_credit
        # Create conversation without ongoing sub_conversation
        conversation.update(last_message_received_at: 10.hours.ago, status: COMPLETED)
        # Ensure no ongoing sub_conversation
        SubConversation.where(conversation_id: conversation.id).update_all(status: COMPLETED)
        user_share_rule

        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              content_type: 'application/json'
            }
          ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              to: '+************',
              type: 'interactive',
              interactive: {
                type: 'button',
                body: {
                  text: 'Question to be asked'
                },
                action: {
                  buttons: [
                    {
                      type: 'reply',
                      reply: { id: 'opt1', title: 'What is your name?' }
                    }
                  ]
                },
                header: {
                  type: 'text',
                  text: 'Header text'
                },
                footer: {
                  text: 'Footer value'
                }
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

        expect {
          described_class.new(params).send_interactive_message
        }.to change(SubConversation, :count).by(1)
         .and change { conversation.reload.status }.from(COMPLETED).to(NEW)

        new_sub_conversation = SubConversation.last
        expect(new_sub_conversation.tenant_id).to eq(connected_account.tenant_id)
        expect(new_sub_conversation.connected_account_id).to eq(connected_account.id)
        expect(new_sub_conversation.conversation_id).to eq(conversation.id)
        expect(new_sub_conversation.status).to eq(NEW)
      end
    end
  end
end
