require 'rails_helper'

RSpec.describe Message::DeleteMessageService do
  before do
    @user = create(:user)
    @user_from_other_tenant = create(:user)
    auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

    thread = Thread.current
    thread[:auth] = auth_data
    thread[:user] = @user

    @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
    thread[:token] = @token
  end

  describe '#call' do
    context 'when sufficient permissions are avaliable to delete message' do
      let(:permission) { :sms_with_delete_permission }

      before { @message = create(:message, owner: @user, tenant_id: @user.tenant_id) }

      context 'when skip_event_publishing is false (default)' do
        it 'deletes message and child entities and publishes event' do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          Message::DeleteMessageService.call(@message.id)

          expect(Message.find_by_id(@message.id)).to be_nil
          expect(MessageLookUp.find_by_message_id(@message.id)).to be_nil
          expect(Attachment.find_by_message_id(@message.id)).to be_nil
          expect(Publishers::MessageDeleted).to have_received(:call)
        end

        context 'when message is whatsapp business message' do
          let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id) }
          let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, connected_account_id: connected_account.id) }
          let(:look_up) { create(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id) }
          let(:conversation_look_up) { create(:conversation_look_up, conversation_id: conversation.id, tenant_id: @user.tenant_id, look_up_id: look_up.id) }
          let(:contact_deal_association) { create(:contact_deal_association, contact_id: look_up.entity_id, deal_name: 'Software License Deal', tenant_id: @user.tenant_id) }
          let(:message) { create(:message, conversation_id: conversation.id, tenant_id: @user.tenant_id, message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id, owner: @user) }
          let(:deleted_at){ DateTime.now }

          before do
            conversation_look_up
            contact_deal_association
            allow(DateTime).to receive(:now).and_return(deleted_at)
          end

          it 'publishes event with related deals' do
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
            allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
            Message::DeleteMessageService.call(message.id)

            expect(Message.find_by_id(message.id)).to be_nil
            expect(MessageLookUp.find_by_message_id(message.id)).to be_nil
            expect(Attachment.find_by_message_id(message.id)).to be_nil
            expect(Publishers::MessageDeleted).to have_received(:call).with(@user.tenant_id, @user.id, {
              "id"=> message.id,
              "tenantId"=> @user.tenant_id,
              "content"=>"Sample text",
              "medium"=>"whatsapp",
              "direction"=>message.direction,
              "sentAt"=>message.sent_at.iso8601(3),
              "deliveredAt"=>message.delivered_at.iso8601(3),
              "readAt"=>message.read_at.iso8601(3),
              "recipientNumber"=>"12312312",
              "senderNumber"=>"12312313",
              "createdAt"=>message.created_at.iso8601(3),
              "updatedAt"=>message.updated_at.iso8601(3),
              "messageType"=>"whatsapp_business",
              "statusMessage"=>nil,
              "conversationId"=>conversation.id,
              "status"=>"Sent",
              "subConversationId"=>nil,
              "owner"=>{"id"=>@user.id, "name"=>@user.name},
              "relatedTo"=>[
                {
                  "name"=>look_up.name,
                  "id"=>look_up.entity_id,
                  "entity"=>look_up.entity_type,
                  "ownerId"=>look_up.owner_id
                },
                {
                  "id"=>contact_deal_association.deal_id,
                  "name"=>"Software License Deal",
                  "entity"=>"deal"
                }
              ],
              "components"=>[],
              "recipients"=>[],
              "attachments"=>[],
              "deletedBy"=>{"id"=>@user.id, "name"=>@user.name},
              "deletedAt"=>deleted_at.utc.iso8601(3)
            })
          end
        end
      end

      context 'when skip_event_publishing is true' do
        it 'deletes message and child entities but does not publish event' do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          Message::DeleteMessageService.new(@message.id, true, {}, true).call

          expect(Message.find_by_id(@message.id)).to be_nil
          expect(MessageLookUp.find_by_message_id(@message.id)).to be_nil
          expect(Attachment.find_by_message_id(@message.id)).to be_nil
          expect(Publishers::MessageDeleted).not_to have_received(:call)
        end
      end

      context 'when message has attachments' do
        before { @attachment = create(:attachment, message: @message) }

        it 'deletes attachments from S3' do
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
          Message::DeleteMessageService.call(@message.id)

          expect(S3::DeleteFileFromS3).to have_received(:call).with([@attachment.file_name], S3_ATTACHMENT_BUCKET)
        end
      end
    end

    context 'when message belongs to different tenant' do
      let(:permission) { :sms_with_delete_permission }

      before{ @message = create(:message, owner: @user_from_other_tenant, tenant_id: @user_from_other_tenant.tenant_id)}

      it 'throws not found error' do
        expect{ Message::DeleteMessageService.call(@message.id) }.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    end

    context 'when delete permission is not available' do
      let(:permission) { :sms_without_delete_permission }

      before{ @message = create(:message, owner: @user, tenant_id: @user.tenant_id)}

      it 'throws delete not allowed error' do
        expect{ Message::DeleteMessageService.call(@message.id) }.to raise_error(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed)
      end
    end
  end

  describe '#soft_delete' do
    context 'when sufficient permissions are available to delete message' do
      let(:permission) { :sms_with_delete_permission }
      let(:look_up) { create(:look_up, entity_type: LOOKUP_LEAD) }
      before do
        @message = create(:message, owner: @user, tenant_id: @user.tenant_id)
        @message.related_to << look_up
        @message.attachments << FactoryBot.create(:attachment, message: @message)
      end

      it 'soft deletes message and publishes event' do
        allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
        Message::DeleteMessageService.new(@message.id).soft_delete

        expect(@message.reload.deleted_at).not_to be_nil
        expect(MessageLookUp.find_by_message_id(@message.id)).not_to be_nil
        expect(Attachment.find_by_message_id(@message.id)).not_to be_nil
        expect(Publishers::MessageDeleted).to have_received(:call)
      end

      context 'when message is whatsapp business message' do
        let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id) }
        let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, connected_account_id: connected_account.id) }
        let(:contact_look_up) { create(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id) }
        let(:conversation_look_up) { create(:conversation_look_up, conversation_id: conversation.id, tenant_id: @user.tenant_id, look_up_id: contact_look_up.id) }
        let(:contact_deal_association) { create(:contact_deal_association, contact_id: contact_look_up.entity_id, deal_name: 'Software License Deal', tenant_id: @user.tenant_id) }
        let(:message) { create(:message, conversation_id: conversation.id, tenant_id: @user.tenant_id, message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id, owner: @user) }
        let(:deleted_at){ DateTime.now }

        before do
          conversation_look_up
          contact_deal_association
          allow(DateTime).to receive(:now).and_return(deleted_at)
        end

        it 'publishes event with related deals' do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          Message::DeleteMessageService.call(message.id)

          expect(Message.find_by_id(message.id)).to be_nil
          expect(MessageLookUp.find_by_message_id(message.id)).to be_nil
          expect(Attachment.find_by_message_id(message.id)).to be_nil
          expect(Publishers::MessageDeleted).to have_received(:call).with(@user.tenant_id, @user.id, {
            "id"=> message.id,
            "tenantId"=> @user.tenant_id,
            "content"=>"Sample text",
            "medium"=>"whatsapp",
            "direction"=>message.direction,
            "sentAt"=>message.sent_at.iso8601(3),
            "deliveredAt"=>message.delivered_at.iso8601(3),
            "readAt"=>message.read_at.iso8601(3),
            "recipientNumber"=>"12312312",
            "senderNumber"=>"12312313",
            "createdAt"=>message.created_at.iso8601(3),
            "updatedAt"=>message.updated_at.iso8601(3),
            "messageType"=>"whatsapp_business",
            "statusMessage"=>nil,
            "conversationId"=>conversation.id,
            "status"=>"Sent",
            "subConversationId"=>nil,
            "owner"=>{"id"=>@user.id, "name"=>@user.name},
            "relatedTo"=>[
              {
                "name"=>contact_look_up.name,
                "id"=>contact_look_up.entity_id,
                "entity"=>contact_look_up.entity_type,
                "ownerId"=>contact_look_up.owner_id
              },
              {
                "id"=>contact_deal_association.deal_id,
                "name"=>"Software License Deal",
                "entity"=>"deal"
              }
            ],
            "components"=>[],
            "recipients"=>[],
            "attachments"=>[],
            "deletedBy"=>{"id"=>@user.id, "name"=>@user.name},
            "deletedAt"=>deleted_at.utc.iso8601(3)
          })
        end
      end

      context 'when message has attachments' do
        before { @attachment = create(:attachment, message: @message) }

        it 'does not delete attachments from S3' do
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
          Message::DeleteMessageService.new(@message.id).soft_delete

          expect(S3::DeleteFileFromS3).not_to have_received(:call)
        end
      end

      context 'when message has sub_conversation_id' do
        let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id) }
        let(:conversation) { create(:conversation, tenant_id: @user.tenant_id, connected_account_id: connected_account.id) }
        let(:sub_conversation) { create(:sub_conversation, tenant_id: @user.tenant_id, connected_account_id: connected_account.id, conversation_id: conversation.id) }
        let(:message_with_sub) { create(:message, owner: @user, tenant_id: @user.tenant_id, sub_conversation_id: sub_conversation.id) }

        context 'when no other messages in sub_conversation' do
          it 'deletes the sub_conversation and updates conversation status to COMPLETED' do
            allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
            Message::DeleteMessageService.new(message_with_sub.id).soft_delete

            expect(SubConversation.find_by(id: sub_conversation.id)).to be_nil
            expect(message_with_sub.reload.deleted_at).not_to be_nil
            expect(conversation.reload.status).to eq(COMPLETED)
          end

          it 'delete the sub conversation but does not update conversation status' do
            allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
            sub_conversation.update(status: COMPLETED)
            Message::DeleteMessageService.new(message_with_sub.id).soft_delete

            expect(SubConversation.find_by(id: sub_conversation.id)).to be_nil
            expect(message_with_sub.reload.deleted_at).not_to be_nil
            expect(conversation.reload.status).to eq(NEW)
          end
        end

        context 'when there are other messages in sub_conversation' do
          before { create(:message, owner: @user, tenant_id: @user.tenant_id, sub_conversation_id: sub_conversation.id) }

          it 'does not delete the sub_conversation' do
            allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
            Message::DeleteMessageService.new(message_with_sub.id).soft_delete

            expect(SubConversation.find_by(id: sub_conversation.id)).not_to be_nil
            expect(message_with_sub.reload.deleted_at).not_to be_nil
          end
        end
      end
    end

    context 'when message belongs to different tenant' do
      let(:permission) { :sms_with_delete_permission }

      before{ @message = create(:message, owner: @user_from_other_tenant, tenant_id: @user_from_other_tenant.tenant_id)}

      it 'throws not found error' do
        expect{ Message::DeleteMessageService.new(@message.id).soft_delete }.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    end

    context 'when delete permission is not available' do
      let(:permission) { :sms_without_delete_permission }

      before{ @message = create(:message, owner: @user, tenant_id: @user.tenant_id)}

      it 'throws delete not allowed error' do
        expect{ Message::DeleteMessageService.new(@message.id).soft_delete }.to raise_error(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed)
      end
    end
  end
end
