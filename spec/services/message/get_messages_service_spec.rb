require 'rails_helper'

RSpec.describe Message::GetMessagesService do
  describe '#call' do
    before(:all) do
      user = create(:user)
      token = FactoryBot.build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token
      auth_data = User::TokenParser.parse(token)
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = token
      thread[:user] = user

      @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)

      connected_account = create(:connected_account, tenant_id: user.tenant_id)
      @messages = create_list(:message, 3, owner: user, tenant_id: user.tenant_id)
      @whatsapp_message = create(:message, owner: user, tenant_id: user.tenant_id, message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id)
      
      @messages.first(2).each do |m|
        m.related_to = [@look_up]
      end
      @whatsapp_message.related_to = [@look_up]
    end

    let(:related_to_rule)  { { 'type': 'related_to_lookup', 'field': 'related_to', 'id': 'related_to', 'operator': 'equal', 'value': { 'id': @look_up.entity_id, 'entity': @look_up.entity_type }}}

    describe 'call' do
      context 'filter - only related_to' do
        before do
          rules =  { "json_rule": { "condition": 'AND', "rules": [related_to_rule] }}
          @messages = Message::GetMessagesService.call(rules.with_indifferent_access)
        end

        it 'checks count' do
          expect(@messages.count).to eq 2
        end

        it 'returns correct messages details in the response' do
          expect(@messages.map { |m| m.id}).to match_array(@messages.map(&:id))
        end
      end
    end
  end
end

