require 'rails_helper'

RSpec.describe Message::GetMessageDetailsService do
  describe '#call' do
    before { @user = create(:user, id: 12, tenant_id: 99) }

    context 'Success' do
      let(:permission) { :lead_with_sms_permission }

      before do
        auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

        thread = Thread.current
        thread[:auth] = auth_data
        thread[:user] = @user

        token = build(:auth_token, user_id: 12, tenant_id: 99).token
        thread[:token] = @token
      end

      it 'returns message' do
        look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        message = create(:message, owner: @user, tenant_id: @user.tenant_id)
        message.related_to << look_up
        result = Message::GetMessageDetailsService.call(message.id)
        expect(result.id).to eq message.id
      end
    end

    context "Insufficient permission" do
      before do
        auth_data = build(:auth_data, :lead_without_sms_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

        thread = Thread.current
        thread[:auth] = auth_data
        thread[:user] = @user
      end

      it "throws forbidden error" do
        look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        message = create(:message, owner: @user, tenant_id: @user.tenant_id)
        message.related_to << look_up
        expect { Message::GetMessageDetailsService.call(message.id) }.to raise_error(ExceptionHandler::MessageNotAllowedError).with_message(ErrorCode.message_not_allowed)
      end

      context 'when skip_permission_check is true' do
        it 'returns message' do
          look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
          message = create(:message, owner: @user, tenant_id: @user.tenant_id)
          message.related_to << look_up
          expect(Message::GetMessageDetailsService.call(message.id, skip_permission_check: true)).to eq(message)
        end
      end
    end
  end
end
