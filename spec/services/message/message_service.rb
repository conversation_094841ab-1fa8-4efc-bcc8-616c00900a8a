require 'rails_helper'

RSpec.describe Message::MessageService do
  describe '#call' do
    let!(:user){ User.create(id: 1, tenant_id: 99, name: '<PERSON>')}
    let!(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
    let(:headers){
      {
        'Authorization' => "Bearer #{valid_auth_token}",
        'Content-Type' => 'application/json'
      }
    }

    let(:invalid_headers){
      {
        'Authorization' => "Bearer #{invalid_jwt}",
        'Content-Type' => 'application/json'
      }
    }

    before do
      Thread.current[:user] = user
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:auth] = auth_data
    end

    context "when tried to create message for lead" do
      context 'Success' do
        let(:look_up) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id) }

        before do
          @params = {
            content: 'This is test message',
            direction: 'outgoing',
            medium: 'whatsapp',
            owner_id: user.id,
            sent_at: DateTime.now,
            delivered_at: DateTime.now,
            read_at: DateTime.now,
            sender_number: '*********',
            recipient_number: '**********',
            status: 'read',
            related_to: [{ entity: 'lead', id: look_up.entity_id, name: "test entity", phone_number: look_up.phone_number }],
            recipients: [{ entity: 'lead', id: look_up.entity_id, name: "test entity", phone_number: look_up.phone_number }]
          }

          stub_request(:get, SERVICE_SALES + "/v1/leads/#{look_up.entity_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{valid_auth_token.token}"
            }).
            to_return(status: 200, body: {"id": look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})

          allow(Publishers::MessageCreated).to receive(:call)

          @res = Message::MessageService.call(@params.dup)
        end

        it 'creates message db' do
          expect(Message.count).to eq 1
        end

        it 'stores correct data' do
          expect(@res).to eq(Message.last.id)
          msg = Message.last
          expect(msg.direction).to eq(@params[:direction])
          expect(msg.medium).to eq(@params[:medium])
          expect(msg.owner_id).to eq(user.id)
          expect(msg.sent_at).not_to be_nil
          expect(msg.delivered_at).not_to be_nil
          expect(msg.recipients.count).to eq(1)
          expect(msg.recipients.first.entity_type).to eq('lead')
        end
      end

      context 'Failure - with invalid input' do
        before do
          @params = {
            direction: 'new',
            related_to: [{ entity: 'lead', id: 1, name: "test entity", phone_number: "1233231231" }],
          }
        end

        it 'throws error' do
          expect { Message::MessageService.call(@params) }.to raise_error(ExceptionHandler::InvalidDataError,
                                                                            "#{ErrorCode.invalid_data}||'new' is not a valid direction")
          expect(Message.count).to eq 0
        end
      end
    end

    context 'when creating a WhatsApp business message' do
      let(:look_up) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id) }
      let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE) }
      let(:conversation) { create(:conversation, tenant_id: user.tenant_id, last_activity_at: 10.hours.ago, connected_account_id: connected_account.id)}
      let(:sub_conversation) { create(:sub_conversation, conversation_id: conversation.id, tenant_id: user.tenant_id) }
      let(:updated_time) { DateTime.now.utc }
      let(:message_params) do
        {
          content: 'WhatsApp test message',
          direction: 'outgoing',
          medium: 'whatsapp',
          owner_id: user.id,
          sent_at: updated_time,
          sender_number: '*********',
          recipient_number: '**********',
          status: 'sending',
          message_type: WHATSAPP_BUSINESS,
          conversation_id: conversation.id,
          sub_conversation_id: sub_conversation.id,
          connected_account_id: connected_account.id,
          related_to: [{ entity: 'lead', id: look_up.entity_id, name: "test entity", phone_number: look_up.phone_number }],
          recipients: [{ entity: 'lead', id: look_up.entity_id, name: "test entity", phone_number: look_up.phone_number }]
        }
      end

      before do
        stub_request(:get, SERVICE_SALES + "/v1/leads/#{look_up.entity_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{valid_auth_token.token}"
            }).
            to_return(status: 200, body: {"id": look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})

        allow(Publishers::MessageCreated).to receive(:call)
      end

      it 'updates conversation last_activity_at' do 
        original_time = conversation.last_activity_at

        Message::MessageService.call(message_params)
        
        conversation.reload
        expect(conversation.last_activity_at).to be > original_time
      end

      context 'when owner token is not present' do
        context 'when user has sms write permission' do  
          before do
            message_params[:related_to] = []
            message_params[:recipients] = []
          end

          it 'creates the message successfully' do
            message_id = Message::MessageService.call(message_params)
            message = Message.find(message_id)
            expect(message).to be_present
            expect(message.medium).to eq(message_params[:medium])
            expect(message.direction).to eq(message_params[:direction])
            expect(message.status).to eq(message_params[:status])
          end
        end
      end

      context 'conversation status update and event publishing' do
        let(:event) { instance_double(Event::WhatsappMessageCreatedV2) }

        before do
          message_params[:status] = 'sent'
          allow(PublishEvent).to receive(:call)
          create(:message, conversation_id: conversation.id, content: 'Hello!', direction: 'incoming', tenant_id: user.tenant_id, owner_id: user.id, connected_account_id: connected_account.id, sub_conversation_id: sub_conversation.id )
          allow(Event::WhatsappMessageCreatedV2).to receive(:new).and_return(event)
          allow(event).to receive(:to_json)
        end

        it 'updates conversation status and publishes event with correct status in payload if there is two way communication' do
          original_status = conversation.status
          expect(conversation.status).to eq(original_status)

          Message::MessageService.call(message_params.merge(message_type: WHATSAPP_BUSINESS))
          
          expect(conversation.reload.status).to eq(IN_PROGRESS)

          expect(Event::WhatsappMessageCreatedV2).to have_received(:new) do |payload|
            expect(payload['entity']['messageConversationStatus']).to eq(IN_PROGRESS)
          end
        end

        it 'updates conversation status and publishes event with correct status in payload if there is one way communication' do
          original_status = conversation.status
          expect(conversation.status).to eq(original_status)

          Message::MessageService.call(message_params.merge(message_type: WHATSAPP_BUSINESS, direction: 'incoming'))
          
          expect(conversation.reload.status).to eq(NEW)

          expect(Event::WhatsappMessageCreatedV2).to have_received(:new) do |payload|
            expect(payload['entity']['messageConversationStatus']).to eq(NEW)
          end
        end

        context 'when conversation status is already IN_PROGRESS' do
          before { conversation.update(status: IN_PROGRESS) }

          it 'still updates status if needed and publishes with correct status' do
            Message::MessageService.call(message_params.merge(message_type: WHATSAPP_BUSINESS))

            expect(Event::WhatsappMessageCreatedV2).to have_received(:new) do |payload|
              expect(payload['entity']['messageConversationStatus']).to eq(conversation.reload.status)
            end
          end
        end

        context 'when conversation status is COMPLETED' do
          before { conversation.update(status: COMPLETED) }

          it 'still updates status if needed and publishes with correct status' do
            Message::MessageService.call(message_params.merge(message_type: WHATSAPP_BUSINESS))

            expect(Event::WhatsappMessageCreatedV2).to have_received(:new) do |payload|
              expect(payload['entity']['messageConversationStatus']).to eq(conversation.reload.status)
            end
          end
        end
      end
    end
  end
end
