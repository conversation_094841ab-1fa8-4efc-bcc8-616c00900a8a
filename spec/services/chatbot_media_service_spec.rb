# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChatbotMediaService do
  let(:user) { create(:user) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data) { User::TokenParser.parse(invalid_auth_token.token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:sample_text_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_text.txt') }
  let(:chatbot_media) { create(:chatbot_media, file_name: "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/file_ab_c.png", tenant_id: user.tenant_id, chatbot_id: 234, connected_account_id: connected_account.id) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  
  describe '#upload' do
    context 'when media file is not present' do
      it 'raises invalid data error' do
        expect { described_class.new({}).upload }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
      end
    end

    context 'with valid token' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:user] = user
      end

      context 'when connected account is not found' do
        it 'raises not found error' do
          expect { described_class.new({ media_file: sample_png_3mb, connected_account_id: -1 }).upload }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
        end
      end

      context 'when connected accout is present' do
        context 'and current user is not an agent in connected account' do
          it 'raises invalid data error' do
            expect { described_class.new({ media_file: sample_png_3mb, connected_account_id: connected_account.id }).upload }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
          end
        end

        context 'and current user is an agent in connected account' do
          before do
            create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
          end

          context 'when media type is valid' do
            before do
              allow(SecureRandom).to receive(:uuid).and_return('48a02e4f-aef4-42e2-a2ad-de97b9004106')

              file_name = "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_#{SecureRandom.uuid}.png"
              resource = instance_double(Aws::S3::Resource)
              bucket = instance_double(Aws::S3::Bucket)
              allow(Aws::S3::Resource).to receive(:new).and_return(resource)
              allow(instance_double(S3::UploadFile)).to receive(:call).and_return(nil)
              allow(resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(bucket)
              allow(bucket).to receive(:object).with(file_name).and_return(instance_double(Aws::S3::Object))
              allow(bucket.object(file_name)).to receive(:upload_file).with(sample_png_3mb.path)
            end

            it 'uploads file to s3' do
              described_class.new({ media_file: sample_png_3mb, media_type: 'image', connected_account_id: connected_account.id }).upload
            end

            it 'creates chatbot_media object' do
              chatbot_media = described_class.new({ media_file: sample_png_3mb, media_type: 'image', connected_account_id: connected_account.id }).upload
              expect(chatbot_media.file_name).to eq("tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_#{SecureRandom.uuid}.png")
              expect(chatbot_media.file_type).to eq('image')
            end
          end
        end
      end
    end
  end

  describe '#list' do
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when connected account is not found' do
      it 'raises not found error' do
        expect { described_class.new({ connected_account_id: -1 }).list }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when connected accout is present' do
      context 'and current user is not an agent in connected account' do
        it 'raises invalid data error' do
          expect { described_class.new({ connected_account_id: connected_account.id }).list }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
        end
      end

      context 'and current user is an agent in connected account' do
        before do
          create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
        end

        context 'when chatbot media is valid and no chatbot medias are present' do
          it 'returns empty array' do
            chatbot_medias = described_class.new({ connected_account_id: connected_account.id, chatbot_id: 123 }).list
            expect(chatbot_medias).to eq([])
          end
        end

        context 'when chatbot medias are present' do
          before do
            chatbot_media
            s3_instance = instance_double(S3::GetPresignedUrl)
            allow(S3::GetPresignedUrl).to receive(:new).with("tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/file_ab_c.png", 'file_ab_c.png', S3_ATTACHMENT_BUCKET, nil).and_return(s3_instance)
            allow(s3_instance).to receive(:call).and_return('https://www.aws.com/file_url.png')
          end

          it 'returns list of chatbot medias' do
            chatbot_medias = described_class.new({ connected_account_id: connected_account.id, chatbot_id: 234 }).list
            expect(chatbot_medias.first).to eq(
              { 
                :id=>chatbot_media.id, 
                :fileName=>"file_ab.png", 
                :fileSize=>1024, 
                :fileType=>"image", 
                :mediaUrl=>"https://www.aws.com/file_url.png"
              }
            )
          end
        end
      end
    end
  end
end
