# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappCreditsService do
  describe '#add' do
    context 'when valid parameters' do
      context 'when whatsapp credits are updated' do
        context 'when credit info is not present' do
          before do
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end

          it 'should add credit and credit history' do
            allow(Time).to receive(:now).and_return(Time.new(2024, 8, 15, 3, 0, 0))
            expect { described_class.new.add(1, 1000) }
              .to change(WhatsappCredit, :count).by(1)
              .and change(WhatsappCreditHistory, :count).by(1)

            whatsapp_credit = WhatsappCredit.last
            expect(whatsapp_credit.total).to eq(1000.00)
            expect(whatsapp_credit.consumed).to eq(0.00)

            whatsapp_credit_history = WhatsappCreditHistory.last
            expect(whatsapp_credit_history.entry_type).to eq('CREDITS_ADDED')
            expect(whatsapp_credit_history.value).to eq(1000.00)
            expect(whatsapp_credit_history.balance).to eq(1000.00)
            expect(whatsapp_credit_history.start_time).to eq(1723671000)
            expect(whatsapp_credit_history.end_time).to eq(1723671000)
          end
        end

        context 'when existing credit info is present' do
          let(:existing_whatsapp_credit) { create(:whatsapp_credit, total: 1000, consumed: 100) }

          before(:each) do
            existing_whatsapp_credit
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end

          it 'updates credits for given tenant_id' do
            expect { described_class.new.add(existing_whatsapp_credit.tenant_id, 500) }
              .to change(WhatsappCredit, :count).by(0)

            whatsapp_credit = WhatsappCredit.find_by(tenant_id: existing_whatsapp_credit.tenant_id)

            expect(whatsapp_credit.total).to eq(500.00)
            expect(whatsapp_credit.consumed).to eq(0.00)
          end

          it 'creates credit history log' do
            allow(Time).to receive(:now).and_return(Time.new(2024, 8, 15, 3, 0, 0))
            expect { described_class.new.add(existing_whatsapp_credit.tenant_id, 500) }
              .to change(WhatsappCreditHistory, :count).by(1)

            whatsapp_credit_history = WhatsappCreditHistory.where(tenant_id: existing_whatsapp_credit.tenant_id).last

            expect(whatsapp_credit_history.entry_type).to eq('CREDITS_ADDED')
            expect(whatsapp_credit_history.value).to eq(400.00)
            expect(whatsapp_credit_history.balance).to eq(500.00)
            expect(whatsapp_credit_history.start_time).to eq(1723671000)
            expect(whatsapp_credit_history.end_time).to eq(1723671000)
          end

          it 'resets is_low_credits_email_sent' do
            existing_whatsapp_credit.update(is_low_credits_email_sent: true)

            expect { described_class.new.add(existing_whatsapp_credit.tenant_id, 500) }
              .to change(WhatsappCredit, :count).by(0)

            whatsapp_credit = WhatsappCredit.find_by(tenant_id: existing_whatsapp_credit.tenant_id)

            expect(whatsapp_credit.total).to eq(500.00)
            expect(whatsapp_credit.consumed).to eq(0.00)
            expect(whatsapp_credit.is_low_credits_email_sent).to eq(false)
          end
        end
      end
    end

    context 'when transaction fails' do
      let(:whatsapp_credit) { create(:whatsapp_credit, total: 1000, consumed: 100) }

      before do
        allow(WhatsappCreditHistory).to receive(:create).and_raise(ActiveRecord::RecordInvalid)
        expect(Rails.logger).to receive(:error).with("WhatsappCreditsService Error while adding credits Tenant id #{whatsapp_credit.tenant_id} message: Record invalid")
      end

      it 'should log error' do
        prev_consumed = whatsapp_credit.consumed
        expect { described_class.new.add(whatsapp_credit.tenant_id, 1000) }
          .not_to change(WhatsappCreditHistory, :count)

        expect(whatsapp_credit.reload.consumed).to eq(prev_consumed)
      end
    end
  end

  describe '#update' do
    let(:user){ create(:user) }
    let(:start_time) { Date.yesterday.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE, credits_revised_upto: start_time) }
    let(:second_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE) }
    let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: connected_account.tenant_id, total: 1000, consumed: 0, credits_revised_at: start_time) }
    let(:conversation_analytics_success_response) do
      {
        "pricing_analytics": {
          "data": [
            {
              "data_points": [
                {
                  "start": **********,
                  "end": **********,
                  "volume": 3,
                  "phone_number": "************",
                  "country": "IN",
                  "pricing_type": "FREE_TIER",
                  "pricing_category": "SERVICE",
                  "cost": 0
                },
                {
                  "start": **********,
                  "end": **********,
                  "volume": 1,
                  "phone_number": "************",
                  "country": "IN",
                  "pricing_type": "FREE_TIER",
                  "pricing_category": "SERVICE",
                  "cost": 0
                },
                {
                  "start": **********,
                  "end": 1722969000,
                  "volume": 1,
                  "phone_number": "************",
                  "country": "IN",
                  "pricing_type": "FREE_TIER",
                  "pricing_category": "SERVICE",
                  "cost": 0
                },
                {
                  "start": **********,
                  "end": 1722969000,
                  "volume": 1,
                  "phone_number": "************",
                  "country": "IN",
                  "pricing_type": "REGULAR",
                  "pricing_category": "MARKETING",
                  "cost": 0.7265
                },
                {
                  "start": **********,
                  "end": **********,
                  "volume": 1,
                  "phone_number": "************",
                  "country": "IN",
                  "pricing_type": "FREE_TIER",
                  "pricing_category": "SERVICE",
                  "cost": 0
                }
              ]
            }
          ]
        },
        "id": "***************"
      }.to_json
    end
    let(:whatsapp_credit_history){
      create(
        :whatsapp_credit_history,
        tenant_id: whatsapp_credit.tenant_id,
        entry_type: CREDITS_PARKED,
        value: 10,
        balance: nil,
        start_time: start_time,
        end_time: end_time,
        connected_account_id: connected_account.id
      )
    }
    let(:whatsapp_credit_history_2){
      create(
        :whatsapp_credit_history,
        tenant_id: whatsapp_credit.tenant_id,
        entry_type: CREDITS_PARKED,
        value: 41.0,
        balance: nil,
        start_time: start_time,
        end_time: end_time,
        connected_account_id: second_connected_account.id
      )
    }

    def stub_fetch_conversation_analytics_request(status_code: , connected_account: , start_time: , end_time: , response_body: )
      stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.waba_id}?fields=pricing_analytics.start(#{start_time}).end(#{end_time}).granularity(DAILY).phone_numbers([\"#{connected_account.waba_number}\"]).dimensions(%5B%22PRICING_CATEGORY%22,%22PRICING_TYPE%22,%22COUNTRY%22,%22PHONE%22%5D)")
      .with(
        headers: {
          'Content-Type'=>'application/json',
          'X-Access-Token'=>'partner-token',
          'X-Waba-Id'=> connected_account.waba_id
        }
      ).to_return(status: status_code, body: response_body)
    end

    before do
      connected_account
    end

    context 'error' do
      context 'when whatsapp credits are not added for tenant' do
        it 'logs error message on rails logger' do
          second_connected_account
          expect(Rails.logger).to receive(:error).with("No whatsapp credits found for tenant id: #{connected_account.tenant_id}")

          described_class.new.update(user.tenant_id)
        end
      end

      context 'when whatsapp credits are added for tenant' do
        before(:each) do
          whatsapp_credit
        end

        context 'when facebook conversation analytics data for given connected account not fetched successfully' do
          before(:each) do
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
            stub_fetch_conversation_analytics_request(
              status_code: 400,
              connected_account: connected_account,
              start_time: start_time,
              end_time: end_time,
              response_body: file_fixture('interakt/conversation_analytics/invalid-token-response.json')
            )
          end

          it 'adds error log on rails logger' do
            expect(Rails.logger).to receive(:error).with("Error in Interakt Request - 400 - {\"error\"=>\"Invalid request\"}").exactly(4).times
            expect(Rails.logger).to receive(:error).with("Error while fetching conversation analytics for connected acccount id: #{connected_account.id}, tenant id: #{connected_account.tenant_id}")

            described_class.new.update(user.tenant_id)
          end
        end

        context 'when facebook conversation analytics data for given connected account fetched successfully' do
          context 'when facebook conversation analytics data is not present for given account' do
            before(:each) do
              allow(PublishEvent).to receive(:call).and_return(nil)
              stub_fetch_conversation_analytics_request(
                status_code: 200,
                connected_account: connected_account,
                start_time: start_time,
                end_time: end_time,
                response_body: { "id": "***************" }.to_json
              )
            end

            it 'logs error message on rails logger' do
              expect(Rails.logger).to receive(:info).with("Interakt response for request url https://amped-express.interakt.ai/api/v17.0/#{connected_account.waba_id}?fields=pricing_analytics.start(#{start_time}).end(#{end_time}).granularity(DAILY).phone_numbers([\"#{connected_account.waba_number}\"]).dimensions([\"PRICING_CATEGORY\",\"PRICING_TYPE\",\"COUNTRY\",\"PHONE\"]) body  {\"id\"=>\"***************\"}")

              expect(Rails.logger).to receive(:info).with("Conversation analytics not present for connected account id: #{connected_account.id}, tenant id: #{connected_account.tenant_id}")

              expect(Rails.logger).to receive(:info).with("Message Service: Tenant Usage publisher called")

              expect(Rails.logger).to receive(:info).with("Event::TenantUsagePublisher data {\"usageRecords\":[{\"tenantId\":#{user.tenant_id},\"count\":0,\"usageEntity\":\"MESSAGE\"},{\"tenantId\":#{user.tenant_id},\"count\":0,\"usageEntity\":\"STORAGE_MESSAGE_ATTACHMENT\"},{\"tenantId\":#{user.tenant_id},\"count\":0.0,\"usageEntity\":\"WHATSAPP_CREDITS\"}]}")

              described_class.new.update(user.tenant_id)

              expect(WhatsappCredit.last.reload.credits_revised_at).to eq(end_time)
              expect(connected_account.reload.credits_revised_upto).to eq(end_time)
            end
          end

          context 'when transaction fails' do
            before do
              stub_fetch_conversation_analytics_request(
                status_code: 200,
                connected_account: connected_account,
                start_time: start_time,
                end_time: end_time,
                response_body: file_fixture('interakt/conversation_analytics/api-success-response.json')
              )
              allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
              allow(WhatsappCreditHistory).to receive(:create!).and_raise(ActiveRecord::RecordInvalid)
              expect(Rails.logger).to receive(:error).with("WhatsappCreditsService Error while updating credits Tenant id #{connected_account.tenant_id}, message: Record invalid")
            end

            it 'should log error' do
              consumed = whatsapp_credit.consumed
              expect { described_class.new.update(user.tenant_id) }
                .not_to change(WhatsappCreditHistory, :count)

              expect(whatsapp_credit.reload.consumed).to eq(consumed)
            end
          end
        end
      end
    end

    context 'success' do
      before(:each) do
        stub_fetch_conversation_analytics_request(
          status_code: 200,
          connected_account: connected_account,
          start_time: start_time,
          end_time: end_time,
          response_body: file_fixture('interakt/conversation_analytics/api-success-response.json')
        )

        whatsapp_credit
      end

      context 'when connected account credits are revised for the given day' do
        before(:each) { connected_account.update(credits_revised_upto: end_time) }

        it 'should not update tenant whatsapp credits' do
          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(0)
        end
      end

      context 'when connected account credits are not revised for the given day' do
        before(:each) do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
        end

        it 'updates tenant whatsapp credits and add whatsapp credit history logs' do
          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(1)
          expect(WhatsappCredit.last.consumed).to eq(2.75)
          expect(WhatsappCreditHistory.last.balance).to eq(997.25)
          expect(connected_account.reload.credits_revised_upto).to eq(end_time)
        end

        it 'should add whatsapp credit history logs by sorting data points with start time' do
          stub_fetch_conversation_analytics_request(
            status_code: 200,
            connected_account: connected_account,
            start_time: start_time,
            end_time: end_time,
            response_body: conversation_analytics_success_response
          )

          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(5)
          expect(WhatsappCredit.last.consumed).to eq(0.8)
          expect(WhatsappCreditHistory.first.start_time).to eq(**********)
          expect(WhatsappCreditHistory.last.start_time).to eq(**********)
          expect(WhatsappCreditHistory.last.balance).to eq(999.2)
          expect(connected_account.reload.credits_revised_upto).to eq(end_time)
        end

        it 'should unpark credits' do
          whatsapp_credit_history
          whatsapp_credit.update(parked: 14.0)
          stub_fetch_conversation_analytics_request(
            status_code: 200,
            connected_account: connected_account,
            start_time: start_time,
            end_time: end_time,
            response_body: conversation_analytics_success_response
          )

          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(6)
          expect(WhatsappCredit.last.consumed).to eq(0.8)
          expect(WhatsappCredit.last.parked).to eq(4.0)

          expect(WhatsappCreditHistory.second.connected_account_id).to eq(connected_account.id)
          expect(WhatsappCreditHistory.second.value).to eq(10.0)
          expect(WhatsappCreditHistory.second.balance).to eq(nil)
          expect(WhatsappCreditHistory.second.entry_type).to eq(CREDITS_UNPARKED)

          expect(WhatsappCreditHistory.third.start_time).to eq(**********)
          expect(WhatsappCreditHistory.last.start_time).to eq(**********)
          expect(WhatsappCreditHistory.last.balance).to eq(999.2)
          expect(connected_account.reload.credits_revised_upto).to eq(end_time)
        end
      end

      context 'when connected account is not active' do
        context 'when connected account deactivated_at value is less than credits_revised_upto' do
          before(:each) do
            connected_account.update(status: INACTIVE, deactivated_at: 2.day.ago.beginning_of_day)
          end

          it 'should not update tenant whatsapp credits' do
            expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(0)
            expect(WhatsappCredit.last.credits_revised_at).to eq(start_time)
            expect(connected_account.reload.credits_revised_upto).to eq(start_time)
          end
        end

        context 'when connected account deactivated_at value is greater than credits_revised_upto' do
          before(:each) do
            connected_account.update(status: INACTIVE, deactivated_at: DateTime.now.to_i)
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end

          it 'updates tenant whatsapp credits and publish tenant usage' do
            expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(1)
            expect(WhatsappCredit.last.consumed).to eq(2.75)
            expect(WhatsappCreditHistory.last.balance).to eq(997.25)
            expect(connected_account.reload.credits_revised_upto).to eq(end_time)
          end
        end
      end

      context 'when two active connected accounts are present' do
        context 'when credits are not updated successfully for second connected account' do
          before(:each) do
            start_time = 2.days.ago.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
            second_connected_account.update(credits_revised_upto: start_time)
            whatsapp_credit.update(credits_revised_at: start_time, parked: 100)

            stub_fetch_conversation_analytics_request(
              status_code: 200,
              connected_account: second_connected_account,
              start_time: start_time,
              end_time: end_time,
              response_body: file_fixture('interakt/conversation_analytics/api-success-response.json')
            )

            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end
          let(:whatsapp_credit_history_3){
            create(
              :whatsapp_credit_history,
              tenant_id: whatsapp_credit.tenant_id,
              entry_type: CREDITS_PARKED,
              value: 33.5,
              balance: nil,
              start_time: 2.days.ago.in_time_zone('Asia/Calcutta').beginning_of_day.to_i,
              end_time: start_time,
              connected_account_id: second_connected_account.id
            )
          }

          it 'updates tenant whatsapp credits by reprocessing second connected account from previous day' do
            expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(2)
            expect(connected_account.reload.credits_revised_upto).to eq(end_time)
            expect(second_connected_account.reload.credits_revised_upto).to eq(end_time)
          end

          it 'unparks credits for both days' do
            whatsapp_credit_history_3
            whatsapp_credit_history
            whatsapp_credit_history_2

            expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(5)
            expect(connected_account.reload.credits_revised_upto).to eq(end_time)
            expect(second_connected_account.reload.credits_revised_upto).to eq(end_time)

            day_before_yesterday = 2.days.ago.in_time_zone('Asia/Calcutta').beginning_of_day.to_i
            expect(WhatsappCreditHistory.all.pluck(:connected_account_id, :entry_type, :value, :start_time, :end_time).map(&:as_json)).to match_array([
              [second_connected_account.id, CREDITS_PARKED, 33.5, day_before_yesterday, start_time],
              [connected_account.id, CREDITS_PARKED, 10.0, start_time, end_time],
              [second_connected_account.id, CREDITS_PARKED, 41.0, start_time, end_time],
              [connected_account.id, CREDITS_UNPARKED, 10.0, start_time, end_time],
              [connected_account.id, CREDITS_DEDUCTED, 2.75, **********, **********],
              [second_connected_account.id, CREDITS_UNPARKED, 33.5, day_before_yesterday, start_time],
              [second_connected_account.id, CREDITS_UNPARKED, 41.0, start_time, end_time],
              [second_connected_account.id, CREDITS_DEDUCTED, 2.75, **********, **********]
            ])

            expect(WhatsappCredit.last.consumed).to eq(5.5)
            expect(WhatsappCredit.last.parked).to eq(15.5)
          end
        end
      end

      context 'when no consumption data points are present for given day' do
        before do
          stub_fetch_conversation_analytics_request(
            status_code: 200,
            connected_account: connected_account,
            start_time: start_time,
            end_time: end_time,
            response_body: {
                  "conversation_analytics": { "data": [] },
                  "id": "***************"
            }.to_json
          )
          whatsapp_credit.update(parked: 100)
          whatsapp_credit_history
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
        end

        it 'should unpark whatsapp credits' do
          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(1)

          whatsapp_credit_history_log = WhatsappCreditHistory.last

          expect(whatsapp_credit.reload.parked).to eq(90.0)

          expect(whatsapp_credit_history_log.connected_account_id).to eq(connected_account.id)
          expect(whatsapp_credit_history_log.value).to eq(10.0)
          expect(whatsapp_credit_history_log.balance).to eq(nil)
          expect(whatsapp_credit_history_log.entry_type).to eq(CREDITS_UNPARKED)
        end
      end
    end

    context 'success after retry' do
      before(:each) do
        stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.waba_id}?fields=pricing_analytics.start(#{start_time}).end(#{end_time}).granularity(DAILY).phone_numbers([\"#{connected_account.waba_number}\"]).dimensions(%5B%22PRICING_CATEGORY%22,%22PRICING_TYPE%22,%22COUNTRY%22,%22PHONE%22%5D)")
        .with(
          headers: {
            'Content-Type'=>'application/json',
            'X-Access-Token'=>'partner-token',
            'X-Waba-Id'=> connected_account.waba_id
          }
        ).
        to_return(status: 500).then.
        to_return(status: 500).then.
        to_return(status: 500).then.
        to_return(status: 200, body: file_fixture('interakt/conversation_analytics/api-success-response.json'))

        whatsapp_credit
      end

      context 'when analytics api fails 3 times and succeeds 4th time and connected account credits are not revised for the given day' do
        before(:each) do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
        end

        it 'updates tenant whatsapp credits and add whatsapp credit history logs' do
          expect{ described_class.new.update(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(1)
          expect(WhatsappCredit.last.consumed).to eq(2.75)
          expect(WhatsappCreditHistory.last.balance).to eq(997.25)
          expect(connected_account.reload.credits_revised_upto).to eq(end_time)
        end
      end
    end
  end

  describe "#get_summary" do
    let (:whatsapp_credit) { create(:whatsapp_credit) }
    let(:valid_auth_token) { build(:auth_token, :with_whatsapp_update_all_read_all_permission, tenant_id: whatsapp_credit.tenant_id) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    let(:token_without_whatsapp_business_permission) { build(:auth_token) }
    let(:auth_data_without_whatsapp_business_permission) { User::TokenParser.parse(token_without_whatsapp_business_permission.token) }

    context 'when credit info is present' do
      before { Thread.current[:auth] = auth_data }

      it 'should return whatsapp_credit info for current tenant' do
        summary_response = described_class.new.get_summary
        expect(summary_response.tenant_id).to eq(whatsapp_credit.tenant_id)
      end
    end

    context 'when credit info is NOT present' do
      let(:valid_auth_token_for_different_tenant) { build(:auth_token, :with_whatsapp_update_all_read_all_permission) }
      let(:auth_data_for_different_tenant)        { User::TokenParser.parse(valid_auth_token_for_different_tenant.token) }
      before { Thread.current[:auth] = auth_data_for_different_tenant }

      it 'returns dummy credits response' do
        summary_response = described_class.new.get_summary
        expect(summary_response.total).to eq(0)
        expect(summary_response.consumed).to eq(0)
      end
    end

    context 'when user does not have read_all on whatsappBusiness' do
      before { Thread.current[:auth] = auth_data_without_whatsapp_business_permission }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read whatsapp business details")
        expect{ described_class.new.get_summary }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe "#get_history" do
    let(:whatsapp_credit_history) { create(:whatsapp_credit_history) }
    let!(:deducted_whatsapp_credit_history) { create(:whatsapp_credit_history, tenant_id: whatsapp_credit_history.tenant_id, entry_type: CREDITS_DEDUCTED, conversation_category: 'AUTH') }
    let!(:deducted_whatsapp_credit_history_1) { create(:whatsapp_credit_history, tenant_id: whatsapp_credit_history.tenant_id, entry_type: CREDITS_DEDUCTED, conversation_category: 'AUTH', start_time: Time.now - 1.day) }
    let!(:deducted_whatsapp_credit_history_2) { create(:whatsapp_credit_history, tenant_id: whatsapp_credit_history.tenant_id, entry_type: CREDITS_DEDUCTED, conversation_category: CHATBOT, start_time: Time.now - 1.day) }
    let!(:another_whatsapp_credit_history) { create(:whatsapp_credit_history) }
    let(:valid_auth_token) { build(:auth_token, :with_whatsapp_update_all_read_all_permission, tenant_id: whatsapp_credit_history.tenant_id) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    let(:token_without_whatsapp_business_permission) { build(:auth_token) }
    let(:auth_data_without_whatsapp_business_permission) { User::TokenParser.parse(token_without_whatsapp_business_permission.token) }

    context 'when whatsapp credit history is present' do
      before { Thread.current[:auth] = auth_data }

      it 'should return whatsapp_credit history for current tenant' do
        history_response = described_class.new.get_history({})
        expect(history_response).to eq([
          [deducted_whatsapp_credit_history_2, deducted_whatsapp_credit_history_1, deducted_whatsapp_credit_history, whatsapp_credit_history],
          { 
            'AUTH' => deducted_whatsapp_credit_history.value + deducted_whatsapp_credit_history_1.value,
            'CHATBOT' => deducted_whatsapp_credit_history_2.value
          }
        ])
      end
    end

    context 'when user does not have read_all on whatsappBusiness' do
      before { Thread.current[:auth] = auth_data_without_whatsapp_business_permission }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read whatsapp business details")
        expect{ described_class.new.get_history({}) }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe "#park_credits" do
    let(:whatsapp_credit){ create(:whatsapp_credit, total: 1000, consumed: 10) }
    let(:connected_account){ create(:connected_account, tenant_id: whatsapp_credit.tenant_id) }

    it 'should park credits for given tenant_id' do
      expect{
        described_class.new.park_credits(whatsapp_credit.tenant_id, '91', 'Utility', connected_account)
      }.to change(WhatsappCreditHistory, :count).by(1)

      whatsapp_credit_history_log = WhatsappCreditHistory.last

      expect(whatsapp_credit.reload.parked).to eq(0.13)
      expect(whatsapp_credit_history_log.connected_account_id).to eq(connected_account.id)
      expect(whatsapp_credit_history_log.value).to eq(0.13)
      expect(whatsapp_credit_history_log.balance).to eq(nil)
    end

    it 'should park credits for given tenant_id when recipient countries pricing falls in others category' do
      expect{
        described_class.new.park_credits(whatsapp_credit.tenant_id, '911', 'Utility', connected_account)
      }.to change(WhatsappCreditHistory, :count).by(1)

      whatsapp_credit_history_log = WhatsappCreditHistory.last

      expect(whatsapp_credit.reload.parked).to eq(0.62)
      expect(whatsapp_credit_history_log.connected_account_id).to eq(connected_account.id)
      expect(whatsapp_credit_history_log.value).to eq(0.62)
      expect(whatsapp_credit_history_log.balance).to eq(nil)
    end
  end

  describe "#unpark_credits" do
    let(:whatsapp_credit){ create(:whatsapp_credit, total: 1000, consumed: 10, parked: 100) }
    let(:connected_account){ create(:connected_account, tenant_id: whatsapp_credit.tenant_id) }
    let(:whatsapp_credit_history){
      create(
        :whatsapp_credit_history,
        tenant_id: whatsapp_credit.tenant_id,
        entry_type: CREDITS_PARKED,
        value: 10,
        balance: nil,
        start_time: **********,
        end_time: **********,
        connected_account_id: connected_account.id
      )
    }
    let(:whatsapp_credit_history_2){
      create(
        :whatsapp_credit_history,
        tenant_id: whatsapp_credit.tenant_id,
        entry_type: CREDITS_PARKED,
        value: 41.0,
        balance: nil,
        start_time: **********,
        end_time: **********,
        connected_account_id: connected_account.id
      )
    }

    it 'should unpark credits for given connected account' do
      whatsapp_credit_history

      expect{
        described_class.new.unpark_credits(connected_account, **********, **********)
      }.to change(WhatsappCreditHistory, :count).by(1)

      whatsapp_credit_history_log = WhatsappCreditHistory.last

      expect(whatsapp_credit.reload.parked).to eq(90.0)
      expect(whatsapp_credit_history_log.connected_account_id).to eq(connected_account.id)
      expect(whatsapp_credit_history_log.value).to eq(10.0)
      expect(whatsapp_credit_history_log.balance).to eq(nil)
      expect(whatsapp_credit_history_log.entry_type).to eq(CREDITS_UNPARKED)

    end

    it 'should unpark credits if start_time and end_time have difference for more than 1 day' do
      whatsapp_credit_history
      whatsapp_credit_history_2

      expect{
        described_class.new.unpark_credits(connected_account, **********, **********)
      }.to change(WhatsappCreditHistory, :count).by(2)

      whatsapp_credit_history_log = WhatsappCreditHistory.third
      whatsapp_credit_history_log_2 = WhatsappCreditHistory.fourth

      expect(whatsapp_credit.reload.parked).to eq(49.0)

      expect(whatsapp_credit_history_log.connected_account_id).to eq(connected_account.id)
      expect(whatsapp_credit_history_log.value).to eq(10.0)
      expect(whatsapp_credit_history_log.balance).to eq(nil)
      expect(whatsapp_credit_history_log.entry_type).to eq(CREDITS_UNPARKED)

      expect(whatsapp_credit_history_log_2.connected_account_id).to eq(connected_account.id)
      expect(whatsapp_credit_history_log_2.value).to eq(41.0)
      expect(whatsapp_credit_history_log_2.balance).to eq(nil)
      expect(whatsapp_credit_history_log_2.entry_type).to eq(CREDITS_UNPARKED)
    end

    it 'should not unpark credits if no credits were parked for that duration' do
      expect{
        described_class.new.unpark_credits(connected_account, **********, **********)
      }.to change(WhatsappCreditHistory, :count).by(0)

      expect(whatsapp_credit.reload.parked).to eq(100.0)
    end
  end

  describe "#get_status" do
    let (:whatsapp_credit) { create(:whatsapp_credit) }
    let(:valid_auth_token) { build(:auth_token, :with_whatsapp_update_all_read_all_permission, tenant_id: whatsapp_credit.tenant_id) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }

    context 'when minimum credits for bulk actions are present' do
      before { Thread.current[:auth] = auth_data }

      it 'should return true' do
        credits_status_response = described_class.new.get_status
        expect(credits_status_response).to eq(true)
      end
    end

    context 'when credit info is NOT present' do
      let(:valid_auth_token_for_different_tenant) { build(:auth_token, :with_whatsapp_update_all_read_all_permission) }
      let(:auth_data_for_different_tenant)        { User::TokenParser.parse(valid_auth_token_for_different_tenant.token) }
      before { Thread.current[:auth] = auth_data_for_different_tenant }

      it 'returns dummy credits response' do
        credits_status_response = described_class.new.get_status
        expect(credits_status_response).to eq(false)
      end
    end
  end

  describe "#deduct_chatbot_credits" do
    let(:whatsapp_credit) { create(:whatsapp_credit, total: 1000, consumed: 100, parked: 0) }
    let(:connected_account) { create(:connected_account, tenant_id: whatsapp_credit.tenant_id, waba_number: '+**********') }
    let(:charge) { 5.0 }

    before do
      allow(Date).to receive(:today).and_return(Date.new(2024, 1, 15))
      allow(Rails.logger).to receive(:info)
    end

    context 'when whatsapp credit exists' do
      it 'deducts credits and updates consumed amount' do
        expect {
          described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, charge)
        }.to change { whatsapp_credit.reload.consumed }.from(100.0).to(105.0)
      end

      it 'creates or updates daily chatbot credit history' do
        expect {
          described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, charge)
        }.to change(WhatsappCreditHistory, :count).by(1)

        history = WhatsappCreditHistory.last
        expect(history.entry_type).to eq(CREDITS_DEDUCTED)
        expect(history.conversation_category).to eq(CHATBOT)
        expect(history.value).to eq(5.0)
        expect(history.tenant_id).to eq(whatsapp_credit.tenant_id)
        expect(history.connected_account_id).to eq(connected_account.id)
        expect(history.balance).to eq(895.0) # 1000 - 105
      end

      it 'updates existing daily log when called multiple times on same day' do
        described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, 3.0)

        expect {
          described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, 2.0)
        }.to change(WhatsappCreditHistory, :count).by(0)

        history = WhatsappCreditHistory.last
        expect(history.value).to eq(5.0) # 3.0 + 2.0
        expect(whatsapp_credit.reload.consumed).to eq(105.0) # 100 + 3 + 2
      end

      it 'creates separate logs for different days' do
        allow(Date).to receive(:today).and_return(Date.new(2024, 1, 15))
        described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, 3.0)

        allow(Date).to receive(:today).and_return(Date.new(2024, 1, 16))

        expect {
          described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, 2.0)
        }.to change(WhatsappCreditHistory, :count).by(1)

        histories = WhatsappCreditHistory.where(tenant_id: whatsapp_credit.tenant_id, conversation_category: CHATBOT).order(:created_at)
        expect(histories.count).to eq(2)
        expect(histories.first.value).to eq(3.0)
        expect(histories.last.value).to eq(2.0)
      end

      it 'logs successful credit deduction' do
        expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: #{charge}/)
        expect(Rails.logger).to receive(:info).with(/Updated chatbot credit history/)

        described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, charge)
      end
    end

    context 'when whatsapp credit does not exist' do
      it 'logs error and returns early' do
        expect(Rails.logger).to receive(:error).with(/No whatsapp credits found/)

        described_class.new.deduct_chatbot_credits(99999, connected_account, charge)
      end

      it 'does not create credit history' do
        expect {
          described_class.new.deduct_chatbot_credits(99999, connected_account, charge)
        }.not_to change(WhatsappCreditHistory, :count)
      end
    end

    context 'when an error occurs' do
      before do
        allow(WhatsappCredit).to receive(:transaction).and_raise(StandardError.new('Database error'))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Error while deducting chatbot credits/)

        described_class.new.deduct_chatbot_credits(whatsapp_credit.tenant_id, connected_account, charge)
      end
    end
  end

  describe "#create_or_update_chatbot_credit_history" do
    let(:whatsapp_credit) { create(:whatsapp_credit, total: 1000, consumed: 100) }
    let(:connected_account) { create(:connected_account, tenant_id: whatsapp_credit.tenant_id, waba_number: '+**********') }
    let(:service) { described_class.new }
    let(:charge) { 5.0 }
    let(:balance) { 895.0 }

    before do
      allow(Date).to receive(:today).and_return(Date.new(2024, 1, 15))
      allow(Rails.logger).to receive(:info)
    end

    context 'when no existing history for today' do
      it 'creates new daily credit history log' do
        expect {
          service.send(:create_or_update_chatbot_credit_history, whatsapp_credit.tenant_id, connected_account, charge, balance)
        }.to change(WhatsappCreditHistory, :count).by(1)

        history = WhatsappCreditHistory.last
        expect(history.entry_type).to eq(CREDITS_DEDUCTED)
        expect(history.conversation_category).to eq(CHATBOT)
        expect(history.value).to eq(charge)
        expect(history.balance).to eq(balance)
        expect(history.phone_number).to eq('**********')
        expect(history.tenant_id).to eq(whatsapp_credit.tenant_id)
        expect(history.connected_account_id).to eq(connected_account.id)
      end
    end

    context 'when existing history for today exists' do
      let!(:existing_history) do
        start_time = Date.new(2024, 1, 15).in_time_zone('Asia/Calcutta').beginning_of_day.to_i
        end_time = Date.new(2024, 1, 15).in_time_zone('Asia/Calcutta').end_of_day.to_i + 1

        create(:whatsapp_credit_history,
          tenant_id: whatsapp_credit.tenant_id,
          connected_account_id: connected_account.id,
          entry_type: CREDITS_DEDUCTED,
          conversation_category: CHATBOT,
          start_time: start_time,
          end_time: end_time,
          value: 3.0,
          balance: 897.0
        )
      end

      it 'updates existing daily log' do
        expect {
          service.send(:create_or_update_chatbot_credit_history, whatsapp_credit.tenant_id, connected_account, charge, balance)
        }.not_to change(WhatsappCreditHistory, :count)

        existing_history.reload
        expect(existing_history.value).to eq(8.0) # 3.0 + 5.0
        expect(existing_history.balance).to eq(balance)
      end
    end
  end
end
