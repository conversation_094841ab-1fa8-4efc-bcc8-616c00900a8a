# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FieldMappingsService do
  let(:user)               { create(:user) }
  let(:valid_auth_token)   { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)          { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data)  { User::TokenParser.parse(invalid_auth_token.token) }

  describe '#get' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

        before do
          Thread.current[:user] = user
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token.token
        end

        context 'when connected account is present' do
          context 'when entity type is valid' do
            context 'when field mapping is present' do
              let!(:field_mapping) { create(:field_mapping, entity_type: entity_type, connected_account: connected_account) }

              it 'returns field mapping for entity' do
                expect(described_class.new({ connected_account_id: connected_account.id, entity_type: entity_type }).get).to eq(field_mapping)
              end
            end

            context 'when field mapping is absent' do
              it 'returns nil' do
                expect(described_class.new({ connected_account_id: connected_account.id, entity_type: entity_type }).get).to be_nil
              end
            end
          end
        end

        context 'when connected account is not found' do
          before { connected_account.update(tenant_id: user.tenant_id + 1) }

          it 'raises not found error' do
            expect{ described_class.new({ connected_account_id: connected_account.id, entity_type: entity_type }).get }
              .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
          end
        end

        context 'when user does not have permission to read connected account' do
          before do
            Thread.current[:user] = user
            Thread.current[:auth] = invalid_auth_data
            Thread.current[:token] = invalid_auth_token.token
          end

          it 'raises authentication error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to view connected account field mappings")
            expect{ described_class.new({ connected_account_id: connected_account.id, entity_type: entity_type }).get }
              .to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
          end
        end
      end
    end

    context "when invalid entity type" do
      let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      before do
        Thread.current[:user] = user
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
      end

      it 'raises invalid data error' do
        expect{ described_class.new({ connected_account_id: connected_account.id, entity_type: LOOKUP_DEAL }).get }
              .to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid entity type')
      end
    end
  end

  describe '#create_or_update' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        let(:campaign_id) { entity_type == LOOKUP_LEAD ? 40366 : 436488 }
        let(:source_id) { entity_type == LOOKUP_LEAD ? 40368 : 439621 }
        let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
        let(:params) do
          {
            connected_account_id: connected_account.id,
            entity_type: entity_type,
            campaign: campaign_id,
            source: source_id,
            sub_source: 'New Sub Source',
            utm_campaign: 'New UTM Campaign',
            utm_content: 'New UTM Content',
            utm_medium: 'New UTM Medium',
            utm_source: 'New UTM Source',
            utm_term: 'New UTM Term'
          }
        end

        before do
          Thread.current[:user] = user
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token.token
        end

        context 'when connected account is present' do
          context 'when entity type is valid' do
            before do
              stub_request(:get, "http://localhost:8086/v1/entities/#{entity_type}/fields?entityType=#{entity_type}&custom-only=false")
                .with(
                  headers: {
                    Authorization: "Bearer #{valid_auth_token.token}"
                  }
                )
                .to_return(status: 200, body: file_fixture("fields/#{entity_type}-fields-response.json").read)
            end

            context 'when field mapping is present' do
              let!(:field_mapping) { create(:field_mapping, entity_type: entity_type, connected_account: connected_account) }

              it 'updates and returns field mapping for entity' do
                expect {
                  described_class.new(params).create_or_update
                }.to change(FieldMapping, :count).by(0)

                expect(field_mapping.reload.campaign).to eq(campaign_id)
                expect(field_mapping.source).to eq(source_id)
                expect(field_mapping.sub_source).to eq('New Sub Source')
                expect(field_mapping.utm_campaign).to eq('New UTM Campaign')
                expect(field_mapping.utm_content).to eq('New UTM Content')
                expect(field_mapping.utm_medium).to eq('New UTM Medium')
                expect(field_mapping.utm_source).to eq('New UTM Source')
                expect(field_mapping.utm_term).to eq('New UTM Term')

                expect(connected_account.reload.updated_by).to eq(user)
              end
            end

            context 'when field mapping is absent' do
              it 'creates and returns field mapping for entity' do
                expect {
                  described_class.new(params).create_or_update
                }.to change(FieldMapping, :count).by(1)

                field_mapping = FieldMapping.last
                expect(field_mapping.connected_account_id).to eq(connected_account.id)
                expect(field_mapping.entity_type).to eq(entity_type)
                expect(field_mapping.campaign).to eq(campaign_id)
                expect(field_mapping.source).to eq(source_id)
                expect(field_mapping.sub_source).to eq('New Sub Source')
                expect(field_mapping.utm_campaign).to eq('New UTM Campaign')
                expect(field_mapping.utm_content).to eq('New UTM Content')
                expect(field_mapping.utm_medium).to eq('New UTM Medium')
                expect(field_mapping.utm_source).to eq('New UTM Source')
                expect(field_mapping.utm_term).to eq('New UTM Term')

                expect(connected_account.reload.updated_by).to eq(user)
              end
            end
          end
        end

        context 'when campaign id or source id is invalid or inactive' do
          before do
            stub_request(:get, "http://localhost:8086/v1/entities/#{entity_type}/fields?entityType=#{entity_type}&custom-only=false")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: file_fixture("fields/#{entity_type}-fields-response.json").read)
          end

          context 'when campaign id is invalid' do
            let(:campaign_id) { 123 }

            it 'raises invalid data error' do
              expect {
                described_class.new(params).create_or_update
              }.to raise_error(ExceptionHandler::InvalidDataError, '022019||Invalid or inactive campaign or source field')
            end
          end

          context 'when campaign id inactive' do
            let(:campaign_id) { entity_type == LOOKUP_LEAD ? 281857 : 436489 }

            it 'raises invalid data error' do
              expect {
                described_class.new(params).create_or_update
              }.to raise_error(ExceptionHandler::InvalidDataError, '022019||Invalid or inactive campaign or source field')
            end
          end

          context 'when source id is invalid' do
            let(:source_id) { 123 }

            it 'raises invalid data error' do
              expect {
                described_class.new(params).create_or_update
              }.to raise_error(ExceptionHandler::InvalidDataError, '022019||Invalid or inactive campaign or source field')
            end
          end

          context 'when source id is inactive' do
            let(:source_id) { entity_type == LOOKUP_LEAD ? 40367 : 441729 }

            it 'raises invalid data error' do
              expect {
                described_class.new(params).create_or_update
              }.to raise_error(ExceptionHandler::InvalidDataError, '022019||Invalid or inactive campaign or source field')
            end
          end
        end

        context 'when connected account is not found' do
          before { connected_account.update(tenant_id: user.tenant_id + 1) }

          it 'raises not found error' do
            expect{ described_class.new(params).create_or_update }
              .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
          end
        end

        context 'when error occurs while saving field mapping' do
          before do
            stub_request(:get, "http://localhost:8086/v1/entities/#{entity_type}/fields?entityType=#{entity_type}&custom-only=false")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: file_fixture("fields/#{entity_type}-fields-response.json").read)
            expect_any_instance_of(FieldMapping).to receive(:save!).and_raise(ActiveRecord::ConnectionTimeoutError)
          end

          it 'raises invalid data error' do
            expect(Rails.logger).to receive(:error).with("FieldMappingsService Error while saving field mapping ActiveRecord::ConnectionTimeoutError")
            expect{ described_class.new(params).create_or_update }
              .to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid fields')
          end
        end

        context 'when user does not have permission to update connected account' do
          before do
            Thread.current[:user] = user
            Thread.current[:auth] = invalid_auth_data
            Thread.current[:token] = invalid_auth_token.token
          end

          it 'raises authentication error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to update connected account field mappings")
            expect{ described_class.new(params).create_or_update }
              .to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
          end
        end
      end
    end

    context "when invalid entity type" do
      let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      before do
        Thread.current[:user] = user
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
      end

      it 'raises invalid data error' do
        expect{ described_class.new({ connected_account_id: connected_account.id, entity_type: LOOKUP_DEAL }).create_or_update }
              .to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid entity type')
      end
    end
  end

  describe '#delete_all' do
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let!(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

    context 'when field mappings exist for the connected account' do
      let!(:field_mapping_1) { create(:field_mapping, connected_account: connected_account, entity_type: LOOKUP_LEAD) }
      let!(:field_mapping_2) { create(:field_mapping, connected_account: connected_account, entity_type: LOOKUP_CONTACT) }
      let!(:other_field_mapping) { create(:field_mapping, connected_account: other_connected_account, entity_type: LOOKUP_LEAD) }

      it 'deletes all field mappings for the specified connected account' do
        expect {
          described_class.new({ id: connected_account.id }).delete_all
        }.to change(FieldMapping, :count).by(-2)

        expect(FieldMapping.where(connected_account_id: connected_account.id)).to be_empty
        expect(FieldMapping.where(connected_account_id: other_connected_account.id)).to contain_exactly(other_field_mapping)
      end

      it 'returns the destroyed field mappings' do
        result = described_class.new({ id: connected_account.id }).delete_all
        expect(result).to be_an(Array)
        expect(result.length).to eq(2)
      end
    end

    context 'when no field mappings exist for the connected account' do
      it 'does not delete any field mappings' do
        expect {
          described_class.new({ id: connected_account.id }).delete_all
        }.not_to change(FieldMapping, :count)
      end

      it 'returns empty array' do
        result = described_class.new({ id: connected_account.id }).delete_all
        expect(result).to eq([])
      end
    end

    context 'when connected account id does not exist' do
      it 'does not delete any field mappings' do
        expect {
          described_class.new({ id: -1 }).delete_all
        }.not_to change(FieldMapping, :count)
      end

      it 'returns empty array' do
        result = described_class.new({ id: -1 }).delete_all
        expect(result).to eq([])
      end
    end
  end
end
