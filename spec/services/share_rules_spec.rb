# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRules, type: :service do
  describe '#manage_share_rules' do
    let(:payload) { JSON.parse(file_fixture('listeners/share-rule-created-v2.json').read) }
    let(:token) { FactoryBot.build(:auth_token).token }

    context 'when valid input' do
      before(:each) do
        allow(GenerateToken).to receive(:call).exactly(:twice).and_return(token)
        stub_request(:get, "http://localhost:8081/v1/users/6932").
          with(
            headers: {
            'Authorization'=>"Bearer #{token}"
            }).
          to_return(status: 200, body: {"id": 6932, "firstName": "Jane", "lastName": "Doe", "email": {"primary": true,"value": "<EMAIL>"}, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"7311111111","dialCode":"+91","primary":true}]}.to_json, headers: {})

        stub_request(:get, "http://localhost:8081/v1/users/3779").
          with(
            headers: {
            'Authorization'=>"Bearer #{token}"
            }).
          to_return(status: 200, body: {"id": 3779, "firstName": "Jane", "lastName": "Doe", "email": {"primary": true,"value": "<EMAIL>"}, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"7311111111","dialCode":"+91","primary":true}]}.to_json, headers: {})

      end

      it 'creates share rule' do
        expect { described_class.new(payload).manage_share_rules }.to change(ShareRule, :count).by(1)
        expect(ShareRule.last.share_rule_id).to eq(3654)
      end
    end
  end

  describe '#delete' do
    let(:payload) { JSON.parse(file_fixture('listeners/share-rule-deleted-v2.json').read) }
    let(:share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: 754, from_id: 4010, to_id: 6932, from_type: 'USER', to_type: 'USER') }

    context 'when valid input' do
      context 'when share rule with given id is not found' do
        it 'should not delete share rule' do
          expect { described_class.new(payload).delete }.to change(ShareRule, :count).by(0)
        end
      end

      context 'when share rule with given id is found' do
        before(:each) { share_rule }

        it 'deletes the share rule with given id' do
          expect { described_class.new(payload).delete }.to change(ShareRule, :count).by(-1)
        end
      end
    end
  end
end
