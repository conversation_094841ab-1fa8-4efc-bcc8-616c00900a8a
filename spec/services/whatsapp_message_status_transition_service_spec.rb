# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappMessageStatusTransitionService do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
  let(:conversation) { create(:conversation, tenant_id: user.tenant_id, owner_id: user.id) }
  let(:message) do
    create(:message, 
           tenant_id: user.tenant_id, 
           owner: user, 
           conversation_id: conversation.id, 
           connected_account_id: connected_account.id,
           status: 'sending', remote_id: 'wamid.********************************************************')
  end
  let(:timestamp) { Time.current }
  let(:old_serialized_message) { MessageSerializer::Details.serialize_whatsapp_message(message) }

  def build_payload(status, timestamp = Time.current, error_data = nil)
    payload = {
      value: {
        statuses: [
          {
            id: message.remote_id,
            status: status,
            timestamp: timestamp.to_i.to_s
          }
        ]
      }
    }

    if error_data
      payload[:value][:statuses][0][:errors] = [error_data]
    end

    payload
  end

  before do
    allow(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call)
    allow(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call)
    allow(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call)
  end

  describe '#call' do
    context 'when transitioning from sending to sent' do
      it 'updates message status without publishing intermediate events' do
        payload = build_payload('sent', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('sent')
        expect(message.sent_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).once
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).once
      end
    end

    context 'when transitioning from sending to delivered' do
      it 'publishes sent event first, then delivered event' do
        payload = build_payload('delivered', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('delivered')
        expect(message.delivered_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).twice
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).twice
      end
    end

    context 'when transitioning from sending to read' do
      it 'publishes sent and delivered events first, then read event' do
        payload = build_payload('read', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('read')
        expect(message.read_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).exactly(3).times
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).exactly(3).times
      end
    end

    context 'when transitioning from sent to delivered' do
      before { message.update(status: 'sent', sent_at: 1.minute.ago) }

      it 'updates message status without publishing intermediate events' do
        payload = build_payload('delivered', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('delivered')
        expect(message.delivered_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).once
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).once
      end
    end

    context 'when transitioning from sent to read' do
      before { message.update(status: 'sent', sent_at: 1.minute.ago) }

      it 'publishes delivered event first, then read event' do
        payload = build_payload('read', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('read')
        expect(message.read_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).twice
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).twice
      end
    end

    context 'when transitioning from delivered to read' do
      before { message.update(status: 'delivered', sent_at: 2.minutes.ago, delivered_at: 1.minute.ago) }

      it 'updates message status without publishing intermediate events' do
        payload = build_payload('read', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('read')
        expect(message.read_at).to be_within(1.second).of(timestamp)

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).once
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).once
      end
    end

    context 'when transitioning to failed status' do
      let(:error_data) { { code: 'ERROR_CODE', error_data: { details: 'Message failed to send' } } }

      it 'updates message status and publishes failed event' do
        payload = build_payload('failed', timestamp, error_data)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('failed')
        expect(message.failed_at).to be_within(1.second).of(timestamp)
        expect(message.status_message).to eq('ERROR_CODE - Message failed to send')

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to have_received(:call).once
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to have_received(:call).once
      end
    end

    context 'when message has campaign_info' do
      before do
        message.update(campaign_info: { campaignId: 123, activityId: 234 })
      end

      it 'also publishes WhatsappEntityMessageStatusTrackPublisher events' do
        payload = build_payload('delivered', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to have_received(:call).twice
      end
    end

    context 'when invalid status transition' do
      before { message.update(status: 'delivered') }

      it 'does not update message or publish events for invalid backward transition' do
        payload = build_payload('sent', timestamp)
        service = described_class.new(message, payload)
        service.call

        expect(message.reload.status).to eq('delivered')

        expect(Publishers::WhatsappMessageStatusUpdatedPublisher).not_to have_received(:call)
        expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).not_to have_received(:call)
      end
    end
  end

  describe '#determine_statuses_to_publish' do
    let(:service) { described_class.new(message, build_payload('delivered', timestamp)) }

    it 'returns correct statuses to publish for sending to delivered transition' do
      statuses_to_publish = service.send(:determine_statuses_to_publish)
      expect(statuses_to_publish).to eq(['sent', 'delivered'])
    end

    context 'when transitioning from sending to read' do
      let(:service) { described_class.new(message, build_payload('read', timestamp)) }

      it 'returns sent, delivered and read as statuses to publish' do
        statuses_to_publish = service.send(:determine_statuses_to_publish)
        expect(statuses_to_publish).to eq(['sent', 'delivered', 'read'])
      end
    end

    context 'when transitioning to failed' do
      let(:error_data) { { code: 'ERROR_CODE', error_data: { details: 'Message failed to send' } } }
      let(:service) { described_class.new(message, build_payload('failed', timestamp, error_data)) }

      it 'returns only failed status' do
        statuses_to_publish = service.send(:determine_statuses_to_publish)
        expect(statuses_to_publish).to eq(['failed'])
      end
    end
  end
end
