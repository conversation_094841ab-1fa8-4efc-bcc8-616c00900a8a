# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappWebhooks do
  include ActiveJob::TestHelper

  describe '#process' do
    let(:user){ create(:user) }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
    let(:connected_account) { create(:connected_account, waba_id: '***************', phone_number_id: **********, status: ACTIVE, tenant_id: auth_data.tenant_id, created_by: user) }

    context 'message template webhooks' do
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, whatsapp_template_id: '***************') }

      context 'when template is approved' do
        let(:whatsapp_template_approved_params) { JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read) }

        it 'updates template' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateStatusUpdated))
          described_class.new(whatsapp_template_approved_params).process

          expect(whatsapp_template.reload.status).to eq('APPROVED')
          expect(whatsapp_template.reason).to eq('NONE')
          expect(whatsapp_template.additional_info).to be_nil
        end
      end

      context 'when template is rejected' do
        let(:whatsapp_template_rejected_params) { JSON.parse(file_fixture('webhooks/whatsapp-template-rejected.json').read) }

        it 'updates template' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateStatusUpdated))
          described_class.new(whatsapp_template_rejected_params).process

          expect(whatsapp_template.reload.status).to eq('REJECTED')
          expect(whatsapp_template.reason).to eq('Sample Error Reason')
          expect(whatsapp_template.additional_info).to be_nil
        end
      end

      context 'when template is paused' do
        let(:whatsapp_template_paused_params) { JSON.parse(file_fixture('webhooks/whatsapp-template-paused.json').read) }

        it 'updates template' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateStatusUpdated))
          described_class.new(whatsapp_template_paused_params).process

          expect(whatsapp_template.reload.status).to eq('PAUSED')
          expect(whatsapp_template.reason).to eq('NONE')
          expect(whatsapp_template.additional_info).to eq({ "description" => "Your WhatsApp message template has been paused for 6 hours until Aug 31 at 12:47 AM UTC because it continued to have issues.", "title" => "SECOND_PAUSE" })
        end
      end

      context 'when template is flagged' do
        let(:whatsapp_template_flagged_params) { JSON.parse(file_fixture('webhooks/whatsapp-template-flagged.json').read) }

        it 'updates template' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateStatusUpdated))
          described_class.new(whatsapp_template_flagged_params).process

          expect(whatsapp_template.reload.status).to eq('FLAGGED')
          expect(whatsapp_template.reason).to be_nil
          expect(whatsapp_template.additional_info).to eq({ "disable_date" => "DATE" })
        end
      end

      context 'when template is pending_deletion' do
        let(:whatsapp_template_pending_deletion_params) { JSON.parse(file_fixture('webhooks/whatsapp-template-pending-deletion.json').read) }

        it 'updates template' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateStatusUpdated))
          described_class.new(whatsapp_template_pending_deletion_params).process

          expect(whatsapp_template.reload.status).to eq('PENDING_DELETION')
          expect(whatsapp_template.reason).to eq('NONE')
          expect(whatsapp_template.additional_info).to be_nil
        end
      end

      context 'when error updating template' do
        let(:whatsapp_template_invalid_status_params) do
          params = JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read)
          params['entry'].first['changes'].first['value']['event'] = 'INVALID'
          params
        end

        it 'logs error' do
          expect(Rails.logger).to receive(:error).with("WhatsappWebhooks Error while updating template status field entry #{whatsapp_template_invalid_status_params['entry'].first['changes'].first} message Status is not included in the list")
          described_class.new(whatsapp_template_invalid_status_params).process
        end
      end
    end

    context 'message status webhooks' do
      context 'when message is sent' do
        let(:whatsapp_message_sent_params) { JSON.parse(file_fixture('webhooks/whatsapp-message-sent.json').read) }
        let(:whatsapp_message_delivered_params) { JSON.parse(file_fixture('webhooks/whatsapp-message-delivered.json').read) }
        let(:whatsapp_message_failed_params) { JSON.parse(file_fixture('webhooks/whatsapp-message-failed.json').read) }
        let(:whatsapp_message_read_params) { JSON.parse(file_fixture('webhooks/whatsapp-message-read.json').read) }
        let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, whatsapp_template_id: '***************') }
        let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: connected_account.tenant_id) }

        context 'and message is not present' do
          it 'logs error message' do
            expect do
              expect(Rails.logger).to receive(:error).with("Whatsapp Message with remote_id *********** and sender_number +************ not found for message status update. Retrying in 15 seconds (attempt 1)")
              described_class.new(whatsapp_message_sent_params).process
            end.to have_enqueued_job(WhatsappMessageStatusRetryJob).with(anything, 1)
          end
        end

        context 'and message is present' do
          before do
            @existing_message = create(:message, tenant_id: connected_account.tenant_id, direction: 'outgoing', remote_id: '***********', sender_number: '+************', status: 'sending', connected_account_id: connected_account.id, conversation_id: conversation.id)
          end

          context 'for sent webhook' do
            context 'and status of message is sending' do
              before do 
                expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
                expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once
              end

              it 'updates status' do
                described_class.new(whatsapp_message_sent_params).process
                expect(@existing_message.reload.status).to eq('sent')
                expect(@existing_message.sent_at).to eq('2024-06-19 09:10:44.********* +0000')
              end

              context 'when campaign_info is present on message' do
                before do
                  @existing_message.update(campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' })
                end

                it 'publishes WhatsappEntityMessageStatusTrackPublisher event' do
                  expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call).with(@existing_message)
                  described_class.new(whatsapp_message_sent_params).process
                end
              end
            end

            context 'and status of message is not sending' do
              before { @existing_message.update(status: 'delivered') }

              it 'does not update status' do
                described_class.new(whatsapp_message_sent_params).process
                expect(@existing_message.reload.status).to eq('delivered')
              end
            end
          end

          context 'for delivered webhook' do
            context 'status of message is sent' do
              before do
                @existing_message.update(status: 'sent')
                expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
                expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once
              end

              it 'updates status' do
                described_class.new(whatsapp_message_delivered_params).process
                expect(@existing_message.reload.status).to eq('delivered')
              end

              context 'when campaign_info is present on message' do
                before do
                  @existing_message.update(campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' }, status: 'sent')
                end

                it 'publishes WhatsappEntityMessageStatusTrackPublisher event' do
                  expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call).with(@existing_message)
                  described_class.new(whatsapp_message_delivered_params).process
                end
              end
            end

            context 'status of message is not sent' do
              before do
                @existing_message.update(status: 'sending')
                expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).twice
                expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).twice
              end

              it 'updates status' do
                described_class.new(whatsapp_message_delivered_params).process
                expect(@existing_message.reload.status).to eq('delivered')
              end
            end
          end

          context 'for read webhook' do
            before do
              expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).thrice
              expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).thrice
            end

            it 'updates status to read' do
              described_class.new(whatsapp_message_read_params).process
              expect(@existing_message.reload.status).to eq('read')
              expect(@existing_message.read_at).to eq('2024-06-19 09:10:44.********* +0000')
            end

            context 'when campaign_info is present on message' do
              before do
                @existing_message.update(campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' })
              end

              it 'publishes WhatsappEntityMessageStatusTrackPublisher event' do
                expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call).with(@existing_message).thrice
                described_class.new(whatsapp_message_read_params).process
              end
            end
          end

          context 'for failed webhook' do
            context 'when error is non retryable' do
              before do
                expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
                expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once
                @existing_message.update!(metadata: { retryConfig: { noOfTimes: 3, timesRetried: 2 } })
              end

              it 'updates status to failed and saves the error message' do
                described_class.new(whatsapp_message_failed_params).process
                expect(@existing_message.reload.status).to eq('failed')
                expect(@existing_message.status_message).to eq('1003 - You do not have permission to send messages to this recipient.')
              end

              context 'when failed to send session message' do
                before { @existing_message.update(remote_id: 'wamid.HBgMOTE4MzA4NDI5OTM5FQIAERgSODBERUQ1QkM5NjY5QzQ5QzZBAA==', sender_number: '15550432746') }

                it 'updates message as failed and saves the error message' do
                  described_class.new(JSON.parse(file_fixture('webhooks/session-message-failed-webhook.json').read)).process
                  expect(@existing_message.reload.status).to eq('failed')
                  expect(@existing_message.status_message).to eq('131047 - Message failed to send because more than 24 hours have passed since the customer last replied to this number.')
                end
              end

              context 'when campaign_info is present on message' do
                before do
                  @existing_message.update(campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' })
                end

                it 'publishes WhatsappEntityMessageStatusTrackPublisher event' do
                  expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call).with(@existing_message)
                  described_class.new(whatsapp_message_failed_params).process
                end
              end

              context 'when campaign_info is present on message' do
                before do
                  @existing_message.update(campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' })
                end

                it 'publishes WhatsappEntityMessageStatusTrackPublisher event' do
                  expect(Publishers::WhatsappEntityMessageStatusTrackPublisher).to receive(:call).with(@existing_message)
                  described_class.new(whatsapp_message_failed_params).process
                end
              end
            end

            context 'when error is retryable' do
              let(:whatsapp_message_failed_retryable_params) { JSON.parse(file_fixture('webhooks/whatsapp-message-failed-retryable.json').read) }

              context 'when retryConfig is not present on message' do
                before do
                  expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
                  expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once
                end

                it 'should update message status and raise events' do
                  described_class.new(whatsapp_message_failed_retryable_params).process
                  expect(@existing_message.reload.status).to eq('failed')
                  expect(@existing_message.status_message).to eq('131049 - In order to maintain a healthy ecosystem engagement, the message failed to be delivered.')
                end
              end

              context 'when all retries are exhausted' do
                before do
                  expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
                  expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once

                  @existing_message.update!(metadata: { retryConfig: { noOfTimes: '3', timesRetried: '3' } })
                end

                it 'should update message status and raise events' do
                  described_class.new(whatsapp_message_failed_retryable_params).process
                  expect(@existing_message.reload.status).to eq('failed')
                  expect(@existing_message.status_message).to eq('131049 - In order to maintain a healthy ecosystem engagement, the message failed to be delivered.')
                end
              end

              context 'when retryConfig is present and retries are not exhausted' do
                before do
                  @existing_message.update!(metadata: { retryConfig: { noOfTimes: '3' } })
                  expect(Rails.logger).not_to receive(:error)
                end

                it 'should NOT update message status and should not raise events' do
                  described_class.new(whatsapp_message_failed_retryable_params).process
                  expect(@existing_message.reload.status).to eq('sending')
                  expect(@existing_message.status_message).to eq(nil)
                end
              end
            end
          end
        end
      end
    end

    context 'incoming messages webhook' do
      let(:incoming_text_message_params) { JSON.parse(file_fixture('webhooks/incoming-text-message.json').read) }
      let(:incoming_image_media_message_params) { JSON.parse(file_fixture('webhooks/incoming-image-media-message.json').read) }
      let(:incoming_video_media_message_params) { JSON.parse(file_fixture('webhooks/incoming-video-media-message.json').read) }
      let(:incoming_audio_media_message_params) { JSON.parse(file_fixture('webhooks/incoming-audio-media-message.json').read) }
      let(:incoming_document_media_message_params) { JSON.parse(file_fixture('webhooks/incoming-document-media-message.json').read) }

      let(:admin_permissions) {
        [
          {
            id: 1,
            name: 'lead',
            description: 'has access to lead',
            limits: -1,
            units: 'count',
            action: {
              readAll: true,
              read: true,
              sms: true,
              write: true,
              update: true,
              reassign: true
            }
          },
          {
            id: 2,
            name: 'contact',
            description: 'has access to contact',
            limits: -1,
            units: 'count',
            action: {
              readAll: true,
              read: true,
              sms: true,
              write: true,
              update: true,
              reassign: true
            }
          },
          {
            id: 3,
            name: 'sms',
            description: 'has access to sms',
            limits: -1,
            units: 'count',
            action: {
              read: true,
              sms: true,
              write: true
            }
          },
          {
            id: 4,
            name: 'user',
            description: 'has access to user',
            limits: -1,
            units: 'count',
            action: {
              read: true,
            }
          }
        ]
      }

      let(:lead_response) {
        {
          content: [
            {
              id: 123,
              name: 'Lead Name',
              phoneNumbers: [
                { code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }
              ],
              recordActions: { sms: true },
              ownerId: connected_account.created_by_id
            },
            {
              id: 124,
              name: 'Lead Name 2',
              phoneNumbers: [
                { code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 112 }
              ],
              recordActions: { sms: true },
              ownerId: connected_account.created_by_id
            }
          ]
        }.to_json
      }
      let(:contact_response) {
        {
          content: [
            {
              id: 123,
              name: 'Contact Name',
              phoneNumbers: [
                { code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 113 }
              ],
              recordActions: { sms: true },
              ownerId: connected_account.created_by_id
            },
            {
              id: 124,
              name: 'Contact Name 2',
              phoneNumbers: [
                { code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 114 }
              ],
              recordActions: { sms: true },
              ownerId: connected_account.created_by_id
            }
          ]
        }.to_json
      }

      let(:message_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
      let(:sub_conversation){ create(:sub_conversation, tenant_id: user.tenant_id, connected_account_id:  connected_account.id, conversation_id: message_conversation.id) }

      before do
        stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
          headers: {
          'Accept'=>'*/*'
          }
        ).to_return(status: 200, body: {
              records: { used: 100, total: 1000 },
              storage: { used: 0.5, total: 2.0 }
            }.to_json, headers: {})
      end

      context 'for text message' do
        let!(:lead_field_mapping) { create(:field_mapping, entity_type: 'lead', connected_account: connected_account) }

        context 'when entities with received phone number exists' do
          before do
            allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          end

          context 'when conversation is not found for given phone number and connected account' do
            context 'and entities with phone are present' do
              before do
                expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
                expect(Publishers::MessageCreated).to receive(:call).once
                expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once

                stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: contact_response, headers: {})

                stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: lead_response, headers: {})
              end

              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                expect {
                  described_class.new(incoming_text_message_params).process
                }.to change(Message, :count).by(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end

              it 'updates last message received at field on the respective connected account' do
                expect {
                  described_class.new(incoming_text_message_params).process
                }.to change(Message, :count).by(1)

                expect(connected_account.reload.last_message_received_at).to be_present
              end

              it 'schedules job to associate newly created conversation with existing entities' do
                expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
                expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })

                described_class.new(incoming_text_message_params).process
              end
            end
          end

          context 'when conversation is found with the given phone number and connected account' do
            context 'and entities with phone are present' do
              let!(:outgoing_message){ create(:message, conversation_id: message_conversation.id, sub_conversation_id: sub_conversation.id, direction: 'outgoing', connected_account_id: message_conversation.connected_account_id ) }
              before do
                expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
                expect(Publishers::MessageCreated).to receive(:call).once
                expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once

                stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: contact_response, headers: {})

                stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: lead_response, headers: {})
                sub_conversation
              end

              it 'syncs message on all entities' do
                expect {
                  described_class.new(incoming_text_message_params).process
                }.to change(Message, :count).by(1)

                message = Message.last
                expect(message.related_to.pluck(:name)).to match_array(['Contact Name', 'Contact Name 2', 'Lead Name', 'Lead Name 2'])
              end

              it 'adds connected account id on synced message' do
                described_class.new(incoming_text_message_params).process
                expect(Message.last.connected_account_id).to eq(connected_account.id)
              end
              
              it 'save message with received status' do
                described_class.new(incoming_text_message_params).process
                expect(Message.last.status).to eq('received')
              end

              it 'adds existing conversation and sub conversation of the given phone number on message' do
                described_class.new(incoming_text_message_params).process

                message = Message.last
                expect(message.conversation_id).to eq(message_conversation.id)
                expect(message.sub_conversation_id).to eq(message_conversation.sub_conversations.last.id)
              end

              it 'updates last_message_received_at and is_read on conversation' do
                described_class.new(incoming_text_message_params).process
                expect(message_conversation.reload.last_message_received_at).to be_present
                expect(message_conversation.reload.is_read).to be_falsey
              end

              it 'update conversation and sub conversation status properly' do 
                described_class.new(incoming_text_message_params).process

                expect(sub_conversation.reload.status).to eq(IN_PROGRESS)
                expect(message_conversation.reload.status).to eq(IN_PROGRESS)
              end

              context 'when conversation is completed' do
                before do
                  message_conversation.update(status: COMPLETED)
                  sub_conversation.update(status: COMPLETED)
                end

                it 'If there is no ongoing sub conversation, create a new sub conversation and mark its status as NEW' do
                  expect { described_class.new(incoming_text_message_params).process }.to change(SubConversation, :count).by(1)
                  expect(message_conversation.reload.status).to eq(NEW)
                end
              end
            end

            context 'when entities with phone does not exist' do
              before do
                expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
                expect(Publishers::MessageCreated).to receive(:call).once
                expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once

                token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
                stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

                stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

                stub_request(:post, "http://localhost:8082/v1/leads").with(
                  body: {
                    ownerId: connected_account.created_by_id,
                    lastName: "Tony Stark",
                    phoneNumbers: [{
                      type: "MOBILE",
                      code: "IN",
                      primary: true,
                      dialCode: "+91",
                      value: "**********"
                    }],
                    campaign: 123,
                    source: 234,
                    subSource: "Sub Source",
                    utmSource: "UTM Source",
                    utmCampaign: "UTM Campaign",
                    utmMedium: "UTM Medium",
                    utmContent: "UTM Content",
                    utmTerm: "UTM Term"
                  },
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

                stub_request(:post, "http://localhost:8082/v1/contacts").with(
                  body: {
                    ownerId: connected_account.created_by_id,
                    lastName: "Tony Stark",
                    phoneNumbers: [{
                      type: "MOBILE",
                      code: "IN",
                      primary: true,
                      dialCode: "+91",
                      value: "**********"
                    }]
                  },
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})
              end

              it 'creates new entities according to connected account configuration' do
                expect {
                  described_class.new(incoming_text_message_params).process
                }.to change(Message, :count).by(1)

                message = Message.last
                expect(message.related_to.pluck(:entity_type)).to match_array(['lead', 'contact'])
                expect(message.related_to.pluck(:phone_number)).to match_array(['************', '************'])
                expect(message.related_to.pluck(:name)).to match_array(['Tony Stark', 'Tony Stark'])
              end

              it 'adds connected account id on message' do
                described_class.new(incoming_text_message_params).process
                expect(Message.last.connected_account_id).to eq(connected_account.id)
              end
            end

            context 'when entities with phone does not exist and connected account does not have any entities configuration' do
              before do
                connected_account.update(entities_to_create: [])
                token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
                stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

                stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                  body: {
                    fields: %w[id name phoneNumbers ownerId],
                    jsonRule: {
                      rules: [
                        {
                          id: 'multi_field',
                          field: 'multi_field',
                          type: 'multi_field',
                          input: 'multi_field',
                          operator: 'multi_field',
                          value: '**********'
                        }
                      ],
                      condition: 'OR',
                      valid: true
                    }
                  }.to_json,
                  headers: {
                    'Accept'=>'application/json',
                    'Authorization'=>"Bearer #{token}",
                    'Content-Type'=>'application/json',
                  }
                ).to_return(status: 200, body: { content: [] }.to_json, headers: {})
              end

              it 'does not create message, lead, contact' do
                expect {
                  described_class.new(incoming_text_message_params).process
                }.to change(Message, :count).by(0)
              end
            end
          end
        end
      end

      context 'for incoming media message' do
        let!(:lead_field_mapping) { create(:field_mapping, entity_type: 'lead', connected_account: connected_account) }
        let(:media_url) { "https://lookaside.fbsbx.com/whatsapp_business/attachments/?mid=***************&ext=**********&hash=ATvGO4vAkFG17B3An7azeX8Wg8xiJlmg8GxrAscU1XdUFA" }
        let(:image_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
        let(:audio_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3') }
        let(:text_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_text_file.txt', 'text/plain') }
        let(:video_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_video.mp4', 'video/mp4') }

        context 'when conversation is not found for given phone number and connected account' do
          context 'and entities with phone are present' do
            before do
              allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
              expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).twice

              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: contact_response, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: lead_response, headers: {})
            end

            context 'when incoming message type is image' do
              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(Message.count).to eq(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end

              it 'updates last message received at field on the respective connected account' do
                 expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(connected_account.reload.last_message_received_at).to be_present
              end
            end

            context 'when incoming message type is video' do
              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                expect { described_class.new(incoming_video_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(Message.count).to eq(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end
            end

            context 'when incoming message type is audio' do
              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                expect { described_class.new(incoming_audio_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(Message.count).to eq(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end
            end

            context 'when incoming message type is document' do
              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                expect { described_class.new(incoming_document_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(Message.count).to eq(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end
            end
          end
        end

        context 'when conversation is found for given phone number and connected account' do
          context 'When entities with given phone number are present' do
            before do
              allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: contact_response, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: lead_response, headers: {})
              sub_conversation
            end

            it 'syncs message on all entities' do
              expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

              expect(Message.count).to eq(1)

              message = Message.last
              expect(message.related_to.pluck(:name)).to match_array(['Contact Name', 'Contact Name 2', 'Lead Name', 'Lead Name 2'])
            end

            it 'adds connected account id on synced message' do
              expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

              expect(Message.count).to eq(1)
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end

            it 'adds existing conversation and sub conversation of the given phone number on message' do
              expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

              expect(Message.count).to eq(1)

              message = Message.last
              expect(message.conversation_id).to eq(message_conversation.id)
              expect(message.sub_conversation_id).to eq(message_conversation.sub_conversations.last.id)
            end

            it 'sets conversation owner user as a message owner' do
              expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

              expect(Message.count).to eq(1)

              message = Message.last
              expect(message.owner_id).to eq(message_conversation.owner_id)
            end
          end

          context 'when entities with phone does not exist' do
            before do
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8082/v1/leads").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }],
                  campaign: 123,
                  source: 234,
                  subSource: "Sub Source",
                  utmSource: "UTM Source",
                  utmCampaign: "UTM Campaign",
                  utmMedium: "UTM Medium",
                  utmContent: "UTM Content",
                  utmTerm: "UTM Term"
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

              stub_request(:post, "http://localhost:8082/v1/contacts").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }]
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})

              sub_conversation
            end

            it 'creates new entities according to connected account configuration' do
              expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

              expect(Message.count).to eq(1)

              message = Message.last
              expect(message.related_to.pluck(:entity_type)).to match_array(['lead', 'contact'])
              expect(message.related_to.pluck(:phone_number)).to match_array(['************', '************'])
              expect(message.related_to.pluck(:name)).to match_array(['Tony Stark', 'Tony Stark'])
            end
          end
        end

        context 'when conversation is not found for singapore phone number and connected account' do
          context 'and entities with phone are not present' do
            before do
              allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8082/v1/leads").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "SG",
                    primary: true,
                    dialCode: "+65",
                    value: "********"
                  }],
                  campaign: 123,
                  source: 234,
                  subSource: "Sub Source",
                  utmSource: "UTM Source",
                  utmCampaign: "UTM Campaign",
                  utmMedium: "UTM Medium",
                  utmContent: "UTM Content",
                  utmTerm: "UTM Term"
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

              stub_request(:post, "http://localhost:8082/v1/contacts").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "SG",
                    primary: true,
                    dialCode: "+65",
                    value: "********"
                  }]
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})
            end

            context 'when incoming message type is image' do
              it 'sync message on all entities by adding newly created conversation and sub conversation id it' do
                incoming_image_media_message_params['entry'][0]['changes'][0]['value']['messages'][0]['from'] = '65********'
                expect { described_class.new(incoming_image_media_message_params).process }.to have_enqueued_job(UploadMediaAttachmentToS3Job).on_queue('default')

                expect(Message.count).to eq(1)

                message = Message.last
                expect(message.conversation_id).to be_present
                expect(message.sub_conversation_id).to be_present
              end
            end
          end
        end   
      end

      context 'for message type button' do
        let(:incoming_button_message_webhook) { JSON.parse(file_fixture('webhooks/quick-reply-incoming-message.json').read) }
        let!(:lead_field_mapping) { create(:field_mapping, entity_type: 'lead', connected_account: connected_account) }

        context 'when entities with received phone number exists' do
          before do
            allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          end

          context 'and entities with phone are present' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: contact_response, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: lead_response, headers: {})
            end

            it 'syncs message on all entities' do
              expect {
                described_class.new(incoming_button_message_webhook).process
              }.to change(Message, :count).by(1)

              message = Message.last
              expect(message.content).to eq('STOP')
              expect(message.component_wise_content).to eq([{"text"=>"STOP", "type"=>"BODY", "value"=>nil, "format"=>"TEXT", "position"=>0}])
              expect(message.related_to.pluck(:name)).to match_array(['Contact Name', 'Contact Name 2', 'Lead Name', 'Lead Name 2'])
            end

            it 'adds connected account id on message' do
              described_class.new(incoming_button_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8082/v1/leads").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }],
                  campaign: 123,
                  source: 234,
                  subSource: "Sub Source",
                  utmSource: "UTM Source",
                  utmCampaign: "UTM Campaign",
                  utmMedium: "UTM Medium",
                  utmContent: "UTM Content",
                  utmTerm: "UTM Term"
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

              stub_request(:post, "http://localhost:8082/v1/contacts").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }]
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})
            end

            it 'creates new entities according to connected account configuration and syncs message on them' do
              expect {
                described_class.new(incoming_button_message_webhook).process
              }.to change(Message, :count).by(1)
              message = Message.last
              expect(message.related_to.pluck(:entity_type)).to match_array(['lead', 'contact'])
              expect(message.related_to.pluck(:phone_number)).to match_array(['************', '************'])
              expect(message.related_to.pluck(:name)).to match_array(['Tony Stark', 'Tony Stark'])
            end

            it 'adds connected account id on message' do
              described_class.new(incoming_button_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist and connected account does not have any entities configuration' do
            before do
              connected_account.update(entities_to_create: [])
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})
            end

            it 'does not create message, lead, contact' do
              expect {
                described_class.new(incoming_button_message_webhook).process
              }.to change(Message, :count).by(0)
            end
          end
        end
      end

      context 'for message type interactive button reply' do
        let(:interactive_button_reply_message_webhook) { JSON.parse(file_fixture('webhooks/interactive-button-reply-message.json').read) }
        let!(:lead_field_mapping) { create(:field_mapping, entity_type: 'lead', connected_account: connected_account) }

        context 'when entities with received phone number exists' do
          before do
            allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          end

          context 'and entities with phone are present' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: contact_response, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: lead_response, headers: {})
            end

            it 'syncs message on all entities' do
              expect {
                described_class.new(interactive_button_reply_message_webhook).process
              }.to change(Message, :count).by(1)

              message = Message.last
              expect(message.content).to eq('Continue')
              expect(message.component_wise_content).to eq(
                [
                  {
                    "text"=>"Continue", 
                    "type"=>"BODY", 
                    "value"=>nil, 
                    "format"=>"TEXT", 
                    "position"=>0
                  }
                ]
              )
              expect(message.related_to.pluck(:name)).to match_array(['Contact Name', 'Contact Name 2', 'Lead Name', 'Lead Name 2'])
            end

            it 'adds connected account id on message' do
              described_class.new(interactive_button_reply_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8082/v1/leads").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }],
                  campaign: 123,
                  source: 234,
                  subSource: "Sub Source",
                  utmSource: "UTM Source",
                  utmCampaign: "UTM Campaign",
                  utmMedium: "UTM Medium",
                  utmContent: "UTM Content",
                  utmTerm: "UTM Term"
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

              stub_request(:post, "http://localhost:8082/v1/contacts").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }]
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})
            end

            it 'creates new entities according to connected account configuration and syncs message on them' do
              expect {
                described_class.new(interactive_button_reply_message_webhook).process
              }.to change(Message, :count).by(1)
              message = Message.last
              expect(message.related_to.pluck(:entity_type)).to match_array(['lead', 'contact'])
              expect(message.related_to.pluck(:phone_number)).to match_array(['************', '************'])
              expect(message.related_to.pluck(:name)).to match_array(['Tony Stark', 'Tony Stark'])
            end

            it 'adds connected account id on message' do
              described_class.new(interactive_button_reply_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist and connected account does not have any entities configuration' do
            before do
              connected_account.update(entities_to_create: [])
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})
            end

            it 'does not create message, lead, contact' do
              expect {
                described_class.new(interactive_button_reply_message_webhook).process
              }.to change(Message, :count).by(0)
            end
          end
        end
      end

      context 'for message type interactive list reply' do
        let(:interactive_list_reply_message_webhook) { JSON.parse(file_fixture('webhooks/interactive-list-reply-message.json').read) }
        let!(:lead_field_mapping) { create(:field_mapping, entity_type: 'lead', connected_account: connected_account) }

        context 'when entities with received phone number exists' do
          before do
            allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          end

          context 'and entities with phone are present' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: contact_response, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: lead_response, headers: {})
            end

            it 'syncs message on all entities' do
              expect {
                described_class.new(interactive_list_reply_message_webhook).process
              }.to change(Message, :count).by(1)

              message = Message.last
              expect(message.content).to eq("Row 1\nDescription 1")
              expect(message.component_wise_content).to eq(
                [
                  {
                    "text"=>"Row 1\nDescription 1", 
                    "type"=>"BODY", 
                    "value"=>nil, 
                    "format"=>"TEXT", 
                    "position"=>0
                  }
                ]
              )
              expect(message.related_to.pluck(:name)).to match_array(['Contact Name', 'Contact Name 2', 'Lead Name', 'Lead Name 2'])
            end

            it 'adds connected account id on message' do
              described_class.new(interactive_list_reply_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist' do
            before do
              expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
              expect(Publishers::MessageCreated).to receive(:call).once
              expect(Publishers::WhatsappMessageCreatedV2Publisher).to receive(:call).once
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8082/v1/leads").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }],
                  campaign: 123,
                  source: 234,
                  subSource: "Sub Source",
                  utmSource: "UTM Source",
                  utmCampaign: "UTM Campaign",
                  utmMedium: "UTM Medium",
                  utmContent: "UTM Content",
                  utmTerm: "UTM Term"
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})

              stub_request(:post, "http://localhost:8082/v1/contacts").with(
                body: {
                  ownerId: connected_account.created_by_id,
                  lastName: "Tony Stark",
                  phoneNumbers: [{
                    type: "MOBILE",
                    code: "IN",
                    primary: true,
                    dialCode: "+91",
                    value: "**********"
                  }]
                },
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                }
              ).to_return(status: 200, body: file_fixture('contact-created-response.json').read, headers: {})
            end

            it 'creates new entities according to connected account configuration and syncs message on them' do
              expect {
                described_class.new(interactive_list_reply_message_webhook).process
              }.to change(Message, :count).by(1)
              message = Message.last
              expect(message.related_to.pluck(:entity_type)).to match_array(['lead', 'contact'])
              expect(message.related_to.pluck(:phone_number)).to match_array(['************', '************'])
              expect(message.related_to.pluck(:name)).to match_array(['Tony Stark', 'Tony Stark'])
              expect(message.component_wise_content).to eq(
                [
                  {
                    "text"=>"Row 1\nDescription 1", 
                    "type"=>"BODY", 
                    "value"=>nil, 
                    "format"=>"TEXT", 
                    "position"=>0
                  }
                ]
              )
            end

            it 'adds connected account id on message' do
              described_class.new(interactive_list_reply_message_webhook).process
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end

          context 'when entities with phone does not exist and connected account does not have any entities configuration' do
            before do
              connected_account.update(entities_to_create: [])
              token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
              stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})

              stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=100&sort=updatedAt,desc").with(
                body: {
                  fields: %w[id name phoneNumbers ownerId],
                  jsonRule: {
                    rules: [
                      {
                        id: 'multi_field',
                        field: 'multi_field',
                        type: 'multi_field',
                        input: 'multi_field',
                        operator: 'multi_field',
                        value: '**********'
                      }
                    ],
                    condition: 'OR',
                    valid: true
                  }
                }.to_json,
                headers: {
                  'Accept'=>'application/json',
                  'Authorization'=>"Bearer #{token}",
                  'Content-Type'=>'application/json',
                 }
              ).to_return(status: 200, body: { content: [] }.to_json, headers: {})
            end

            it 'does not create message, lead, contact' do
              expect {
                described_class.new(interactive_list_reply_message_webhook).process
              }.to change(Message, :count).by(0)
            end
          end
        end
      end

      context 'when connected account for phone number id is absent' do
        it 'skips syncing for the message' do
          expect_any_instance_of(Message::SyncService).not_to receive(:call)
          described_class.new(incoming_text_message_params).process
        end
      end

      context 'when connected account for phone number id is inactive' do
        before { connected_account.update(status: 'inactive') }

        it 'skips syncing for the message' do
          expect_any_instance_of(Message::SyncService).not_to receive(:call)
          described_class.new(incoming_text_message_params).process
        end
      end
    end
  end
end
