require 'rails_helper'

RSpec.describe TenantUsageService do
  let(:user) { create(:user, tenant_id: 123) }
  let(:token) { 'valid_token' }
  let(:usage_response) do
    {
      "records" => {
        "used" => 500,
        "total" => 1000
      },
      "storage" => {
        "used" => 1.5,
        "total" => 2.0
      }
    }
  end

  before do
    Thread.current[:user] = user
    Thread.current[:token] = token
  end

  after do
    Thread.current[:user] = nil
    Thread.current[:token] = nil
  end

  describe '#call' do
    context 'when API call is successful' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: usage_response.to_json)
      end

      it 'returns parsed usage data' do
        result = described_class.new.call

        expect(result).to eq({
          records: {
            used: 500,
            total: 1000
          },
          storage: {
            used: 1.5,
            total: 2.0
          }
        })
      end
    end

    context 'when API returns 404' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 404)
      end

      it 'raises InvalidDataError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
    end

    context 'when API returns 500' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 500)
      end

      it 'raises InternalServerError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InternalServerError,
          ErrorCode.internal_error
        )
      end
    end

    context 'when API returns 401' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 401)
      end

      it 'raises AuthenticationError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::AuthenticationError,
          ErrorCode.unauthorized
        )
      end
    end

    context 'when API returns invalid JSON' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: 'invalid json')
      end

      it 'raises InternalServerError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InternalServerError,
          ErrorCode.internal_error
        )
      end
    end

    context 'when API returns malformed response structure' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: { "invalid" => "structure" }.to_json)
      end

      it 'raises InternalServerError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InternalServerError,
          ErrorCode.internal_error
        )
      end
    end
  end
end
