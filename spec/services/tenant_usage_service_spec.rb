require 'rails_helper'

RSpec.describe TenantUsageService do
  let(:user) { create(:user, tenant_id: 123) }
  let(:token) { 'valid_token' }
  let(:usage_response) do
    {
      "records" => {
        "used" => 500,
        "total" => 1000
      },
      "storage" => {
        "used" => 1.5,
        "total" => 2.0
      }
    }
  end

  before do
    Thread.current[:user] = user
    Thread.current[:token] = token
    Rails.cache.clear
  end

  after do
    Thread.current[:user] = nil
    Thread.current[:token] = nil
    Rails.cache.clear
  end

  describe '#call' do
    context 'when API call is successful' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: usage_response.to_json)
      end

      it 'returns parsed usage data' do
        result = described_class.new.call

        expect(result).to eq({
          records: {
            used: 500,
            total: 1000
          },
          storage: {
            used: 1.5,
            total: 2.0
          }
        })
      end

      it 'caches the result' do
        expect(Rails.cache).to receive(:write).with("tenant_usage:#{user.tenant_id}", anything, expires_in: 5.minutes)
        described_class.new.call
      end

      it 'logs API fetch' do
        expect(Rails.logger).to receive(:info).with("TenantUsageService tenant_id: #{user.tenant_id} - Fetching from API")
        expect(Rails.logger).to receive(:info).with("TenantUsageService tenant_id: #{user.tenant_id} - Data cached for 300 seconds")
        described_class.new.call
      end
    end

    context 'when API returns 404' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 404)
      end

      it 'raises InvalidDataError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
    end

    context 'when API returns 500' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 500)
      end

      it 'raises InternalServerError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InternalServerError,
          ErrorCode.internal_error
        )
      end
    end

    context 'when API returns 401' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 401)
      end

      it 'raises AuthenticationError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::AuthenticationError,
          ErrorCode.unauthorized
        )
      end
    end

    context 'when API returns invalid JSON' do
      before do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: 'invalid json')
      end

      it 'raises InternalServerError' do
        expect { described_class.new.call }.to raise_error(
          ExceptionHandler::InternalServerError,
          ErrorCode.internal_error
        )
      end
    end

    context 'when cache exists' do
      let(:cached_data) do
        {
          records: { used: 200, total: 1000 },
          storage: { used: 0.8, total: 2.0 }
        }
      end

      before do
        Rails.cache.write("tenant_usage:#{user.tenant_id}", cached_data, expires_in: 5.minutes)
      end

      it 'returns cached data without API call' do
        expect(RestClient).not_to receive(:get)
        expect(Rails.logger).to receive(:info).with("TenantUsageService tenant_id: #{user.tenant_id} - Cache hit")

        result = described_class.new.call
        expect(result).to eq(cached_data)
      end
    end

    context 'when tenant_id is nil' do
      before do
        Thread.current[:user] = nil
      end

      it 'fetches data from API without caching' do
        stub_request(:get, "#{SERVICE_IAM}/v1/tenants/usage")
          .with(headers: { 'Authorization' => "Bearer #{token}" })
          .to_return(status: 200, body: usage_response.to_json)

        expect(Rails.cache).not_to receive(:exist?)
        expect(Rails.cache).not_to receive(:write)

        described_class.new.call
      end
    end
  end

  describe '.clear_cache' do
    let(:cached_data) do
      {
        records: { used: 200, total: 1000 },
        storage: { used: 0.8, total: 2.0 }
      }
    end

    before do
      Rails.cache.write("tenant_usage:#{user.tenant_id}", cached_data, expires_in: 5.minutes)
    end

    it 'clears cache for specific tenant' do
      expect(Rails.cache.exist?("tenant_usage:#{user.tenant_id}")).to be true
      expect(Rails.logger).to receive(:info).with("TenantUsageService - Cache cleared for tenant_id: #{user.tenant_id}")

      described_class.clear_cache(user.tenant_id)

      expect(Rails.cache.exist?("tenant_usage:#{user.tenant_id}")).to be false
    end
  end
end
