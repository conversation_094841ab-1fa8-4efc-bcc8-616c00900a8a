# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChatbotUserResponseService do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, created_by: user, updated_by: user) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: 'conv-123', chatbot_conversation_completed: false) }
  let(:message) { create(:message, conversation_id: conversation.id, content: 'Hi, I need your help!', direction: INCOMING, tenant_id: user.tenant_id, owner_id: user.id) }

  describe '.call' do
    before do
      allow(Publishers::ChatbotUserResponsePublisher).to receive(:call)
      allow(Rails.logger).to receive(:info)
    end

    context 'when all conditions are met' do
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

      it 'publishes chatbot user response event' do
        expect(Publishers::ChatbotUserResponsePublisher).to receive(:call).with(
          message: message.content,
          chatbot_conversation_id: conversation.chatbot_conversation_id,
          completed: conversation.chatbot_conversation_completed,
          is_media_message: false
        )

        described_class.call(message: message, conversation: conversation)
      end

      it 'logs successful processing' do
        expect(Rails.logger).to receive(:info).with(/Publishing chatbot user response for conversation/)

        described_class.call(message: message, conversation: conversation)
      end

      context 'when interactive_message_reply_id is provided' do
        let(:interactive_message_reply_id) { 'btn_1' }

        it 'uses interactive_message_reply_id as message content' do
          expect(Publishers::ChatbotUserResponsePublisher).to receive(:call).with(
            message: interactive_message_reply_id,
            chatbot_conversation_id: conversation.chatbot_conversation_id,
            completed: conversation.chatbot_conversation_completed,
            is_media_message: false
          )

          described_class.call(message: message, conversation: conversation, interactive_message_reply_id: interactive_message_reply_id)
        end
      end
    end

    context 'when message is not incoming' do
      let(:outgoing_message) { create(:message, conversation_id: conversation.id, content: 'Hello!', direction: 'outgoing', tenant_id: user.tenant_id, owner_id: user.id) }
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

      it 'does not publish event' do
        expect(Publishers::ChatbotUserResponsePublisher).not_to receive(:call)

        described_class.call(message: outgoing_message, conversation: conversation)
      end
    end

    context 'when conversation has no chatbot_conversation_id' do
      let(:conversation_without_chatbot_id) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: nil, phone_number: '+**********') }
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation_without_chatbot_id, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

      it 'does not publish event' do
        expect(Publishers::ChatbotUserResponsePublisher).not_to receive(:call)

        described_class.call(message: message, conversation: conversation_without_chatbot_id)
      end
    end

    context 'when chatbot conversation is completed' do
      let(:completed_conversation) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: 'conv-123', chatbot_conversation_completed: true, phone_number: '+**********') }
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: completed_conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

      it 'does not publish event' do
        expect(Publishers::ChatbotUserResponsePublisher).not_to receive(:call)

        described_class.call(message: message, conversation: completed_conversation)
      end
    end

    context 'when an error occurs' do
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

      before do
        allow(Publishers::ChatbotUserResponsePublisher).to receive(:call).and_raise(StandardError.new('Test error'))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Error processing chatbot user response/)

        described_class.call(message: message, conversation: conversation)
      end
    end
  end

  describe '#should_publish_chatbot_user_response?' do
    let(:service) { described_class.new(message: message, conversation: conversation) }

    context 'when all conditions are met' do
      it 'returns true' do
        result = service.send(:should_publish_chatbot_user_response?)
        expect(result).to be true
      end
    end

    context 'when message is not incoming' do
      let(:outgoing_message) { create(:message, conversation_id: conversation.id, content: 'Hello!', direction: 'outgoing', tenant_id: user.tenant_id, owner_id: user.id) }
      let(:service) { described_class.new(message: outgoing_message, conversation: conversation) }

      it 'returns false' do
        result = service.send(:should_publish_chatbot_user_response?)
        expect(result).to be false
      end
    end

    context 'when conversation has no chatbot_conversation_id' do
      let(:conversation_without_chatbot_id) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: nil, phone_number: '+**********') }
      let(:service) { described_class.new(message: message, conversation: conversation_without_chatbot_id) }

      it 'returns false' do
        result = service.send(:should_publish_chatbot_user_response?)
        expect(result).to be false
      end
    end

    context 'when chatbot conversation is completed' do
      let(:completed_conversation) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: 'conv-123', chatbot_conversation_completed: true, phone_number: '+**********') }
      let(:service) { described_class.new(message: message, conversation: completed_conversation) }

      it 'returns false' do
        result = service.send(:should_publish_chatbot_user_response?)
        expect(result).to be false
      end
    end
  end
end
