require 'rails_helper'

RSpec.describe UsageLimitValidatorService do
  let(:user) { create(:user, tenant_id: 123) }
  let(:usage_data) do
    {
      records: {
        used: 500,
        total: 1000
      },
      storage: {
        used: 1.5,
        total: 2.0
      }
    }
  end

  before do
    Thread.current[:user] = user
    allow_any_instance_of(TenantUsageService).to receive(:call).and_return(usage_data)
  end

  after do
    Thread.current[:user] = nil
  end

  describe '#call' do
    context 'when usage is within limits' do
      it 'returns true' do
        result = described_class.new(user.tenant_id).call
        expect(result).to be true
      end
    end

    context 'when records limit is exceeded' do
      let(:usage_data) do
        {
          records: {
            used: 1000,
            total: 1000
          },
          storage: {
            used: 1.5,
            total: 2.0
          }
        }
      end

      it 'raises UsageLimitExceededError' do
        expect { described_class.new(user.tenant_id).call }.to raise_error(
          ExceptionHandler::UsageLimitExceededError,
          "#{ErrorCode.usage_limit_exceeded}||Records limit exceeded. Please upgrade your plan or contact support."
        )
      end
    end

    context 'when storage limit is exceeded' do
      let(:usage_data) do
        {
          records: {
            used: 500,
            total: 1000
          },
          storage: {
            used: 2.0,
            total: 2.0
          }
        }
      end

      it 'raises UsageLimitExceededError' do
        expect { described_class.new(user.tenant_id).call }.to raise_error(
          ExceptionHandler::UsageLimitExceededError,
          "#{ErrorCode.usage_limit_exceeded}||Storage limit exceeded. Please upgrade your plan or contact support."
        )
      end
    end

    context 'when TenantUsageService fails' do
      before do
        allow_any_instance_of(TenantUsageService).to receive(:call).and_raise(StandardError.new('API Error'))
      end

      it 'returns true to allow operation to proceed' do
        result = described_class.new(user.tenant_id).call
        expect(result).to be true
      end
    end

    context 'when usage data is nil' do
      before do
        allow_any_instance_of(TenantUsageService).to receive(:call).and_return(nil)
      end

      it 'returns true' do
        result = described_class.new(user.tenant_id).call
        expect(result).to be true
      end
    end
  end
end
