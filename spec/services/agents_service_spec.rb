# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AgentsService do
  let(:user)               { create(:user) }
  let(:valid_auth_token)   { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)          { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data)  { User::TokenParser.parse(invalid_auth_token.token) }

  describe '#get_all' do
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user
    end

    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

        context 'when agents are present' do
          let(:params) { { connected_account_id: connected_account.id, entity_type: entity_type } }
          let(:users_list) { create_list(:user, 11, tenant_id: user.tenant_id) }

          before { connected_account.send("#{entity_type}_agents=", users_list) }

          it 'returns all agents' do
            expect(described_class.new(params).get_all).to match_array(users_list)
          end
        end

        context 'when agents are not present' do
          let(:params) { { connected_account_id: connected_account.id, entity_type: entity_type } }

          it 'returns blank resposne' do
            expect(described_class.new(params).get_all).to eq([])
          end
        end

        context 'when connected account not found' do
          let(:params) { { connected_account_id: connected_account.id + 1, entity_type: entity_type } }

          it 'raises not found' do
            expect{ described_class.new(params).get_all }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
          end
        end

        context 'when user does not have read all' do
          let(:params) { { connected_account_id: connected_account.id, entity_type: entity_type } }

          before do
            Thread.current[:auth] = invalid_auth_data
            Thread.current[:token] = invalid_auth_token
            Thread.current[:user] = user
          end

          it 'raises authentication error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to view connected account agents")
            expect{ described_class.new(params).get_all }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
          end
        end
      end
    end

    context 'when invalid entity' do
      let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
      let(:params) { { connected_account_id: connected_account.id, entity_type: 'deal' } }

      it 'raises error' do
        expect { described_class.new(params).get_all }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type')
      end
    end
  end

  describe '#save' do
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let!(:another_user) { create(:user, tenant_id: user.tenant_id) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        context 'when all agents are valid' do
          let(:params) {
            {
              connected_account_id: connected_account.id,
              entity_type: entity_type,
              agents: [
                { id: user.id, name: user.name },
                { id: another_user.id, name: another_user.name }
              ]
            }
          }
          let(:user_summary_response) do
            user_response = JSON.parse(file_fixture('user-summary-response.json').read)
            user_response.first['id'] = user.id
            user_response.last['id'] = another_user.id
            user_response
          end

          before do
            stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: user_summary_response.to_json)
          end

          it 'adds agents to connected accounts' do
            save_response = nil
            expect { save_response = described_class.new(params).save }.to change(AgentUser.where(entity_type: entity_type), :count).by(2)
            expect(save_response).to match_array([user, another_user])
            expect(connected_account.reload.updated_by).to eq(user)
          end

          it 'updates tenant id in agent user table' do
            described_class.new(params).save
            expect(AgentUser.where(connected_account_id: connected_account.id, entity_type: entity_type).pluck(:tenant_id).uniq).to eq([connected_account.tenant_id])
          end
        end

        context 'when even one of the agents is invalid' do
          let(:params) {
            {
              connected_account_id: connected_account.id,
              entity_type: entity_type,
              agents: [
                { id: user.id, name: user.name },
                { id: another_user.id, name: another_user.name }
              ]
            }
          }

          before do
            stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 404, body: '')
          end

          it 'raises invalid data' do
            expect(Rails.logger).to receive(:error).with("User::Summary iam 404")
            expect{ described_class.new(params).save }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
          end
        end

        context 'when connected account not found' do
          let(:params) { { connected_account_id: connected_account.id + 1, entity_type: entity_type } }

          it 'raises not found' do
            expect{ described_class.new(params).save }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
          end
        end

        context 'when user does not have read all' do
          let(:params) { { connected_account_id: connected_account.id, entity_type: entity_type } }

          before do
            Thread.current[:auth] = invalid_auth_data
            Thread.current[:token] = invalid_auth_token
            Thread.current[:user] = user
          end

          it 'raises authentication error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to update connected account agents")
            expect{ described_class.new(params).save }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
          end
        end
      end
    end
  end

  describe '#delete_all' do
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let!(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let!(:third_user) { create(:user, tenant_id: user.tenant_id) }
    let!(:fourth_user) { create(:user, tenant_id: user.tenant_id) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when connected account has agents' do
      let(:params) { { id: connected_account.id } }

      before do
        connected_account.lead_agents = [user, another_user]
        connected_account.contact_agents = [third_user, fourth_user]
      end

      it 'deletes all agent users for the connected account' do
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(4)

        described_class.new(params).delete_all

        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
      end

      it 'only deletes agents for the specified connected account' do
        other_connected_account = create(:connected_account, tenant_id: user.tenant_id)
        other_connected_account.lead_agents = [user]

        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(4)
        expect(AgentUser.where(connected_account_id: other_connected_account.id).count).to eq(1)

        described_class.new(params).delete_all

        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
        expect(AgentUser.where(connected_account_id: other_connected_account.id).count).to eq(1)
      end

      it 'deletes agents for both lead and contact entity types' do
        lead_agent_users = AgentUser.where(connected_account_id: connected_account.id, entity_type: LOOKUP_LEAD)
        contact_agent_users = AgentUser.where(connected_account_id: connected_account.id, entity_type: LOOKUP_CONTACT)

        expect(lead_agent_users.count).to eq(2)
        expect(contact_agent_users.count).to eq(2)

        described_class.new(params).delete_all

        expect(lead_agent_users.count).to eq(0)
        expect(contact_agent_users.count).to eq(0)
      end

      it 'removes associations from connected account' do
        expect(connected_account.lead_agents.count).to eq(2)
        expect(connected_account.contact_agents.count).to eq(2)

        described_class.new(params).delete_all

        connected_account.reload
        expect(connected_account.lead_agents.count).to eq(0)
        expect(connected_account.contact_agents.count).to eq(0)
      end
    end

    context 'when connected account has no agents' do
      let(:params) { { id: connected_account.id } }

      it 'does not raise error' do
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)

        expect { described_class.new(params).delete_all }.not_to raise_error

        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
      end
    end
  end
end
