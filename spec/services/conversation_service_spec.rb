# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConversationService, type: :service do
  describe '#fetch_messages_and_related_to' do
    let(:user){ create(:user) }
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, status: 'active') }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
    let(:second_user) { create(:user) }
    let(:second_user_auth_token){ build(:auth_token, :without_sms_permission, user_id: second_user.id, tenant_id: second_user.tenant_id, username: second_user.name) }
    let(:second_user_auth_data){ User::TokenParser.parse(second_user_auth_token.token) }
    let(:message_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:second_user_conversation){ create(:conversation, phone_number: '+************', tenant_id: second_user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:valid_rule){ { 'type': 'related_to_lookup', 'field': 'related_to', 'id': 'related_to', 'operator': 'equal', 'value': { 'id': 34343, 'entity': 'lead' } } }
    let!(:second_user_message) { create(:message, owner: user, tenant_id: user.tenant_id) }
    let!(:first_user_messages) { create_list(:message, 3, owner: user, tenant_id: user.tenant_id, conversation_id: message_conversation.id) }
    let(:first_conversation_params) do
      {
        "id": message_conversation.id,
        "json_rule": {
          "condition": 'AND',
          "rules": [valid_rule]
        }
      }.with_indifferent_access
    end

    let(:second_conversation_params) do
      {
        "id": second_user_conversation.id,
        "json_rule": {
          "condition": 'AND',
          "rules": [valid_rule]
        }
      }.with_indifferent_access
    end

    let(:conversation_params_without_json_rule) do
      {
        "id": message_conversation.id,
      }.with_indifferent_access
    end

    context 'when invalid request' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
      end

      context 'when phone number conversation is not found with the given conversation id' do
        it 'raises error' do
          expect{ described_class.new({}).fetch_messages_and_related_to }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
        end
      end

      context 'when user does not have conversation read permission' do
        before(:each) do
          Thread.current[:auth] = second_user_auth_data
          Thread.current[:token] = second_user_auth_token.token
          Thread.current[:user] = second_user

          allow_any_instance_of(GenerateToken).to receive(:call).and_return(second_user_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{second_user_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
        end

        it 'raises error' do
          expect { described_class.new(second_conversation_params).fetch_messages_and_related_to }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
        end
      end
    end

    context 'when valid request' do
      let(:look_up){create(:look_up, tenant_id: message_conversation.tenant_id, entity_type: 'lead')}
      let(:conversation_look_up){create(:conversation_look_up, conversation_id: message_conversation.id, tenant_id: message_conversation.tenant_id, look_up_id: look_up.id)}

      before(:each) do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        conversation_look_up
      end

      it 'returns messages present in the conversation with entity permissions' do
        response, related_to, entity_permissions = described_class.new(first_conversation_params).fetch_messages_and_related_to

        expect(response.count).to eq(3)
        expect(response.map { |message| message[:conversation_id] }).to include(message_conversation.id)
        expect(related_to).to eq([look_up])

        expect(entity_permissions).to be_a(Hash)
        expect(entity_permissions).to have_key(:isEntityAccessible)
        expect(entity_permissions).to have_key(:isPhoneNumberPresent)
        expect(entity_permissions[:isEntityAccessible]).to be(true)
      end

      context 'when EntityService throws NotFound exception' do
        before do
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 404, body: {}.to_json, headers: {})
        end

        context 'when user has basic conversation permissions' do
          before do
            allow(user).to receive(:can_read_all_conversation?).and_return(true)
          end

          it 'returns messages present in the conversation with entity permissions' do
            response, related_to, entity_permissions = described_class.new(first_conversation_params).fetch_messages_and_related_to

            expect(response.count).to eq(3)
            expect(response.map { |message| message[:conversation_id] }).to include(message_conversation.id)
            expect(related_to).to eq([look_up])
            
            expect(entity_permissions).to be_a(Hash)
            expect(entity_permissions).to have_key(:isEntityAccessible)
            expect(entity_permissions).to have_key(:isPhoneNumberPresent)
            expect(entity_permissions[:isEntityAccessible]).to be(false)
            expect(entity_permissions[:isPhoneNumberPresent]).to be(false)
          end
        end

        context 'when user does not have basic conversation permissions' do
          before do
            allow(user).to receive(:can_read_all_conversation?).and_return(false)
            allow(user).to receive(:id).and_return(999)
          end

          it 'raises MessageNotAllowedError' do
            expect { described_class.new(first_conversation_params).fetch_messages_and_related_to }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
          end
        end
      end

      context 'when conversation_look_ups is not present' do
        before do
          conversation_look_up.destroy
        end

        context 'when user has basic conversation permissions' do
          before do
            allow(user).to receive(:can_read_all_conversation?).and_return(true)
          end

          it 'returns messages present in the conversation with entity permissions' do
            response, related_to, entity_permissions = described_class.new(first_conversation_params).fetch_messages_and_related_to

            expect(response.count).to eq(3)
            expect(response.map { |message| message[:conversation_id] }).to include(message_conversation.id)
            expect(related_to).to be_empty
            
            expect(entity_permissions).to be_a(Hash)
            expect(entity_permissions).to have_key(:isEntityAccessible)
            expect(entity_permissions).to have_key(:isPhoneNumberPresent)
            expect(entity_permissions[:isEntityAccessible]).to be(false)
            expect(entity_permissions[:isPhoneNumberPresent]).to be(true)
          end
        end

        context 'when user does not have basic conversation permissions' do
          before do
            allow(user).to receive(:can_read_all_conversation?).and_return(false)
            allow(user).to receive(:id).and_return(999)
          end

          it 'raises MessageNotAllowedError' do
            expect { described_class.new(first_conversation_params).fetch_messages_and_related_to }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
          end
        end
      end
    end
  end

  describe '#associate_conversations_by_given_phone_numbers_with_entity' do
    let(:user){ create(:user) }
    let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id) }
    let(:second_connected_account){ create(:connected_account, tenant_id: user.tenant_id) }

    let!(:conversation){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+91**********') }
    let!(:conversation_2){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+91**********') }
    let!(:conversation_3){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: second_connected_account.id, phone_number: '+91**********') }

    let!(:conversation_4){ create(:conversation, phone_number: '+91**********') }
    let!(:conversation_5){ create(:conversation, phone_number: '+91**********') }
    let(:existing_look_up){ create(:look_up, entity_type: 'lead', tenant_id: user.tenant_id, phone_number: '+91**********') }

    def get_valid_params(tenant_id, entity_id = 123)
      {
        entity_name: 'test user',
        entity_type: 'lead',
        entity_id: entity_id,
        tenant_id: tenant_id,
        phone_numbers: [{
          'type' => 'MOBILE',
          'code' => 'IN',
          'value' => '**********',
          'dialCode' => '+91',
          'primary' => true
        },
        {
          'type' => 'WORK',
          'code' => 'IN',
          'value' => '**********',
          'dialCode' => '+91',
          'primary' => false
        }]
      }
    end

    context 'when invalid request' do
     context 'when phone numbers are not present' do
        it 'should not add converation lookups, lookup' do
          params = {
            entity_type: 'lead',
            entity_id: 123,
            tenant_id: 333
          }
          expect{
            described_class.new(params).associate_conversations_by_given_phone_numbers_with_entity
          }.to change(ConversationLookUp, :count).by(0)
          .and change(LookUp, :count).by(0)
        end
      end

      context 'when conversations are not present for given phone numbers' do
        it 'should not add converation lookups, lookup' do
          params = {
            entity_type: 'lead',
            entity_id: 123,
            tenant_id: 333,
            phone_numbers: [{
              'type' => 'MOBILE',
              'code' => 'IN',
              'value' => '9999999995',
              'dialCode' => '+91',
              'primary' => true
            },
            {
              'type' => 'WORK',
              'code' => 'IN',
              'value' => '9999999994',
              'dialCode' => '+91',
              'primary' => false
            }]
          }

          expect{
            described_class.new(params).associate_conversations_by_given_phone_numbers_with_entity
          }.to change(ConversationLookUp, :count).by(0)
          .and change(LookUp, :count).by(0)
        end
      end
    end

    context 'when valid request' do
      context 'when entity is not present in lookup' do

        before do
          valid_auth_token = build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
          allow(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:get, SERVICE_SALES + "/v1/leads/123").
          with(
            headers: {
            "Authorization" => "Bearer #{ valid_auth_token.token }"
            }).
          to_return(status: 200, body: { "id": 1234, "ownerId": 1, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})
        end

        it 'associates conversations with entity and creates lookup' do
          expect {
            described_class.new(get_valid_params(user.tenant_id)).associate_conversations_by_given_phone_numbers_with_entity
          }.to change(ConversationLookUp, :count).by(3)
          .and change(LookUp, :count).by(2)

          expect(LookUp.last.name).to eq('test user')
        end
      end
      
      context 'when entity is already present in lookup' do
        before do
          existing_look_up
          existing_look_up.update!(owner_id: 123)
          valid_auth_token = build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
          allow(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
          
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{existing_look_up.entity_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{ valid_auth_token.token }"
            }).
          to_return(status: 200, body: { "id": 1234, "ownerId": 1, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})
        end

        it 'associates conversations with entity' do
          expect{
            described_class.new(get_valid_params(user.tenant_id, existing_look_up.entity_id)).associate_conversations_by_given_phone_numbers_with_entity
          }.to change(ConversationLookUp, :count).by(3)
          .and change(LookUp, :count).by (1)
        end
      end
    end
  end

  describe '#fetch_conversations' do
    let(:user) { create(:user)}
    let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    
    let(:connected_account_id_1){create(:connected_account, tenant_id: user.tenant_id)}
    
    let(:another_user) { create(:user, tenant_id: user.tenant_id)}
    let(:another_user_auth_token){ build(:auth_token, :without_sms_permission, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name) }
    let(:another_user_auth_data){ User::TokenParser.parse(another_user_auth_token.token) }

    let!(:conversation_user_owner_1) {create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id, last_activity_at: 10.hours.ago)}
    let!(:other_tenant_conversation) {create(:conversation, phone_number: '+************')}
    let!(:conversation_another_user_owner) {create(:conversation, owner_id: another_user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id)}

    context 'when invalid request' do
      before do
        Thread.current[:auth] = another_user_auth_data
        Thread.current[:token] = another_user_auth_token.token
        Thread.current[:user] = another_user
      end

      context 'when user does not have read permission on sms' do
        it 'raises error' do
          expect{ described_class.new({}).fetch_conversations }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
        end
      end
    end

    context 'when valid request' do
      before do
        Thread.current[:auth] = valid_auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
      end

      it 'returns conversations' do
        conversations = described_class.new({}).fetch_conversations

        expect(conversations.count).to eq(2)
        expect(conversations.map(&:id)).to eq([conversation_user_owner_1.id, conversation_another_user_owner.id])
      end
    end
  end

  describe '#delete_conversation' do
    let(:user) { create(:user) }
    let(:valid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let!(:conversation) { create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, connected_account_id: connected_account.id) }
    let!(:message) { create(:message, conversation_id: conversation.id, tenant_id: user.tenant_id, owner: user, message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id) }
    let(:look_up) { create(:look_up, tenant_id: user.tenant_id, entity_type: LOOKUP_CONTACT) }
    let!(:conversation_look_up) { create(:conversation_look_up, conversation_id: conversation.id, tenant_id: user.tenant_id, look_up_id: look_up.id) }
    let!(:sub_conversation) { create(:sub_conversation, conversation_id: conversation.id) }
    let(:deleted_at){ DateTime.now }
    let(:contact_deal_association) { create(:contact_deal_association, contact_id: look_up.entity_id, deal_name: 'Software License Deal', tenant_id: user.tenant_id) }

    context 'when user has required permissions' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
        allow(DateTime).to receive(:now).and_return(deleted_at)
      end

      it 'soft deletes the conversation' do
        params = { id: conversation.id }.with_indifferent_access
        
        expect {
          described_class.new(params).delete_conversation
        }.to change { conversation.reload.deleted_at }.from(nil).to(be_present)
      end

      it 'publishes message deleted events for all messages' do
        params = { id: conversation.id }.with_indifferent_access
        contact_deal_association

        expect(Publishers::MessageDeleted).to receive(:call).with(user.tenant_id, user.id, {
          "id"=> message.id,
          "tenantId"=> user.tenant_id,
          "content"=>"Sample text",
          "medium"=>"whatsapp",
          "direction"=>message.direction,
          "sentAt"=>message.sent_at.iso8601(3),
          "deliveredAt"=>message.delivered_at.iso8601(3),
          "readAt"=>message.read_at.iso8601(3),
          "recipientNumber"=>"12312312",
          "senderNumber"=>"12312313",
          "createdAt"=>message.created_at.iso8601(3),
          "updatedAt"=>message.updated_at.iso8601(3),
          "messageType"=>"whatsapp_business",
          "statusMessage"=>nil,
          "conversationId"=>conversation.id,
          "status"=>"Sent",
          "subConversationId"=>nil,
          "owner"=>{"id"=>user.id, "name"=>user.name},
          "relatedTo"=>[
            {
              "name"=>look_up.name,
              "id"=>look_up.entity_id,
              "entity"=>look_up.entity_type,
              "ownerId"=>look_up.owner_id
            },
            {
              "id"=>contact_deal_association.deal_id,
              "name"=>"Software License Deal",
              "entity"=>"deal"
            }
          ],
          "components"=>[],
          "recipients"=>[],
          "attachments"=>[],
          "deletedBy"=>{"id"=>user.id, "name"=>user.name},
          "deletedAt"=>deleted_at.utc.iso8601(3)
        })
        
        described_class.new(params).delete_conversation
      end
    end

    context 'when user does not have required permissions' do
      let(:unauthorized_user) { create(:user) }
      let(:unauthorized_auth_token) { build(:auth_token, :with_meesage_read_permission, user_id: unauthorized_user.id, tenant_id: unauthorized_user.tenant_id) }
      let(:unauthorized_auth_data) { User::TokenParser.parse(unauthorized_auth_token.token) }

      before do
        Thread.current[:auth] = unauthorized_auth_data
        Thread.current[:token] = unauthorized_auth_token.token
        Thread.current[:user] = unauthorized_user
      end

      it 'raises DeleteNotAllowedError' do
        params = { id: conversation.id }.with_indifferent_access
        expect { described_class.new(params).delete_conversation }.to raise_error(ExceptionHandler::DeleteNotAllowedError, '022008')
      end
    end

    context 'when conversation does not exist' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
      end

      it 'raises ConversationNotFoundError' do
        params = { id: 'invalid_id' }.with_indifferent_access
        expect { described_class.new(params).delete_conversation }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
      end
    end
  end

  describe '#cleanup_soft_deleted_conversations' do
    before do
      @user = create(:user)
      @connected_account = create(:connected_account, tenant_id: @user.tenant_id)
      @conversation = create(:conversation, owner_id: @user.id, tenant_id: @user.tenant_id, connected_account_id: @connected_account.id)
      @message = create(:message, conversation_id: @conversation.id, tenant_id: @user.tenant_id)
      @look_up = create(:look_up, tenant_id: @user.tenant_id, entity_type: LOOKUP_LEAD)
      @conversation_look_up = create(:conversation_look_up, conversation_id: @conversation.id, tenant_id: @user.tenant_id, look_up_id: @look_up.id)
      @sub_conversation = create(:sub_conversation, conversation_id: @conversation.id)

      @conversation.soft_delete!
    end

    it 'hard deletes soft-deleted conversations and their messages' do
      expect {
        described_class.new({}).cleanup_soft_deleted_conversations
      }.to change(Conversation.unscoped, :count).by(-1)
        .and change(Message, :count).by(-1)
        .and change(ConversationLookUp, :count).by(-1)
        .and change(SubConversation, :count).by(-1)
    end

    it 'does not publish message deleted events during cleanup' do
      allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
      allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
      
      described_class.new({}).cleanup_soft_deleted_conversations
      
      expect(Publishers::MessageDeleted).not_to have_received(:call)
    end

    it 'uses conversation owner_id and tenant_id for cleanup' do
      allow(Message::DeleteMessageService).to receive(:new).and_call_original
      
      described_class.new({}).cleanup_soft_deleted_conversations
      
      expect(Message::DeleteMessageService).to have_received(:new).with(
        @message.id,
        false,
        { user_id: @user.id, tenant_id: @user.tenant_id },
        true
      )
    end

    context 'when message has attachments' do
      before do
        @attachment = create(:attachment, message: @message)
        allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
      end

      it 'deletes attachments from S3' do
        described_class.new({}).cleanup_soft_deleted_conversations
        
        expect(S3::DeleteFileFromS3).to have_received(:call).with([@attachment.file_name], S3_ATTACHMENT_BUCKET)
      end
    end

    context 'when multiple soft-deleted conversations exist' do
      before do
        @second_conversation = create(:conversation, owner_id: @user.id, tenant_id: @user.tenant_id, connected_account_id: @connected_account.id)
        @second_message = create(:message, conversation_id: @second_conversation.id, tenant_id: @user.tenant_id)
        @second_conversation.soft_delete!
      end

      it 'cleans up all soft-deleted conversations' do
        expect {
          described_class.new({}).cleanup_soft_deleted_conversations
        }.to change(Conversation.unscoped, :count).by(-2)
          .and change(Message, :count).by(-2)
      end
    end
  end

  describe '#create_or_fetch_conversation' do
    let(:user) { create(:user, id: 4010) }
    let(:tenant_id) { user.tenant_id }
    let(:connected_account) { create(:connected_account, tenant_id: tenant_id, status: 'active', is_verified: true) }
    let(:second_connected_account) { create(:connected_account, tenant_id: tenant_id, status: 'active', is_verified: true) }
    let(:entity_id) { 34343 }
    let(:entity_type) { 'lead' }
    let(:entity_phone_number) { '+************' }
    let(:entity_owner_id) { user.id }
    let(:look_up) { create(:look_up, entity_id: entity_id, entity_type: entity_type, tenant_id: tenant_id, phone_number: entity_phone_number, owner_id: entity_owner_id) }
    let(:last_conversation) { create(:conversation, phone_number: entity_phone_number, tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
    let(:auth_token) { build(:auth_token, user_id: user.id, tenant_id: tenant_id, username: user.name) }

    before do
      Thread.current[:auth] = User::TokenParser.parse(auth_token.token)
      Thread.current[:token] = auth_token.token
      Thread.current[:user] = user
      allow(AssociateConversationWithEntitiesJob).to receive(:perform_later)
    end

    context 'when entity-based and all is valid' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
      end
      it 'creates a new conversation and associates look_up' do
        params = {
          last_conversation_id: last_conversation.id,
          connected_account_id: second_connected_account.id,
          entity_id: entity_id,
          entity_type: entity_type
        }.with_indifferent_access
        result = described_class.new(params).create_or_fetch_conversation
        expect(result[:id]).to be_present
        expect(Conversation.find(result[:id]).phone_number).to eq(entity_phone_number)
        expect(AssociateConversationWithEntitiesJob).to have_received(:perform_later).at_least(:once)
      end
    end

    context 'when phone-based and all is valid' do
      let(:phone_conversation) { create(:conversation, phone_number: '+************', tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
      it 'creates a new conversation and schedules jobs' do
        params = {
          last_conversation_id: phone_conversation.id,
          connected_account_id: second_connected_account.id
        }.with_indifferent_access
        result = described_class.new(params).create_or_fetch_conversation
        expect(result[:id]).to be_present
        expect(Conversation.find(result[:id]).phone_number).to eq(phone_conversation.phone_number)
        expect(AssociateConversationWithEntitiesJob).to have_received(:perform_later).at_least(:once)
      end
    end

    context 'when conversation already exists for connected account' do
      let!(:existing_conversation) { create(:conversation, phone_number: entity_phone_number, tenant_id: tenant_id, connected_account_id: second_connected_account.id, owner_id: user.id) }
      it 'returns the existing conversation id' do
        params = {
          last_conversation_id: last_conversation.id,
          connected_account_id: second_connected_account.id
        }.with_indifferent_access
        result = described_class.new(params).create_or_fetch_conversation
        expect(result[:id]).to eq(existing_conversation.id)
      end
    end

    context 'when last_conversation_id is missing' do
      it 'raises InvalidDataError' do
        params = { connected_account_id: second_connected_account.id }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::InvalidDataError)
      end
    end

    context 'when last_conversation_id is invalid' do
      it 'raises ConversationNotFoundError' do
        params = { last_conversation_id: 999999, connected_account_id: second_connected_account.id }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::ConversationNotFoundError)
      end
    end

    context 'when connected_account is inactive or unverified' do
      let(:inactive_account) { create(:connected_account, tenant_id: tenant_id, status: 'inactive', is_verified: false) }
      it 'raises NotFound' do
        params = { last_conversation_id: last_conversation.id, connected_account_id: inactive_account.id }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::NotFound)
      end
    end

    context 'when phone number is not present on entity' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: {
            content: [
              {
                id: entity_id,
                firstName: 'lead first name',
                ownerId: entity_owner_id,
                phoneNumbers: [
                  { dialCode: '+91', value: '**********', id: 999999 }
                ]
              }
            ]
          }.to_json,
          headers: {}
        )
      end
      it 'raises MessageNotAllowedError' do
        params = {
          last_conversation_id: last_conversation.id,
          connected_account_id: second_connected_account.id,
          entity_id: entity_id,
          entity_type: entity_type
        }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::MessageNotAllowedError)
      end
    end

    context 'when user lacks permission (entity-based)' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
        allow_any_instance_of(User).to receive(:can_send_conversation_message?).and_return(false)
      end
      it 'raises MessageNotAllowedError' do
        params = {
          last_conversation_id: last_conversation.id,
          connected_account_id: second_connected_account.id,
          entity_id: entity_id,
          entity_type: entity_type
        }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::MessageNotAllowedError)
      end
    end

    context 'when user lacks permission (phone-based)' do
      before do
        allow_any_instance_of(User).to receive(:can_create_conversation?).and_return(false)
      end
      it 'raises MessageNotAllowedError' do
        params = {
          last_conversation_id: last_conversation.id,
          connected_account_id: second_connected_account.id
        }.with_indifferent_access
        expect { described_class.new(params).create_or_fetch_conversation }.to raise_error(ExceptionHandler::MessageNotAllowedError)
      end
    end
  end

  describe '#mark_as_completed' do
    let(:user) { create(:user) }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let(:auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data) { User::TokenParser.parse(auth_token.token) }
    let!(:conversation) { create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, connected_account_id: connected_account.id) }
    let!(:sub_conversation) { create(:sub_conversation, conversation_id: conversation.id, status: IN_PROGRESS) }
    let(:params) { { id: conversation.id } }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = auth_token.token
      Thread.current[:user] = user
      allow(Publishers::ChatbotConversationCompletedPublisher).to receive(:call)
      allow(Rails.logger).to receive(:info)
    end

    context 'when chatbot is not in progress' do
      before do
        conversation.update!(chatbot_conversation_id: nil, chatbot_conversation_completed: false)
      end

      it 'does not update chatbot_conversation_completed' do
        expect { described_class.new(params).mark_as_completed }.not_to change { conversation.reload.chatbot_conversation_completed }
      end

      it 'does not call the publisher' do
        described_class.new(params).mark_as_completed
        expect(Publishers::ChatbotConversationCompletedPublisher).not_to have_received(:call)
      end
    end

    context 'when chatbot is in progress' do
      before do
        conversation.update!(chatbot_conversation_id: 'chatbot-123', chatbot_conversation_completed: false)
      end

      it 'updates chatbot_conversation_completed to true' do
        expect { described_class.new(params).mark_as_completed }.to change { conversation.reload.chatbot_conversation_completed }.from(false).to(true)
      end

      it 'calls the publisher with the conversation' do
        described_class.new(params).mark_as_completed
        expect(Publishers::ChatbotConversationCompletedPublisher).to have_received(:call).with(conversation)
      end

      it 'marks conversation and sub_conversation as COMPLETED' do
        described_class.new(params).mark_as_completed
        expect(conversation.reload.status).to eq(COMPLETED)
        expect(sub_conversation.reload.status).to eq(COMPLETED)
      end
    end
  end

  describe '#get_conversation_by_entity' do
    let(:user) { create(:user, id: 4010) }
    let(:entity_id) { '123' }
    let(:entity_type) { 'lead' }
    let(:entity_name) { 'John Doe' }
    let(:phone_id) { 207783 }
    let(:phone_number) { '+************' }
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
    let(:entity_data) do
      {
        'id' => entity_id,
        'entityType' => entity_type,
        'firstName' => 'John',
        'lastName' => 'Doe',
        'ownerId' => user.id,
        'phoneNumbers' => [
          {
            'id' => phone_id,
            'dialCode' => '+1',
            'value' => '*********'
          }
        ]
      }
    end
    let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }

    before do
      Thread.current[:auth] = valid_auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
      allow(user).to receive(:can_user_read_conversation?).and_return(true)
      allow(user).to receive(:can_create_conversation?).and_return(true)

      stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
    end

    context 'when entity exists' do
      context 'when conversation exists' do
        let!(:conversation) do
          create(:conversation,
                 tenant_id: user.tenant_id,
                 phone_number: phone_number,
                 connected_account_id: connected_account.id,
                 last_message_received_at: 1.hour.ago)
        end

        context 'when conversation has multiple look-ups' do
          let!(:look_up1) { create(:look_up, entity_id: entity_id, entity_type: entity_type, tenant_id: user.tenant_id) }
          let!(:look_up2) { create(:look_up, entity_id: '456', entity_type: 'lead', tenant_id: user.tenant_id) }
          
          before do
            conversation.look_ups << look_up1
            conversation.look_ups << look_up2
          end

          it 'returns existing conversation with all look-ups' do
            result = described_class.new(
              entity_id: entity_id,
              entity_type: entity_type,
              entity_name: entity_name
            ).get_conversation_by_entity

            expect(result[:conversation]).to eq(conversation)
            expect(result[:entity_data][:id]).to eq(entity_id)
            expect(result[:entity_data][:entity_type]).to eq(entity_type)
            expect(result[:entity_data][:entity_name]).to eq(entity_name)
            expect(conversation.look_ups.count).to eq(2)
          end
        end

        context 'when conversation has no look-ups' do
          it 'raises conversation not found error' do
            expect {
              described_class.new(
              entity_id: entity_id,
              entity_type: entity_type,
              entity_name: entity_name
            ).get_conversation_by_entity
            }.to raise_error(ExceptionHandler::NotFound)
          end
        end
      end

      context 'when conversation does not exist' do
        context 'when phone_id is provided' do
          before do
            allow(AssociateConversationWithEntitiesJob).to receive(:perform_later)
          end
          it 'creates new conversation' do
            result = described_class.new(
              entity_id: entity_id,
              entity_type: entity_type,
              entity_name: entity_name,
              phone_id: phone_id
            ).get_conversation_by_entity

            expect(result[:conversation].id).to be_present
            expect(result[:entity_data][:id]).to eq(entity_id)
            expect(result[:entity_data][:entity_type]).to eq(entity_type)
            expect(result[:entity_data][:entity_name]).to eq(entity_name)
            expect(AssociateConversationWithEntitiesJob).to have_received(:perform_later).exactly(2).times
          end
        end

        context 'when phone_id is not provided' do
          it 'raises conversation not found error' do
            expect {
              described_class.new(
                entity_id: entity_id,
                entity_type: entity_type,
                entity_name: entity_name
              ).get_conversation_by_entity
            }.to raise_error(ExceptionHandler::NotFound)
          end
        end
      end
    end

    context 'when entity does not exist' do
      before do
        allow_any_instance_of(EntityService).to receive(:get_by_id).and_return(nil)
      end

      it 'raises not found error' do
        expect {
          described_class.new(
            entity_id: entity_id,
            entity_type: entity_type,
            entity_name: entity_name
          ).get_conversation_by_entity
        }.to raise_error(ExceptionHandler::NotFound)
      end
    end

    context 'when user does not have permission' do
      before do
        allow(user).to receive(:can_user_read_conversation?).and_return(false)
      end

      it 'raises message not allowed error' do
        expect {
          described_class.new(
            entity_id: entity_id,
            entity_type: entity_type,
            entity_name: entity_name
          ).get_conversation_by_entity
        }.to raise_error(ExceptionHandler::NotFound)
      end
    end

    context 'when phone number is not found' do
      let(:entity_data) do
        {
          'id' => entity_id,
          'entityType' => entity_type,
          'firstName' => 'John',
          'lastName' => 'Doe',
          'ownerId' => user.id,
          'phoneNumbers' => []
        }
      end

      before do
        allow_any_instance_of(EntityService).to receive(:get_by_id).and_return(entity_data)
      end

      it 'raises invalid phone number error' do
        expect {
          described_class.new(
            entity_id: entity_id,
            entity_type: entity_type,
            entity_name: entity_name,
            phone_id: phone_id
          ).get_conversation_by_entity
        }.to raise_error(ExceptionHandler::InvalidDataError)
      end
    end

    context 'when no active connected account exists' do
      before do
        connected_account.update!(status: 'inactive')
      end

      it 'raises not found error' do
        expect {
          described_class.new(
            entity_id: entity_id,
            entity_type: entity_type,
            entity_name: entity_name,
            phone_id: phone_id
          ).get_conversation_by_entity
        }.to raise_error(ExceptionHandler::NotFound)
      end
    end
  end
end
