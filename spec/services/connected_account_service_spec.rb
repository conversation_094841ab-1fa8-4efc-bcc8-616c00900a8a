# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConnectedAccountService do
  let(:user){ create(:user) }
  let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
  let(:second_user){ create(:user, id: 4010) }
  let(:second_user_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: second_user.id, tenant_id: second_user.tenant_id, username: second_user.name) }
  let(:second_user_auth_data){ User::TokenParser.parse(second_user_auth_token.token) }
  let(:third_user) { create(:user) }
  let(:third_user_auth_token){ build(:auth_token, :with_meesage_read_permission, user_id: third_user.id, tenant_id: third_user.tenant_id, username: third_user.name) }
  let(:third_user_auth_data){ User::TokenParser.parse(third_user_auth_token.token) }
  let(:invalid_auth_token){ build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data){ User::TokenParser.parse(invalid_auth_token.token) }
  let(:valid_params) {
    {
      phone_number_id: '***************',
      waba_id: '230809620119620',
      auth_code: 'AQDXzbcCitGOWxYnE1-qgwefwnP7-f7OkEXLKPjSVDxoJ4FJDPgmans4itLYX4dEm__EVUpEroFyl79HqnMhvVftYEeYdelngKyfIFIEp4GJGGUvAhHnId8qpJGJiNga8sDbAk3-VyVLS2tjE1lvRk691T09skeClwWw493vz4OwWYBnUrXXyVPGsmIx2dp24o83ws28_Et-JzmpnmXmmlu6-zgPWioEpIxwaeGKQVQFqmLWyXQsf0rzQqCaVNl181BvUafq2tog6u8q7e_ImRYxzxzHlT_tvzE-ptAyThrcyIVvJWRiIMZo1xCK60VTtxzJ_pCijXi-06epw03FS16-bxtbLRj7OZkxP1_J9OkliAflu26Q9PKs4Q9pwa3Z4jjcYev7gIvpVw1aTinKIJMo',
      entities_to_create: []
    }
  }

  let(:incorrect_code_error_payload) do
    JSON.parse({
      "error": {
        "message": 'This authorization code has expired.',
        "type": 'OAuthException',
        "code": 100,
        "error_subcode": 36_007,
        "fbtrace_id": 'A4NItWtMw2FflXBSo4Ress8'
      }
    }.to_json)
  end

  let(:access_token_response) do
    JSON.parse({
      "access_token": "EAANNzXrhnuwBOZBY9ZBeCTZAm7AZA7woZA1WnBYwhVq0vyZCrYbGX2akZCTflnGWRydEaOCGx57ZCz00yIRSoJ1iZBcyMyB9u9jKZCRbq7V6RakLZARK4Bi9BLXlbI8dWGCS83t7V7DK1FaRiLTZBeZACY7gX07OGPZAKkXEnBzgpZAzV5JIcLw82HcmmlihE0LJavZC8BXcYP3iqulIqPYrscdmbQW7NYPpUvjksadbsjakdbsajdbaskdbaskjdbaskdbaskjdb",
      "token_type": "bearer"
    }.to_json)
  end

  let(:phone_number_response) do
    JSON.parse({
      "verified_name": "Kylas Demo",
      "code_verification_status": "VERIFIED",
      "display_phone_number": "+91 70302 40148",
      "quality_rating": "UNKNOWN",
      "platform_type": "NOT_APPLICABLE",
      "throughput": {
          "level": "NOT_APPLICABLE"
      },
      "last_onboarded_time": "2024-02-28T07:29:01+0000",
      "id": "***************"
    }.to_json)
  end

  describe '#get_all' do
    context 'with valid input' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      context 'when no connected account is present' do
        it 'returns empty list' do
          expect(described_class.new({}).get_all).to eq([])
        end
      end

      context 'when connected account is present' do
        let!(:connected_account_1) { create(:connected_account, created_by_id: user.id, tenant_id: user.tenant_id) }
        let!(:connected_account_2) { create(:connected_account, created_by_id: user.id, tenant_id: user.tenant_id, access_token: 'A23dsjkadsdksakdsahk123') }

        it 'returns connected accounts for tenant' do
          expect(described_class.new({}).get_all).to eq([connected_account_2, connected_account_1])
        end
      end
    end

    context 'with invalid input' do
      context 'when user does not have required permissions' do
        before do
          Thread.current[:auth] = invalid_auth_data
          Thread.current[:token] = invalid_auth_token
          Thread.current[:user] = user
        end

        it 'raises 401 unauthorized error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to view connected account")
          expect { described_class.new({}).get_all }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
        end
      end
    end
  end

  describe '#show' do
    let!(:connected_account) { create(:connected_account, created_by: user) }
    let(:params) { ActionController::Parameters.new({ id: connected_account.id }).permit! }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user
    end

    context 'when user has read all' do
      it 'returns connected account' do
        expect(described_class.new(params).show).to eq(connected_account)
      end
    end

    context 'when id is invalid' do
      let(:params) { ActionController::Parameters.new({ id: connected_account.id + 1 }).permit! }

      it 'raises not found error' do
        expect { described_class.new(params).show }.to raise_error(ExceptionHandler::NotFound, '022006||Account not found')
      end
    end

    context 'when user does not have read all' do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:token] = invalid_auth_token
        Thread.current[:user] = user
      end

      it 'raises authentication error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to view connected account")
        expect { described_class.new(params).show }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#create' do
    before do
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = valid_auth_token
      thread[:user] = user
    end

    context 'with valid input - ' do
      before { allow(Date).to receive(:today).and_return(Date.new(2007, 5, 12))}
      it 'returns connected account' do
        facebook_auth_response = Facebook::Response.new(
          status_code: '200',
          body: access_token_response
        )
        facebook_phone_number_response = Facebook::Response.new(
          status_code: '200',
          body: phone_number_response
        )
        expect_any_instance_of(Facebook::AuthCode).to receive(:exchange).and_return(facebook_auth_response)
        expect_any_instance_of(Facebook::PhoneNumber).to receive(:find).and_return(facebook_phone_number_response)
        expect(WhatsappCredit.find_by(tenant_id: auth_data.tenant_id)).to eq(nil)

        response = described_class.new(valid_params).create
        expect(response.as_json).to eq(ConnectedAccount.first.as_json)

        whatsapp_credit = WhatsappCredit.find_by(tenant_id: auth_data.tenant_id)
        expect(whatsapp_credit.total).to eq(0)
        expect(whatsapp_credit.consumed).to eq(0)
        expect(whatsapp_credit.credits_revised_at).to eq(**********)

      end
    end

    context 'with invalid input' do
      context 'when user does not have required permissions' do
        it 'raises 401 unauthorized error' do
          thread = Thread.current
          thread[:auth] = invalid_auth_data
          thread[:token] = invalid_auth_token
          thread[:user] = user
          expect { described_class.new({}).create }.to raise_error(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
        end
      end

      context 'when some parameters are invalid' do
        it 'returns appropriate exception' do
          expect { described_class.new(valid_params.merge(phone_number_id: nil)).create }.to raise_error(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||Phone number can't be blank")
        end
      end
    end

    context 'when connected account is already present in draft status' do
      it 'considers existing connected account' do
        connected_account = create(:connected_account, phone_number_id: '***************',  tenant_id: auth_data.tenant_id)
        expect(ConnectedAccount).to receive_message_chain(:where, :first).and_return(connected_account)
        facebook_auth_response = Facebook::Response.new(
          status_code: '200',
          body: access_token_response
        )
        facebook_phone_number_response = Facebook::Response.new(
          status_code: '200',
          body: phone_number_response
        )
        expect_any_instance_of(Facebook::AuthCode).to receive(:exchange).and_return(facebook_auth_response)
        expect_any_instance_of(Facebook::PhoneNumber).to receive(:find).and_return(facebook_phone_number_response)
        response = described_class.new(valid_params).create
        expect(response.as_json).to eq(ConnectedAccount.first.as_json)
      end
    end

    context 'when phone number id is already by taken by another tenant' do
      before { create(:connected_account, phone_number_id: '***************') }

      it 'raises error' do
        expect { described_class.new(valid_params).create }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Phone number has already been taken')
      end
    end
  end

  describe '#update' do
    let(:connected_account) { create(:connected_account, waba_number: '+************', tenant_id: auth_data.tenant_id, status: DRAFT, interakt_onboarding_status: DRAFT_STATUS) }
    let(:update_params) { { id: connected_account.id, display_name: 'Demo Account', waba_number: '+************' } }

    context 'with valid input - ' do
      before do
        stub_request(:post, "https://api.interakt.ai/v1/organizations/tp-signup/")
          .with(
            headers: {
              content_type: 'application/json',
              Authorization: 'partner-token'
            },
            body: {
              entry: [
                {
                  changes: [
                    {
                      value: {
                        event: 'PARTNER_ADDED',
                        waba_info: {
                          waba_id: connected_account&.waba_id,
                          solution_id: 'solution-id',
                          phone_number: connected_account&.waba_number
                        }
                      }
                    }
                  ]
                }
              ],
              object: 'tech_partner'
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/onboarding/waba-onboarding-api-success.json'))

        thread = Thread.current
        thread[:auth] = auth_data
        thread[:token] = valid_auth_token
        thread[:user] = user
      end

      it 'returns connected account' do
        response = described_class.new(update_params).update
        expect(response.display_name).to eq('Demo Account')
        expect(response.interakt_onboarding_status).to eq('REQUEST_SENT')
      end
    end

    context 'when no connected account is found' do
      it 'returns appropriate error' do
        thread = Thread.current
        thread[:auth] = auth_data
        thread[:token] = valid_auth_token
        thread[:user] = user
        update_params = {
          id: 1,
          display_name: 'Demo Account',
          status: ACTIVE,
          waba_number: '+************'
        }
        expect { described_class.new(update_params).update }.to raise_error(ExceptionHandler::NotFound, "022006||Connected Account not found")
      end
    end

    context 'when user does not have required permissions' do
      it 'raises 401 unauthorized error' do
        connected_account = create(:connected_account, waba_number: '+************', tenant_id: auth_data.tenant_id)
        thread = Thread.current
        thread[:auth] = invalid_auth_data
        thread[:token] = invalid_auth_token
        thread[:user] = user
        update_params = {
          id: connected_account.id,
          display_name: 'Demo Account',
          status: ACTIVE,
          waba_number: '+************'
        }
        expect { described_class.new(update_params).update }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
      end
    end
  end

  describe '#activate' do
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, interakt_onboarding_status: 'WABA_ONBOARDED', status: INACTIVE, deactivated_at: DateTime.now.utc) }

    context 'when connected account is present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      context 'when connected account is onboarded to interakt' do
        it 'activates connected account' do
          connected_account_obj = described_class.new({ id: connected_account.id }).activate
          expect(connected_account_obj.status).to eq('active')
          expect(connected_account_obj.deactivated_at).to be_nil
        end
      end

      context 'when interakt onboarding webhook missed' do
        before {connected_account.update(interakt_onboarding_status: 'REQUEST_SENT')  }

        it 'raises error' do
          expect { described_class.new({ id: (connected_account.id) }).activate }
            .to raise_error(ExceptionHandler::InvalidDataError, '022014||Failed to register whatsapp business. Please contact support.')
        end
      end

      context 'when interakt onboarding failed' do
        before {connected_account.update(interakt_onboarding_status: 'WABA_ONBOARDING_FAILED')  }

        it 'raises error' do
          expect { described_class.new({ id: (connected_account.id) }).activate }
            .to raise_error(ExceptionHandler::InvalidDataError, '022014||Failed to register whatsapp business. Please contact support.')
        end
      end
    end

    context 'when connected account is not present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect { described_class.new({ id: (connected_account.id + 1) }).activate }
          .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when user does not have permission' do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:token] = invalid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect { described_class.new({ id: (connected_account.id) }).activate }
          .to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#deactivate' do
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, status: ACTIVE) }

    context 'when connected account is present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      context 'when connected account is draft or active' do
        [DRAFT, PENDING, ACTIVE].each do |existing_status|
          context "when existing status is #{existing_status}" do
            before { connected_account.update_column(:status, existing_status) }

            it 'deactivates connected account' do
              connected_account_obj = described_class.new({ id: connected_account.id }).deactivate
              expect(connected_account_obj.status).to eq('inactive')
              expect(connected_account_obj.deactivated_at.present?).to be_truthy
            end
          end
        end
      end
    end

    context 'when connected account is not present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect { described_class.new({ id: (connected_account.id + 1) }).deactivate }
          .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end
  end

  describe '#send_verification_code' do
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, status: ACTIVE) }

    context 'when connected account is present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      context 'when code method and language is present' do
        it 'sends request code to phone number' do
          expect_any_instance_of(Facebook::PhoneNumber).to receive(:request_code).with('SMS', 'en').and_return(Facebook::Response.new(status_code: '200', body: { success: true }))
          described_class.new({ id: connected_account.id, code_method: 'SMS', language: 'en' }).send_verification_code
        end
      end

      context 'when code method is present' do
        it 'sends request code to phone number with default language en' do
          expect_any_instance_of(Facebook::PhoneNumber).to receive(:request_code).with('SMS', 'en').and_return(Facebook::Response.new(status_code: '200', body: { success: true }))
          described_class.new({ id: connected_account.id, code_method: 'SMS' }).send_verification_code
        end
      end
    end

    context 'when connected account is not present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect { described_class.new({ id: (connected_account.id + 1), code_method: 'SMS', language: 'en' }).send_verification_code }
          .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when user does not have permission' do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:token] = invalid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to send verification code for connected account")
        expect { described_class.new({ id: connected_account.id, code_method: 'SMS', language: 'en' }).send_verification_code }
          .to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end

    context 'when invalid or missing code method' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user
      end

      context 'when code method is invalid' do
        it 'raises error' do
          expect { described_class.new({ id: connected_account.id, code_method: 'sms' }).send_verification_code }
            .to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid code method')
        end
      end

      context 'when code method is missing' do
        it 'raises error' do
          expect { described_class.new({ id: connected_account.id }).send_verification_code }
            .to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid code method')
        end
      end
    end
  end

  describe '#verify_code' do
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, is_verified: false) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user
    end

    context 'when connected account is present' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: 123456
            }
          )
          .to_return(status: 200, body: { success: true }.to_json)
      end

      it 'verifies code and updates connected account to verified' do
        described_class.new({ id: connected_account.id, otp_code: 123456 }).verify_code
        expect(connected_account.reload.is_verified).to be_truthy
      end
    end

    context 'when connected account is not present' do
      it 'raises error' do
        expect { described_class.new({ id: (connected_account.id + 1), otp_code: 123456 }).verify_code }
          .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when error while verifying code' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: 123456
            }
          )
          .to_return(status: 400, body: file_fixture('facebook/phone_number/verify-code-error.json').read)
      end

      it 'raises error' do
        expect { described_class.new({ id: connected_account.id, otp_code: 123456 }).verify_code }
          .to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Verify code error. Code couldn\'t be verified, You have already verified ownership of this phone number.')
        expect(connected_account.reload.is_verified).to be_falsey
      end
    end

    context 'when user does not have permission' do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:token] = invalid_auth_token
        Thread.current[:user] = user
      end

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to verify code for connected account")
        expect { described_class.new({ id: connected_account.id, otp_code: 123456 }).verify_code }
          .to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#deactivate_all_accounts' do
    let(:params) { JSON.parse(file_fixture('usage-limit-changed-payload.json').read) }

    context 'when active draft connected accounts are present' do
      before do
        create_list(:connected_account, 2, tenant_id: params['tenantId'])
        create_list(:connected_account, 2)
        create_list(:connected_account, 2, tenant_id: params['tenantId'], status: ACTIVE)
        create_list(:connected_account, 2, status: ACTIVE)
        create_list(:connected_account, 2, tenant_id: params['tenantId'], status: INACTIVE, deactivated_at: DateTime.now.utc)
        create_list(:connected_account, 2, status: INACTIVE)
      end

      it 'deactivates all accounts' do
        expect { described_class.new(params).deactivate_all_accounts }
          .to change(ConnectedAccount.where(tenant_id: params['tenantId'], status: INACTIVE), :count).by(4)
        expect(ConnectedAccount.where(tenant_id: params['tenantId']).pluck(:deactivated_at).compact.count).to eq(6)
      end
    end
  end

  describe '#destroy' do
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:different_tenant_connected_account) { create(:connected_account) }

    context 'when connected account exists' do
      it 'deletes the connected account successfully' do
        expect {
          described_class.new({ id: connected_account.id }).destroy
        }.to change(ConnectedAccount, :count).by(-1)

        expect(ConnectedAccount.find_by(id: connected_account.id)).to be_nil
      end

      it 'does not affect other connected accounts' do
        other_connected_account

        expect {
          described_class.new({ id: connected_account.id }).destroy
        }.to change(ConnectedAccount, :count).by(-1)

        expect(ConnectedAccount.find_by(id: other_connected_account.id)).to be_present
      end
    end

    context 'when connected account has associated records' do
      let(:another_user) { create(:user, tenant_id: user.tenant_id) }
      let!(:agent_user) { create(:agent_user, connected_account: connected_account, user: user, entity_type: LOOKUP_LEAD) }

      it 'raises error' do
        expect {
          described_class.new({ id: connected_account.id }).destroy
        }.to raise_error(ActiveRecord::InvalidForeignKey)

        expect(ConnectedAccount.find_by(id: connected_account.id)).not_to be_nil
      end
    end

    context 'when connected account does not exist' do
      it 'returns not found error' do
        expect { described_class.new({ id: 999999 }).destroy }
          .to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end
  end

  describe '#lookup' do
    let!(:connected_accounts_matching_query) { create_list(:connected_account, 2, created_by: user, display_name: 'Some Query Text', status: ACTIVE, last_message_received_at: Time.now.to_i) }
    let!(:connected_accounts_not_matching_query) { create_list(:connected_account, 2, created_by: user, status: ACTIVE) }
    let!(:inactive_connected_accounts) { create_list(:connected_account, 2, created_by: user, status: INACTIVE) }
    let!(:unverified_connected_accounts) { create_list(:connected_account, 2, created_by: user, status: ACTIVE, is_verified: false) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user
    end

    context 'when query parameter is present' do
      before do
        (connected_accounts_matching_query + connected_accounts_not_matching_query + inactive_connected_accounts + unverified_connected_accounts).each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end
      end

      it 'returns active connected account matching query in display name' do
        expect(described_class.new(ActionController::Parameters.new({ q: 'query' }).permit!).lookup).to match_array(connected_accounts_matching_query)
      end
    end

    context 'when query parameter is absent' do
      before do
        (connected_accounts_matching_query + connected_accounts_not_matching_query).each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end
      end

      it 'returns all active connected accounts' do
        expect(described_class.new(ActionController::Parameters.new({}).permit!).lookup).to match_array(connected_accounts_matching_query + connected_accounts_not_matching_query)
      end
    end

    context 'when entity type is present' do
      before do
        connected_accounts_matching_query.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end
        connected_accounts_not_matching_query.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id, entity_type: 'contact')
        end
      end

      it 'returns all active connected accounts' do
        expect(described_class.new(ActionController::Parameters.new({ entity_type: 'lead' }).permit!).lookup).to match_array(connected_accounts_matching_query)
      end
    end

    context 'when user is not an agent in any of the accounts' do
      it 'returns blank' do
        expect(described_class.new(ActionController::Parameters.new({}).permit!).lookup).to match_array([])
      end
    end

    context 'when view is billing' do
      before do
        connected_accounts_matching_query.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end
        inactive_connected_accounts.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id, entity_type: 'contact')
        end
      end

      it 'returns all active, inactive connected accounts' do
        expect(described_class.new(ActionController::Parameters.new({ view: 'billing' }).permit!).lookup).to match_array(connected_accounts_matching_query + inactive_connected_accounts)
      end
    end

    context 'when incoming messages are received on the multiple connected  accounts' do
      before do
        connected_accounts_matching_query.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end

        inactive_connected_accounts.each do |connected_account|
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id, entity_type: 'contact')
        end
      end

      it 'returns sorted connected accounts based on last_message_received_at' do
        expect(described_class.new(ActionController::Parameters.new({ view: 'billing'}).permit!).lookup).to match_array(connected_accounts_matching_query + inactive_connected_accounts)
      end
    end
  end

  describe '#entiy_phones' do
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, status: 'active') }
    let(:second_connected_account) { create(:connected_account, tenant_id: second_user_auth_data.tenant_id, status: 'active') }
    let(:third_connected_account) { create(:connected_account, tenant_id: third_user_auth_data.tenant_id, status: 'active') }
    let(:first_conversation){ create(:conversation, phone_number: '+91**********', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:second_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:sgp_conversation){ create(:conversation, phone_number: '+65********', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:deleted_phone_number_conversation){ create(:conversation, phone_number: '+91**********', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:deleted_phone_number_conversation_on_different_account){ create(:conversation, phone_number: '+91**********', tenant_id: user.tenant_id, connected_account_id:  second_connected_account.id, owner_id: user.id) }
    let(:third_conversation){ create(:conversation, phone_number: '+************', tenant_id: third_user.tenant_id, connected_account_id:  third_connected_account.id, owner_id: second_user.id) }
    let(:fourth_conversation){ create(:conversation, phone_number: '+91**********', tenant_id: third_user.tenant_id, connected_account_id:  third_connected_account.id, owner_id: second_user.id) }
    let(:token_without_sms) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data_without_sms) { User::TokenParser.parse(token_without_sms.token) }
    let(:sample_response) {
      {
        "accessByOwners": {},
        "accessByRecords": {
          "246547": {
            "read": true,
            "write": false,
            "update": false,
            "delete": false,
            "email": false,
            "call": true,
            "sms": true,
            "task": false,
            "note": false,
            "meeting": false,
            "readAll": false,
            "updateAll": false,
            "deleteAll": false,
            "quotation": false
          }
        }
      }
    }

    let(:first_user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }
    let(:third_user_share_rule) { create(:share_rule, share_rule_id: 3655, name: 'Share rule name updated', tenant_id: third_user.tenant_id, from_id: 4010, to_id: third_user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when connected account id is invalid' do
      it 'raises not found error' do
        expect { described_class.new(ActionController::Parameters.new({ id: -1, entity_type: 'lead' }).permit!).entity_phones }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when entity is not supported' do
      it 'raises error' do
        expect { described_class.new(ActionController::Parameters.new({ entity_type: 'INVALID' }).permit!).entity_phones }.to raise_error(
          ExceptionHandler::InvalidDataError, "022003||Unsupported entity"
        )
      end
    end

    context 'when requested with proper data' do
      before do
        allow(AssociateConversationWithEntitiesJob).to receive(:perform_later)
      end

      context 'when user is owner of the given entity and has conversation permission on entity' do
        before(:each) do
          Thread.current[:auth] = second_user_auth_data
          Thread.current[:user] = second_user
          Thread.current[:token] = second_user_auth_token.token

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ second_user_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
        end

        it 'returns phone numbers with session and conversation details' do
          phones = described_class.new(ActionController::Parameters.new({ id: second_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end

        it 'creates new conversation, lookup, conversation_lookup' do
          expect{
            described_class.new(ActionController::Parameters.new({ id: second_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          }.to change(Conversation, :count).by(3)
          .and change(ConversationLookUp, :count).by(3)
          .and change(LookUp, :count).by(3)

          expect(LookUp.last.name).to eq('lead first name')
        end
      end

      context 'when user is conversation user' do
        before(:each) do
          Thread.current[:auth] = third_user_auth_data
          Thread.current[:user] = third_user
          Thread.current[:token] = third_user_auth_token.token

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ third_user_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          first_conversation
          second_conversation
          third_user_share_rule
        end

        it 'returns phone numbers with session and conversation details' do
          phones = described_class.new(ActionController::Parameters.new({ id: third_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end
      end

      context 'when entity is shared to user with conversation read permission' do
        before(:each) do
          Thread.current[:auth] = third_user_auth_data
          Thread.current[:user] = third_user
          Thread.current[:token] = third_user_auth_token.token

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ third_user_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          second_user
          third_conversation
          fourth_conversation
          third_user_share_rule
        end

        it 'returns phone numbers with session and conversation details' do
          phones = described_class.new(ActionController::Parameters.new({ id: third_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end
      end

      context 'when all entities owned by particular user are shared with this current user' do
        before(:each) do
          Thread.current[:auth] = third_user_auth_data
          Thread.current[:user] = third_user
          Thread.current[:token] = third_user_auth_token.token

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ third_user_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          second_user
          third_conversation
          fourth_conversation
          third_user_share_rule.update(share_all_records: true, entity_id: nil)
        end

        it 'returns phone numbers with session and conversation details' do
          phones = described_class.new(ActionController::Parameters.new({ id: third_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end
      end

      context 'when user does not have required permission' do
        before(:each) do
          Thread.current[:auth] = third_user_auth_data
          Thread.current[:user] = third_user
          Thread.current[:token] = third_user_auth_token.token

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ third_user_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )
          second_user
          third_conversation
          fourth_conversation
        end

        it 'raises error' do
          expect { described_class.new(ActionController::Parameters.new({ id: third_connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
        end
      end

      before(:each) do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        allow(GenerateToken).to receive(:call).and_return(token_without_pid)
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ valid_auth_token }"
          }
        ).to_return(
          status: 200,
          body: [].to_json,
          headers: {}
        )

        first_user_share_rule
      end

      context 'when user has read all conversation permission' do
        it 'returns phone numbers with session and conversation details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end
      end

      context 'when last_message_received_at is not present on the conversation' do
        it 'returns phone numbers with session inactive' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph[:session] }).to eq(['inactive', 'inactive', 'inactive'])
          expect(phones.map { |ph| ph[:lastContactedAt] }).to eq([nil, nil, nil])
        end
      end

      context 'when last_message_received_at is not present on the conversation' do
        before do
          first_conversation.last_message_received_at = Time.now - 29.hours
          first_conversation.save
          second_conversation.last_message_received_at = Time.now - 2.hours
          second_conversation.save
        end

        it 'returns session details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph[:session] }).to match_array(['active', 'inactive', 'inactive'])
        end
      end

      context 'when masking is not enabled on phone number field of the given entity' do
        it 'returns unmasked phone numbers with session details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['**********', '**********', '********'])
        end
      end

      context 'when masking is enabled on phone number field of the given entity' do
        before(:each) do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ valid_auth_token }"
            }
          ).to_return(
            status: 200,
            body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )
        end

        it 'returns masked phone numbers with session details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones
          expect(phones.map { |ph| ph['value'] }).to match_array(['****499', '****499', '****888'])
        end
      end

      context 'when conversation with given phone number is already present' do
        before(:each) do
          first_conversation
          second_conversation
          sgp_conversation
        end

        it 'returns existing conversation data with phone number details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones.map { |ph| ph[:conversationId] }).to match_array([first_conversation.id, second_conversation.id, sgp_conversation.id])
        end
      end

      context 'when conversation with given phone number is not present' do
        it 'returns phone number details with newly conversation created details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones.first).to have_key(:conversationId)
          expect(Conversation.count).to eq(3)
        end
      end

      context 'when phone number is not present on entity and user has conversation permission' do
        before(:each) do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          entity_details = JSON.parse(file_fixture('get_lead_response.json').read)
          entity_details['content'][0]['phoneNumbers'] = []

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: entity_details.to_json, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ valid_auth_token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          first_user_share_rule
        end

        it 'returns empty array' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones).to eq([])
        end
      end

      context 'when conversation is present for entity for a deleted phone number' do
        before(:each) do
          first_conversation
          second_conversation
          sgp_conversation
          deleted_phone_number_conversation
          look_up = create(:look_up, entity_id: 34343, entity_type: 'lead', tenant_id: connected_account.tenant_id, phone_number: deleted_phone_number_conversation.phone_number)
          look_up.conversations << deleted_phone_number_conversation
        end

        it 'returns existing conversation data with phone number details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones.map { |ph| ph[:conversationId] }).to match_array([first_conversation.id, second_conversation.id, sgp_conversation.id, deleted_phone_number_conversation.id])

          disabled_conversation = phones.find{ |ph| ph[:conversationId] == deleted_phone_number_conversation.id }
          expect(disabled_conversation).to eq({
            "dialCode" => "+91",
            "disabled" => true,
            "value" => "**********",
            :conversationId => deleted_phone_number_conversation.id,
            :lastContactedAt => nil,
            :session => "inactive"
          })
        end

        it 'returns session as inactive for deleted phone number conversation' do
          deleted_phone_number_conversation.last_message_received_at = Time.now - 2.hours
          deleted_phone_number_conversation.save

          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          disabled_conversation = phones.find{ |ph| ph[:conversationId] == deleted_phone_number_conversation.id }
          expect(disabled_conversation[:session]).to eq("inactive")
        end
      end

      context 'when lookup phone number is stored without + sign' do
        before(:each) do
          first_conversation
          second_conversation
          sgp_conversation
          look_up = create(:look_up, entity_id: 34343, entity_type: 'lead', tenant_id: connected_account.tenant_id, phone_number: '************')
          look_up.conversations << second_conversation
        end

        it 'returns conversation data with phone number detail without duplicate phone numbers' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones.map { |ph| ph[:conversationId] }).to match_array([first_conversation.id, second_conversation.id, sgp_conversation.id])
          expect(phones.map { |phone| phone['value'] }).to match_array(['**********', '********', '**********'])
        end
      end
      
      context 'when conversation is present for entity for a deleted phone number but on different connected account' do
        before(:each) do
          first_conversation
          second_conversation
          sgp_conversation
          deleted_phone_number_conversation_on_different_account
          look_up = create(:look_up, entity_id: 34343, entity_type: 'lead', tenant_id: connected_account.tenant_id, phone_number: deleted_phone_number_conversation_on_different_account.phone_number)
          look_up.conversations << deleted_phone_number_conversation_on_different_account
        end

        it 'returns existing conversation data with phone number details' do
          phones = described_class.new(ActionController::Parameters.new({ id: connected_account.id, entity_id: 34343, entity_type: 'lead' }).permit!).entity_phones

          expect(phones.map { |ph| ph[:conversationId] }).to match_array([first_conversation.id, second_conversation.id, sgp_conversation.id])
        end
      end
    end
  end
end
