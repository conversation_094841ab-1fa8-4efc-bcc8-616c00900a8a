# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ValidateAndBuildQueryFromJsonRule do
  describe '#build_query' do
    let(:scope) { WhatsappTemplate.where(tenant_id: 1) }
    let(:rules) { [rule] }

    context 'valid rules' do
      before {
        allow(Time).to receive(:now).and_return Time.new(2024, 8, 15, 3, 0, 0)
        @built_query = described_class.new(scope, rules).build_query
      }

      context 'string fields' do
        context 'when equal operator' do
          let(:rule) { { field: 'status', type: 'string', operator: 'equal', value: 'ACTIVE' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"status\" ILIKE 'ACTIVE'")
          end
        end

        context 'when not equal operator' do
          let(:rule) { { field: 'category', type: 'string', operator: 'not_equal', value: 'AUTHENTICATION' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT (\"whatsapp_templates\".\"category\" ILIKE 'AUTHENTICATION')")
          end
        end

        context 'when contains operator' do
          let(:rule) { { field: 'language', type: 'string', operator: 'contains', value: 'en' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"language\" ILIKE '%en%'")
          end
        end

        context 'when not contains operator' do
          let(:rule) { { field: 'entityType', type: 'string', operator: 'not_contains', value: 'lead' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT (\"whatsapp_templates\".\"entity_type\" ILIKE '%lead%')")
          end
        end

        context 'when begins with operator' do
          let(:rule) { { field: 'status', type: 'string', operator: 'begins_with', value: 'ACT' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"status\" ILIKE 'ACT%'")
          end
        end

        context 'when in operator' do
          let(:rule) { { field: 'status', type: 'string', operator: 'in', value: 'ACTIVE,PENDING' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND (\"whatsapp_templates\".\"status\" ILIKE 'ACTIVE' OR \"whatsapp_templates\".\"status\" ILIKE 'PENDING')")
          end
        end

        context 'when not in operator' do
          let(:rule) { { field: 'category', type: 'string', operator: 'not_in', value: 'MARKETING,UTILITY' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT ((\"whatsapp_templates\".\"category\" ILIKE 'MARKETING' OR \"whatsapp_templates\".\"category\" ILIKE 'UTILITY'))")
          end
        end

        context 'when is null operator' do
          let(:rule) { { field: 'category', type: 'string', operator: 'is_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND (\"whatsapp_templates\".\"category\" = '' OR \"whatsapp_templates\".\"category\" IS NULL)")
          end
        end

        context 'when is not null operator' do
          let(:rule) { { field: 'category', type: 'string', operator: 'is_not_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT ((\"whatsapp_templates\".\"category\" = '' OR \"whatsapp_templates\".\"category\" IS NULL))")
          end
        end

        context 'when is empty operator' do
          let(:rule) { { field: 'status', type: 'string', operator: 'is_empty', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND (\"whatsapp_templates\".\"status\" = '' OR \"whatsapp_templates\".\"status\" IS NULL)")
          end
        end

        context 'when is not empty operator' do
          let(:rule) { { field: 'status', type: 'string', operator: 'is_not_empty', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT ((\"whatsapp_templates\".\"status\" = '' OR \"whatsapp_templates\".\"status\" IS NULL))")
          end
        end
      end

      context 'when date fields' do
        context 'when equal operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'equal', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" = '2024-05-06 11:11:00'")
          end
        end

        context 'when not equal operator' do
          let(:rule) { { field: 'updatedAt', type: 'date', operator: 'not_equal', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"updated_at\" != '2024-05-06 11:11:00'")
          end
        end

        context 'when greater operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'greater', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" > '2024-05-06 11:11:00'")
          end
        end

        context 'when less operator' do
          let(:rule) { { field: 'updatedAt', type: 'date', operator: 'less', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"updated_at\" < '2024-05-06 11:11:00'")
          end
        end

        context 'when greater or equal operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'greater_or_equal', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" >= '2024-05-06 11:11:00'")
          end
        end

        context 'when less or equal operator' do
          let(:rule) { { field: 'updatedAt', type: 'date', operator: 'less_or_equal', value: '2024-05-06T11:11:00.000Z' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"updated_at\" <= '2024-05-06 11:11:00'")
          end
        end

        context 'when between operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'between', value: ['2024-05-06T11:11:00.000Z', '2024-05-06T12:11:00.000Z'] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-05-06 11:11:00' AND '2024-05-06 12:11:00'")
          end
        end

        context 'when not between operator' do
          let(:rule) { { field: 'updatedAt', type: 'date', operator: 'not_between', value: ['2024-05-06T11:11:00.000Z', '2024-05-06T12:11:00.000Z'] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND (\"whatsapp_templates\".\"updated_at\" < '2024-05-06 11:11:00' OR \"whatsapp_templates\".\"updated_at\" > '2024-05-06 12:11:00')")
          end
        end

        context 'when is null operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'is_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" IS NULL")
          end
        end

        context 'when is not null operator' do
          let(:rule) { { field: 'updatedAt', type: 'date', operator: 'is_not_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"updated_at\" IS NOT NULL")
          end
        end

        context 'when today operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'today', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-14 18:30:00' AND '2024-08-15 18:29:59.999999'")
          end
        end

        context 'when yesterday operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'yesterday', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-13 18:30:00' AND '2024-08-14 18:29:59.999999'")
          end
        end
        
        context 'when tomorrow operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'tomorrow', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-15 18:30:00' AND '2024-08-16 18:29:59.999999'")
          end
        end

        context 'when last_seven_days operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_seven_days', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-07 18:30:00' AND '2024-08-14 18:29:59.999999'")
          end
        end

        context 'when next_seven_days operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_seven_days', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-15 18:30:00' AND '2024-08-22 18:29:59.999999'")
          end
        end

        context 'when last_thirty_days operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_thirty_days', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-07-15 18:30:00' AND '2024-08-14 18:29:59.999999'")
          end
        end

        context 'when next_thirty_days operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_thirty_days', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-15 18:30:00' AND '2024-09-14 18:29:59.999999'")
          end
        end

        context 'when current_week operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'current_week', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-11 18:30:00' AND '2024-08-18 18:29:59.999999'")
          end
        end

        context 'when last_week operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_week', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-04 18:30:00' AND '2024-08-11 18:29:59.999999'")
          end
        end

        context 'when next_week operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_week', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-18 18:30:00' AND '2024-08-25 18:29:59.999999'")
          end
        end

        context 'when current_month operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'current_month', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-07-31 18:30:00' AND '2024-08-31 18:29:59.999999'")
          end
        end

        context 'when last_month operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_month', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-06-30 18:30:00' AND '2024-07-31 18:29:59.999999'")
          end
        end

        context 'when next_month operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_month', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-31 18:30:00' AND '2024-09-30 18:29:59.999999'")
          end
        end

        context 'when current_quarter operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'current_quarter', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-06-30 18:30:00' AND '2024-09-30 18:29:59.999999'")
          end
        end

        context 'when last_quarter operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_quarter', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-03-31 18:30:00' AND '2024-06-30 18:29:59.999999'")
          end
        end

        context 'when next_quarter operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_quarter', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-09-30 18:30:00' AND '2024-12-31 18:29:59.999999'")
          end
        end

        context 'when current_year operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'current_year', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2023-12-31 18:30:00' AND '2024-12-31 18:29:59.999999'")
          end
        end

        context 'when last_year operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_year', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2022-12-31 18:30:00' AND '2023-12-31 18:29:59.999999'")
          end
        end

        context 'when next_year operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_year', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-12-31 18:30:00' AND '2025-12-31 18:29:59.999999'")
          end
        end

        context 'when week_to_date operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'week_to_date', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-11 18:30:00' AND '2024-08-15 18:29:59.999999'")
          end
        end

        context 'when month_to_date operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'month_to_date', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-07-31 18:30:00' AND '2024-08-15 18:29:59.999999'")
          end
        end

        context 'when quarter_to_date operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'quarter_to_date', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-06-30 18:30:00' AND '2024-08-15 18:29:59.999999'")
          end
        end

        context 'when year_to_date operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'year_to_date', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2023-12-31 18:30:00' AND '2024-08-15 18:29:59.999999'")
          end
        end

        context 'when before_current_date_and_time operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'before_current_date_and_time', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" < '2024-08-14 21:30:00'")
          end
        end

        context 'when after_current_date_and_time operator' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'after_current_date_and_time', value: nil, timeZone: 'Asia/Calcutta' } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" > '2024-08-14 21:30:00'")
          end
        end

        context 'when last_n_days operator with valid N' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_n_days', value: '5', timeZone: 'Asia/Calcutta' } }

          it 'returns query with correct date range' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-09 18:30:00' AND '2024-08-14 18:29:59.999999'")
          end
        end

        context 'when next_n_days operator with valid N' do
          let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_n_days', value: '5', timeZone: 'Asia/Calcutta' } }

          it 'returns query with correct date range' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_at\" BETWEEN '2024-08-15 18:30:00' AND '2024-08-20 18:29:59.999999'")
          end
        end
      end

      context 'when long fields' do
        context 'when equal operator' do
          let(:rule) { { field: 'id', type: 'long', operator: 'equal', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" = 1")
          end
        end

        context 'when not equal operator' do
          let(:rule) { { field: 'createdBy', type: 'long', operator: 'not_equal', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"created_by_id\" != 1")
          end
        end

        context 'when greater operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'greater', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" > 1")
          end
        end

        context 'when less operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'less', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" < 1")
          end
        end

        context 'when greater or equal operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'greater_or_equal', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" >= 1")
          end
        end

        context 'when less or equal operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'less_or_equal', value: 1 } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" <= 1")
          end
        end

        context 'when between operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'between', value: [1, 10] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" >= 1 AND \"whatsapp_templates\".\"id\" <= 10")
          end
        end

        context 'when not between operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'not_between', value: [1, 10] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND NOT (\"whatsapp_templates\".\"id\" >= 1 AND \"whatsapp_templates\".\"id\" <= 10)")
          end
        end

        context 'when in operator' do
          let(:rule) { { field: 'updatedBy', type: 'long', operator: 'in', value: [1, 10] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"updated_by_id\" IN (1, 10)")
          end
        end

        context 'when not in operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'not_in', value: [1, 10] } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" NOT IN (1, 10)")
          end
        end

        context 'when is null operator' do
          let(:rule) { { field: 'id', type: 'long', operator: 'is_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" IS NULL")
          end
        end

        context 'when not is not null operator' do
          let(:rule) { { field: 'id', type: 'double', operator: 'is_not_null', value: nil } }

          it 'returns query' do
            expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"id\" IS NOT NULL")
          end
        end
      end

      context 'when multi_field' do
        let(:rule) { { field: 'multi_field', type: 'multi_field', operator: 'multi_field', value: 'some string' } }

        it 'returns string contains query' do
          expect(@built_query.to_sql).to eq("SELECT \"whatsapp_templates\".* FROM \"whatsapp_templates\" WHERE \"whatsapp_templates\".\"tenant_id\" = 1 AND \"whatsapp_templates\".\"name\" ILIKE '%some string%'")
        end
      end
    end

    context 'when last_n_days operator with invalid N=0' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_n_days', value: '0', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when last_n_days operator with invalid N=-1' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_n_days', value: '-1', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when last_n_days operator with non-integer N' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_n_days', value: 'abc', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when last_n_days operator with greater than 364' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'last_n_days', value: '366', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when next_n_days operator with greater than 364' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_n_days', value: '366', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when next_n_days operator with invalid N=0' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_n_days', value: '0', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when next_n_days operator with invalid N=-1' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_n_days', value: '-1', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when next_n_days operator with non-integer N' do
      let(:rule) { { field: 'createdAt', type: 'date', operator: 'next_n_days', value: 'abc', timeZone: 'Asia/Calcutta' } }

      it 'raises InvalidDataError' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Please enter a valid number of days')
      end
    end

    context 'when invalid field' do
      let(:rule) { { field: 'tenantId', type: 'long', operator: 'equal', value: 1 } }

      it 'raises error' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid filter params')
      end
    end

    context 'when invalid type' do
      let(:rule) { { field: 'status', type: 'boolean', operator: 'equal', value: true } }

      it 'raises error' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid filter params')
      end
    end

    context 'when invalid operator' do
      let(:rule) { { field: 'status', type: 'long', operator: 'between', value: true } }

      it 'raises error' do
        expect { described_class.new(scope, rules).build_query }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid filter params')
      end
    end
  end
end
