# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchWhatsappTemplatesFromMetaService, type: :service do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account) }
  let(:params) { { connected_account_id: connected_account.id, entity_type: 'lead' } }
  let(:entity_type) { 'lead' }

  before do
    Thread.current[:user] = user
    allow(FetchWhatsappTemplatesFromMetaJob).to receive(:perform_later).and_return(double('Job', job_id: '12345'))
  end

  describe '#fetch' do
    it 'enqueues FetchWhatsappTemplatesFromMetaJob with correct arguments' do
      expect(FetchWhatsappTemplatesFromMetaJob).to receive(:perform_later).with(connected_account.id, user.tenant_id, user.id, entity_type).once
      
      service = described_class.new(params)
      service.fetch
    end
  end
end
