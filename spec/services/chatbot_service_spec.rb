# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChatbotService do
  let(:message) { 'Hello, I need help' }
  let(:entity_details) do
    [
      { id: 123, entityType: 'lead' },
      { id: 456, entityType: 'contact' }
    ]
  end
  let(:connected_account) { create(:connected_account, name: 'Test Account') }
  let(:admin_token) { nil }
  let(:conversation_id) { 'conv-123' }
  let(:service) { described_class.new(message, entity_details, connected_account, admin_token, conversation_id) }

  describe '#initiate_conversation' do
    let(:expected_payload) do
      {
        message: message,
        entityDetails: entity_details,
        connectedAccount: {
          id: connected_account.id,
          name: connected_account.name
        },
        messageConversationId: conversation_id
      }
    end

    let(:expected_headers) do
      {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
      }
    end

    context 'when the request is successful' do
      # let(:successful_response_body) do
      #   {
      #     'chatbotConversationId' => 'conv-uuid-123',
      #     'message' => 'Hello! How can I help you?',
      #     'firstQuestion' => 'What is your name?'
      #   }.to_json
      # end

      let(:successful_response_body) do
        {}.to_json
      end

      before do
        stub_request(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .with(
            body: expected_payload.to_json,
            headers: expected_headers
          )
          .to_return(
            status: 200,
            body: successful_response_body,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'makes a POST request to the chatbot service with correct payload' do
        service.initiate_conversation

        expect(WebMock).to have_requested(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .with(
            body: expected_payload.to_json,
            headers: expected_headers
          )
      end

      it 'returns success response with chatbot data' do
        result = service.initiate_conversation
        expect(result).to eq({
          success: true,
          data: {}
        })
      end

      it 'logs successful initiation' do
        expect(Rails.logger).to receive(:info).with(/Chatbot conversation initiated successfully/)
        service.initiate_conversation
      end
    end

    context 'when admin token is provided' do
      let(:admin_token) { 'test-admin-token-123' }
      let(:expected_headers_with_auth) do
        {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json',
          'Authorization' => 'Bearer test-admin-token-123'
        }
      end

      before do
        stub_request(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .with(
            body: expected_payload.to_json,
            headers: expected_headers_with_auth
          )
          .to_return(
            status: 200,
            body: {}.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'makes a POST request with authorization header' do
        service.initiate_conversation

        expect(WebMock).to have_requested(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .with(
            body: expected_payload.to_json,
            headers: expected_headers_with_auth
          )
      end

      it 'returns success response with chatbot data' do
        result = service.initiate_conversation

        expect(result).to eq({
          success: true,
          data: {}
        })
      end
    end

    context 'when the request returns non-200 status' do
      before do
        stub_request(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .to_return(status: 422, body: 'Unprocessable Entity')
      end

      it 'returns failure response' do
        result = service.initiate_conversation

        expect(result).to eq({
          success: false,
          error: 'Service error: 422 Unprocessable Entity'
        })
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Chatbot service error/)
        service.initiate_conversation
      end
    end

    context 'when the request raises BadRequest error' do
      before do
        stub_request(:post, "#{SERVICE_WHATSAPP_CHATBOT}/v1/chatbot/conversations")
          .to_return(status: 400, body: 'Bad Request')
      end

      it 'returns failure response' do
        result = service.initiate_conversation

        expect(result).to eq({
          success: false,
          error: 'Bad request: 400 Bad Request'
        })
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Chatbot service bad request/)
        service.initiate_conversation
      end
    end
  end
end
