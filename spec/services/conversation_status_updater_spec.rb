# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConversationStatusUpdater, type: :service do
  describe '#call' do
    let(:connected_account) { create(:connected_account) }
    let(:conversation) { create(:conversation, connected_account_id: connected_account.id) }
    let(:sub_conversation) { create(:sub_conversation, conversation: conversation, status: NEW) }

    before do
      allow(conversation).to receive(:ongoing_sub_conversation).and_return(sub_conversation)
    end

    context 'when no messages exist for the sub_conversation' do
      it 'does not update any statuses' do
        expect(sub_conversation).not_to receive(:update)
        expect(conversation).not_to receive(:update)

        described_class.new(conversation).call
      end
    end

    context 'when only one incoming message exists' do
      let!(:message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :incoming) }

      it 'sets sub_conversation status to NEW and main conversation status to NEW' do
        described_class.new(conversation).call

        expect(sub_conversation.reload.status).to eq(NEW)
        expect(conversation.reload.status).to eq(NEW)
      end
    end

    context 'when multiple incoming messages exist' do
      let!(:message1) { create(:message, sub_conversation_id: sub_conversation.id, direction: :incoming, connected_account_id: conversation.connected_account_id) }
      let!(:message2) { create(:message, sub_conversation_id: sub_conversation.id, direction: :incoming, connected_account_id: conversation.connected_account_id) }

      it 'sets sub_conversation status to IN_PROGRESS and main conversation status to NEW' do
        described_class.new(conversation).call

        expect(sub_conversation.reload.status).to eq(IN_PROGRESS)
        expect(conversation.reload.status).to eq(NEW)
      end
    end

    context 'when only one outgoing message exists' do
      let!(:message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :outgoing) }

      it 'sets sub_conversation status to NEW and main conversation status to NEW' do
        described_class.new(conversation).call

        expect(sub_conversation.reload.status).to eq(NEW)
        expect(conversation.reload.status).to eq(NEW)
      end
    end

    context 'when multiple outgoing messages exist' do
      let!(:message1) { create(:message, sub_conversation_id: sub_conversation.id, direction: :outgoing, connected_account_id: conversation.connected_account_id) }
      let!(:message2) { create(:message, sub_conversation_id: sub_conversation.id, direction: :outgoing, connected_account_id: conversation.connected_account_id) }

      it 'sets sub_conversation status to IN_PROGRESS and main conversation status to NEW' do
        described_class.new(conversation).call

        expect(sub_conversation.reload.status).to eq(IN_PROGRESS)
        expect(conversation.reload.status).to eq(NEW)
      end
    end

    context 'when both incoming and outgoing messages exist' do
      let!(:incoming_message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :incoming, connected_account_id: conversation.connected_account_id) }
      let!(:outgoing_message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :outgoing, connected_account_id: conversation.connected_account_id) }

      it 'sets sub_conversation status to IN_PROGRESS and main conversation status to IN_PROGRESS' do
        described_class.new(conversation).call

        expect(sub_conversation.reload.status).to eq(IN_PROGRESS)
        expect(conversation.reload.status).to eq(IN_PROGRESS)
      end
    end

    context 'when sub_conversation does not exist' do
      before do
        allow(conversation).to receive(:ongoing_sub_conversation).and_return(nil)
      end

      it 'does not update conversation' do
        expect(conversation.reload.status).to eq(conversation.status)
      end
    end

    context 'when both sub_conversation and conversation are already IN_PROGRESS' do
      let!(:incoming_message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :incoming) }
      let!(:outgoing_message) { create(:message, sub_conversation_id: sub_conversation.id, direction: :outgoing) }

      before do
        sub_conversation.update!(status: IN_PROGRESS)
        conversation.update!(status: IN_PROGRESS)
      end

      it 'skips updating statuses and returns early' do
        expect(sub_conversation).not_to receive(:update!)
        expect(conversation).not_to receive(:update!)

        described_class.new(conversation).call
      end
    end
  end
end
