# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ConnectedAccountsController, type: :request do
  let(:user)                          { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                       { valid_headers(valid_auth_token) }
  let(:invalid_auth_token)            { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:no_whatsapp_permission_token)  { valid_headers(invalid_auth_token) }
  let(:invalid_header)                { invalid_headers }
  let(:access_token_response) do
    {
      'access_token' => 'EAANNzXrhnuwBOZBY9ZBeCTZAm7AZA7woZA1WnBYwhVq0vyZCrYbGX2akZCTflnGWRydEaOCGx57ZCz00yIRSoJ1iZBcyMyB9u9jKZCRbq7V6RakLZARK4Bi9BLXlbI8dWGCS83t7V7DK1FaRiLTZBeZACY7gX07OGPZAKkXEnBzgpZAzV5JIcLw82HcmmlihE0LJavZC8BXcYP3iqulIqPYrscdmbQW7NYPpUvjksadbsjakdbsajdbaskdbaskjdbaskdbaskjdb',
      'token_type' => 'bearer'
    }
  end
  let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }

  let(:phone_number_response) do
    JSON.parse({
      verified_name: '<PERSON><PERSON><PERSON>',
      code_verification_status: 'VERIFIED',
      display_phone_number: '+91 70302 40148',
      quality_rating: 'UNKNOWN',
      platform_type: 'NOT_APPLICABLE',
      throughput: {
        level: 'NOT_APPLICABLE'
      },
      last_onboarded_time: '2024-02-28T07:29:01+0000',
      id: '***************'
    }.to_json)
  end

  describe '#index' do
    before do
      @connected_account_1 = create(:connected_account, created_by_id: user.id, tenant_id: user.tenant_id)
      @connected_account_2 = create(:connected_account, created_by_id: user.id, tenant_id: user.tenant_id, access_token: 'A23dsjkadsdksakdsahk123')
    end

    context 'when user visits listing page' do
      context 'when user has all required permissions' do
        it 'should display all connected accounts' do
          get '/v1/messages/connected-accounts', headers: headers
          expect(response.status).to eq(200)
          expect(JSON.parse(response.body)).to eq({
            'content' => [
              {
                'id' => @connected_account_2.id,
                'wabaNumber' => @connected_account_2.waba_number,
                'wabaId' => @connected_account_2.waba_id,
                'phoneNumberId' => @connected_account_2.phone_number_id,
                'name' => @connected_account_2.name,
                'displayName' => @connected_account_2.display_name,
                'entitiesToCreate' => %w[lead contact],
                'status' => 'active',
                'createdBy' => UserSerializer::Details.serialize(@connected_account_2.created_by),
                'updatedBy' => UserSerializer::Details.serialize(@connected_account_2.updated_by),
                'isVerified' => true,
                'onboardingStatus' => 'WABA_ONBOARDED',
                'isChatbotConfigured' => @connected_account_2.is_chatbot_configured
              },
              {
                'id' => @connected_account_1.id,
                'wabaNumber' => @connected_account_1.waba_number,
                'wabaId' => @connected_account_1.waba_id,
                'phoneNumberId' => @connected_account_1.phone_number_id,
                'name' => @connected_account_1.name,
                'displayName' => @connected_account_1.display_name,
                'entitiesToCreate' => %w[lead contact],
                'status' => 'active',
                'createdBy' => UserSerializer::Details.serialize(@connected_account_1.created_by),
                'updatedBy' => UserSerializer::Details.serialize(@connected_account_1.updated_by),
                'isVerified' => true,
                'onboardingStatus' => 'WABA_ONBOARDED',
                'isChatbotConfigured' => @connected_account_1.is_chatbot_configured
              }
            ]
          })
        end
      end

      context 'when user has invalid token' do
        it 'should display error message' do
          get '/v1/messages/connected-accounts', headers: invalid_header
          expect(response.status).to eq(401)
          expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022001', 'message' => nil })
        end
      end

      context 'when user has no required permissions' do
        it 'should display error message' do
          get '/v1/messages/connected-accounts', headers: no_whatsapp_permission_token
          expect(response.status).to eq(401)
          expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022002', 'message' => 'Unauthorized access.' })
        end
      end
    end
  end

  describe '#show' do
    let!(:connected_account) { create(:connected_account, created_by: user) }

    context 'when user has access to connected account' do
      it 'returns connected account in response' do
        get "/v1/messages/connected-accounts/#{connected_account.id}", headers: headers

        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)).to eq({
          "id" => connected_account.id,
          "name" => connected_account.name,
          "displayName" => connected_account.display_name,
          "wabaNumber" => connected_account.waba_number,
          "entitiesToCreate" => [
            "lead",
            "contact"
          ],
          "status" => "active",
          "isVerified" => true,
          "createdBy" => {
            "id" => user.id,
            "name" => user.name
          },
          "updatedBy" => {
            "id" => user.id,
            "name" => user.name
          }
        })
      end
    end

    context 'when id is invalid' do
      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id + 1}", headers: headers

        expect(response.status).to eq(404)
        expect(JSON.parse(response.body)).to eq({ "errorCode" => "022006", "message" => "Account not found" })
      end
    end

    context 'when user does not have access to connected account' do
      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id}", headers: no_whatsapp_permission_token

        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when user context is invalid' do
      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id}", headers: invalid_headers

        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#create' do
    context 'when user creates connected account' do
      context 'when all params are valid' do
        it 'returns connected account' do
          facebook_auth_response = Facebook::Response.new(
            status_code: '200',
            body: access_token_response
          )

          facebook_phone_number_response = Facebook::Response.new(
            status_code: '200',
            body: phone_number_response
          )
          expect_any_instance_of(Facebook::AuthCode).to receive(:exchange).and_return(facebook_auth_response)
          expect_any_instance_of(Facebook::PhoneNumber).to receive(:find).and_return(facebook_phone_number_response)
          post '/v1/messages/connected-accounts', params: { phoneNumberId: '***************', wabaId: '1234', authCode: '1234' }.to_json, headers: headers
          connected_account = ConnectedAccount.last
          body = JSON.parse(response.body)
          expect(response.code).to eq('201')
          expect(body['id']).to eq(connected_account.id)
          expect(body['wabaNumber']).to eq(connected_account.waba_number)
          expect(connected_account.phone_number_id).to eq('***************')
        end
      end

      context 'when user has invalid token' do
        it 'should display error message' do
          post '/v1/messages/connected-accounts', params: { phoneNumberId: '***************', wabaId: '1234', authCode: '1234' }.to_json, headers: invalid_header
          expect(response.status).to eq(401)
          expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022001', 'message' => nil })
        end
      end
    end
  end

  describe '#update' do
    context 'when connected account is not present' do
      it 'returns appropriate error' do
        params =  {
          displayName: 'Updated Account name',
          entitiesToCreate: ['lead']
        }
        put '/v1/messages/connected-accounts/12', params: params.to_json, headers: headers
        expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022006', 'message' => 'Connected Account not found' })
      end
    end

    context 'when connected account is present but there is invalid token' do
      it 'returns appropriate error' do
        connected_account = create(:connected_account, waba_number: '+************', tenant_id: user.tenant_id)
        params = {
          displayName: 'Updated Account name',
          entitiesToCreate: ['lead']
        }

        put "/v1/messages/connected-accounts/#{connected_account.id}", params: params.to_json, headers: invalid_header
        expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022001', 'message' => nil })
      end
    end

    context 'when connected account is present but there is unauthorized token' do
      it 'returns appropriate error' do
        connected_account = create(:connected_account, waba_number: '+************', tenant_id: user.tenant_id)
        params = {
          displayName: 'Updated Account name',
          entitiesToCreate: ['lead']
        }

        put "/v1/messages/connected-accounts/#{connected_account.id}", params: params.to_json, headers: no_whatsapp_permission_token
        expect(JSON.parse(response.body)).to eq({ 'errorCode' => '022002', 'message' => 'Unauthorized access.' })
      end
    end

    context 'when connected account is present and have valid token' do
      let(:connected_account) { create(:connected_account, waba_number: '+************', tenant_id: user.tenant_id) }
      let(:params) { { displayName: 'Updated Account name', entitiesToCreate: ['lead'] } }

      before do
        stub_request(:post, "https://api.interakt.ai/v1/organizations/tp-signup/")
          .with(
            headers: {
              content_type: 'application/json',
              Authorization: 'partner-token'
            },
            body: {
              entry: [
                {
                  changes: [
                    {
                      value: {
                        event: 'PARTNER_ADDED',
                        waba_info: {
                          waba_id: connected_account&.waba_id,
                          solution_id: 'solution-id',
                          phone_number: connected_account&.waba_number
                        }
                      }
                    }
                  ]
                }
              ],
              object: 'tech_partner'
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/onboarding/waba-onboarding-api-success.json'))
      end

      it 'updates and returns connected account' do
        put "/v1/messages/connected-accounts/#{connected_account.id}", params: params.to_json, headers: headers
        response_body = JSON.parse(response.body)
        expect(response_body['displayName']).to eq('Updated Account name')
        expect(response_body['entitiesToCreate']).to eq(['lead'])
      end
    end

    context 'when entities to create is not present' do
      let(:params) { { displayName: 'Updated Account Name' } }
      let(:connected_account) { create(:connected_account, waba_number: '+************', tenant_id: user.tenant_id, entities_to_create: ['contact'], interakt_onboarding_status: 'WABA_ONBOARDED') }

      it 'should not reset entities to create' do
        put "/v1/messages/connected-accounts/#{connected_account.id}", params: params.to_json, headers: headers
        response_body = JSON.parse(response.body)
        expect(response_body['displayName']).to eq('Updated Account Name')
        expect(response_body['entitiesToCreate']).to eq(['contact'])
      end
    end
  end

  describe '#activate' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, interakt_onboarding_status: 'WABA_ONBOARDED') }

    context 'when connected account is present' do
      it 'activates account' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/activate", headers: headers

        expect(response.status).to eq(200)
        expect(connected_account.reload.status).to eq('active')
      end
    end

    context 'when waba not onboarded to interakt' do
      before { connected_account.update(interakt_onboarding_status: 'WABA_ONBOARDING_FAILED') }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/activate", headers: headers

        expect(response.status).to eq(422)
        expect(response.parsed_body['errorCode']).to eq('022014')
        expect(response.parsed_body['message']).to eq('Failed to register whatsapp business. Please contact support.')
      end
    end

    context 'when connected account is not present' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id + 1}/activate", headers: headers

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('022006')
        expect(response.parsed_body['message']).to eq('Connected Account not found')
      end
    end

    context 'when user context is missing' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/activate", headers: no_whatsapp_permission_token

        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('022002')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end
  end

  describe '#deactivate' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }

    context 'when connected account is present' do
      it 'deactivates account' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/deactivate", headers: headers

        expect(response.status).to eq(200)
        expect(connected_account.reload.status).to eq('inactive')
      end
    end

    context 'when connected account is not present' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id + 1}/deactivate", headers: headers

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('022006')
        expect(response.parsed_body['message']).to eq('Connected Account not found')
      end
    end
  end

  describe '#destroy' do
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:different_tenant_connected_account) { create(:connected_account) }

    context 'when connected account exists' do
      it 'deletes the connected account successfully' do
        expect {
          delete "/v1/messages/connected-accounts/#{connected_account.id}"
        }.to change(ConnectedAccount, :count).by(-1)

        expect(response.status).to eq(200)
        expect(response.body).to eq('')
        expect(ConnectedAccount.find_by(id: connected_account.id)).to be_nil
      end

      it 'does not affect other connected accounts' do
        other_connected_account

        expect {
          delete "/v1/messages/connected-accounts/#{connected_account.id}"
        }.to change(ConnectedAccount, :count).by(-1)

        expect(ConnectedAccount.find_by(id: other_connected_account.id)).to be_present
      end
    end

    context 'when connected account has associated records' do
      let(:another_user) { create(:user, tenant_id: user.tenant_id) }
      let!(:agent_user) { create(:agent_user, connected_account: connected_account, user: user, entity_type: LOOKUP_LEAD) }

      it 'raises error' do
        expect {
          delete "/v1/messages/connected-accounts/#{connected_account.id}"
        }.to raise_error(ActiveRecord::InvalidForeignKey)

        expect(ConnectedAccount.find_by(id: connected_account.id)).not_to be_nil
      end
    end

    context 'when connected account does not exist' do
      it 'returns not found error' do
        delete "/v1/messages/connected-accounts/999999"

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('022006')
        expect(response.parsed_body['message']).to eq('Connected Account not found')
      end
    end
  end

  describe '#request_code' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let(:code_method) { 'SMS' }
    let(:language) { 'en' }
    let(:params) { { codeMethod: code_method, language: language } }

    context 'when connected account is present' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/request_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code_method: code_method,
              language: language
            }
          )
          .to_return(status: 200, body: { success: true }.to_json)
      end

      it 'sends code to phone number and status 200' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/request-code", headers: headers, params: params.to_json

        expect(response.status).to eq(200)
      end
    end

    context 'when invalid code method' do
      let(:code_method) { 'sms' }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/request-code", headers: headers, params: params.to_json

        expect(response.status).to eq(422)
        expect(response.parsed_body['errorCode']).to eq('022003')
        expect(response.parsed_body['message']).to eq('Invalid code method')
      end
    end

    context 'when connected account is not present' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id + 1}/request-code", headers: headers, params: params.to_json

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('022006')
        expect(response.parsed_body['message']).to eq('Connected Account not found')
      end
    end

    context 'when user context is missing' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/request-code", headers: no_whatsapp_permission_token, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('022002')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end
  end

  describe '#verify_code' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, is_verified: false) }
    let(:params) { { otpCode: 123456 } }

    context 'when connected account is present' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: 123456
            }
          )
          .to_return(status: 200, body: { success: true }.to_json)
      end

      it 'verifies code and updates connected account verified true' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/verify-code", headers: headers, params: params.to_json

        expect(response.status).to eq(200)
        expect(connected_account.reload.is_verified).to be_truthy
      end
    end

    context 'when connected account is not present' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id + 1}/verify-code", headers: headers, params: params.to_json

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('022006')
        expect(response.parsed_body['message']).to eq('Connected Account not found')
      end
    end

    context 'when user context is missing' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/verify-code", headers: no_whatsapp_permission_token, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('022002')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end

    context 'when error while verifying code' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: 123456
            }
          )
          .to_return(status: 400, body: file_fixture('facebook/phone_number/verify-code-error.json').read)
      end

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/verify-code", headers: headers, params: params.to_json

        expect(response.status).to eq(422)
        expect(response.parsed_body['errorCode']).to eq('022017')
        expect(response.parsed_body['message']).to eq('Verify code error. Code couldn\'t be verified, You have already verified ownership of this phone number.')
      end
    end
  end

  describe '#lookup' do
    let!(:connected_account) { create(:connected_account, created_by: user, status: ACTIVE) }
    let!(:inactive_connected_account) { create(:connected_account, created_by: user, status: INACTIVE) }

    before do
      create(:agent_user, user_id: user.id, tenant_id: user.tenant_id, connected_account_id: connected_account.id, entity_type: LOOKUP_CONTACT)
      create(:agent_user, user_id: user.id, tenant_id: user.tenant_id, connected_account_id: inactive_connected_account.id, entity_type: LOOKUP_CONTACT)
    end

    context 'when user has valid token' do
      context 'when query is not present' do
        it 'returns serialized lookup for connected accounts' do
          get '/v1/messages/connected-accounts/lookup', headers: headers

          expect(response.status).to eq(200)
          expect(response.parsed_body).to eq({
            "content" => [
              {
                "id" => connected_account.id,
                "name" => connected_account.display_name,
                "businessName" => connected_account.name,
                "isChatbotConfigured" => connected_account.is_chatbot_configured
              }
            ]
          })
        end
      end

      context 'when entity type is present' do
        it 'returns serialized lookup for connected accounts having agents matching entity type' do
          get '/v1/messages/connected-accounts/lookup?entityType=contact', headers: headers

          expect(response.status).to eq(200)
          expect(response.parsed_body).to eq({
            "content" => [
              {
                "id" => connected_account.id,
                "name" => connected_account.display_name,
                "businessName" => connected_account.name,
                "isChatbotConfigured" => connected_account.is_chatbot_configured
              }
            ]
          })
        end
      end

      context 'when view is billing' do
        it 'returns serialized lookup for connected accounts having status both ACTIVE and INACTIVE' do
          get '/v1/messages/connected-accounts/lookup?view=billing', headers: headers

          expect(response.status).to eq(200)
          expect(response.parsed_body['content']).to match_array([
            {
              "id" => connected_account.id,
              "name" => connected_account.display_name,
              "businessName" => connected_account.name,
              "isChatbotConfigured" => connected_account.is_chatbot_configured
            },
            {
              "id" => inactive_connected_account.id,
              "name" => inactive_connected_account.display_name,
              "businessName" => inactive_connected_account.name,
              "isChatbotConfigured" => inactive_connected_account.is_chatbot_configured
            }
          ])
        end
      end
    end

    context 'when user has invalid token' do
      it 'returns error' do
        get '/v1/messages/connected-accounts/lookup', headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#entity_phones' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active') }
    let(:first_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:second_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:sgp_conversation){ create(:conversation, phone_number: '+**********', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
 
    context 'when requested for entity' do
      context 'and entity is valid' do
        before(:each) do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ valid_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

          @ten_hours_ago = 10.hours.ago
          @twenty_five_hours_ago = 25.hours.ago
          first_conversation.update(last_message_received_at: @ten_hours_ago)
          second_conversation.update(last_message_received_at: @twenty_five_hours_ago)
          sgp_conversation.update(last_message_received_at: @twenty_five_hours_ago)
        end

        it 'returns phone numbers with session details' do
          get "/v1/messages/connected-accounts/#{connected_account.id}/lead/34343/phone-numbers", headers: headers

          expect(response.status).to eq(200)
          expect(response.parsed_body).to match_array([
            {
              "code"=>"IN",
              "dialCode"=>"+91",
              "id"=>207783,
              "type"=>"MOBILE",
              "value"=>"**********",
              "primary"=>true,
              "session"=>"active",
              "lastContactedAt"=> @ten_hours_ago.strftime("%FT%T.%LZ"),
              "conversationId"=>first_conversation.id
            },
           {
              "code"=>"IN",
              "dialCode"=>"+91",
              "id"=>207784,
              "type"=>"MOBILE",
              "value"=>"**********",
              "primary"=>false,
              "session"=>"inactive",
              "lastContactedAt"=> @twenty_five_hours_ago.strftime("%FT%T.%LZ"),
              "conversationId"=>second_conversation.id
            },
            {
              "code"=>"SG",
              "dialCode"=>"+65",
              "id"=>207785,
              "type"=>"MOBILE",
              "value"=>"88888888",
              "primary"=>false,
              "session"=>"inactive",
              "lastContactedAt"=> @twenty_five_hours_ago.strftime("%FT%T.%LZ"),
              "conversationId"=>sgp_conversation.id
            }
          ])
        end
      end
    end
  end
end
