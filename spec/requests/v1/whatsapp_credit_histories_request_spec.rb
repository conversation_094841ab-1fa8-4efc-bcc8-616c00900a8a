# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::WhatsappCreditHistoriesController, type: :request do
  let(:user) { create(:user) }

  describe '#delete_all' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:different_tenant_connected_account) { create(:connected_account) }

    context 'when removing connected_account_id from credit histories' do
      let!(:credit_history_1) do
        create(:whatsapp_credit_history,
               tenant_id: user.tenant_id,
               connected_account_id: connected_account.id,
               entry_type: CREDITS_DEDUCTED,
               value: 10.0,
               balance: 990.0)
      end

      let!(:credit_history_2) do
        create(:whatsapp_credit_history,
               tenant_id: user.tenant_id,
               connected_account_id: connected_account.id,
               entry_type: CREDITS_ADDED,
               value: 100.0,
               balance: 1000.0)
      end

      let!(:other_account_credit_history) do
        create(:whatsapp_credit_history,
               tenant_id: user.tenant_id,
               connected_account_id: other_connected_account.id,
               entry_type: CREDITS_DEDUCTED,
               value: 5.0,
               balance: 995.0)
      end

      let!(:different_tenant_credit_history) do
        create(:whatsapp_credit_history,
               tenant_id: different_tenant_connected_account.tenant_id,
               connected_account_id: different_tenant_connected_account.id,
               entry_type: CREDITS_DEDUCTED,
               value: 15.0,
               balance: 985.0)
      end

      it 'removes connected_account_id from credit histories for the specified connected account' do
        expect {
          delete "/v1/messages/connected-accounts/#{connected_account.id}/credit-history"
        }.to change {
          WhatsappCreditHistory.where(connected_account_id: connected_account.id).count
        }.from(2).to(0)

        expect(response.status).to eq(200)
        expect(response.body).to eq('')

        credit_history_1.reload
        credit_history_2.reload
        expect(credit_history_1.connected_account_id).to be_nil
        expect(credit_history_2.connected_account_id).to be_nil

        expect(credit_history_1.tenant_id).to eq(user.tenant_id)
        expect(credit_history_1.value).to eq(10.0)
        expect(credit_history_1.balance).to eq(990.0)
        expect(credit_history_2.tenant_id).to eq(user.tenant_id)
        expect(credit_history_2.value).to eq(100.0)
        expect(credit_history_2.balance).to eq(1000.0)
      end

      it 'does not affect credit histories of other connected accounts' do
        delete "/v1/messages/connected-accounts/#{connected_account.id}/credit-history"

        other_account_credit_history.reload
        expect(other_account_credit_history.connected_account_id).to eq(other_connected_account.id)
        expect(other_account_credit_history.value).to eq(5.0)
        expect(other_account_credit_history.balance).to eq(995.0)
      end

      it 'does not affect credit histories of different tenants' do
        delete "/v1/messages/connected-accounts/#{connected_account.id}/credit-history"

        different_tenant_credit_history.reload
        expect(different_tenant_credit_history.connected_account_id).to eq(different_tenant_connected_account.id)
        expect(different_tenant_credit_history.value).to eq(15.0)
        expect(different_tenant_credit_history.balance).to eq(985.0)
      end
    end

    context 'when connected account has no credit histories' do
      it 'returns success without errors' do
        delete "/v1/messages/connected-accounts/#{connected_account.id}/credit-history"

        expect(response.status).to eq(200)
        expect(response.body).to eq('')
      end
    end

    context 'when connected account does not exist' do
      it 'returns success without errors' do
        delete "/v1/messages/connected-accounts/999999/credit-history"

        expect(response.status).to eq(200)
        expect(response.body).to eq('')
      end
    end
  end
end
