# frozen_string_literal: true

require 'rails_helper'

describe V1::WhatsappCreditsController, type: :request do
  let(:user)                       { create(:user)}
  let(:valid_auth_token)           { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:auth_data)                  { User::TokenParser.parse(valid_auth_token.token) }
  let(:headers)                    { valid_headers(valid_auth_token) }

  describe '#get_summary' do
    context 'when whatsapp credit summary is requested' do
      let!(:whatsapp_credit) { create(:whatsapp_credit, tenant_id: auth_data.tenant_id, credits_revised_at: **********) }

      it 'returns whatsapp credit summary' do
        get "/v1/messages/whatsapp-credits/summary", headers: headers

          expect(response.status).to eq(200)
          expect(response.parsed_body).to eq({
            "creditsRevisedAt"=>'2024-08-12T16:23:20.000Z',
            "total"=> whatsapp_credit.total,
            "used"=> whatsapp_credit.consumed,
            "parked"=> whatsapp_credit.parked
          })
      end
    end

    context 'when credit info is not found' do
      it 'returns dummy response' do
        get "/v1/messages/whatsapp-credits/summary", headers: headers

        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "creditsRevisedAt"=> nil,
          "total"=> 0,
          "used"=> 0,
          "parked"=>0.0
        })
      end
    end

    context 'with invalid token' do
      before { get '/v1/messages/whatsapp-credits/summary', headers: invalid_headers }

      it 'should throw invalid token error' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
      end
    end
  end

  describe '#post_history' do
    context 'when whatsapp credit history is requested' do
      let(:connected_account){ create(:connected_account, tenant_id: auth_data.tenant_id, display_name: 'Test Account') }
      let(:connected_account_2){ create(:connected_account, tenant_id: auth_data.tenant_id, display_name: 'Second Account') }
      let!(:whatsapp_credit_history) { create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, value: 1000, balance: 1000) }
      let!(:another_whatsapp_credit_history) { create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'MARKETING', value: 10) }
      let!(:other_tenant_history) { create(:whatsapp_credit_history) }
      let(:second_whatsapp_credit_history) { create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: 1.day.ago.to_i, connected_account_id: connected_account.id, entry_type: CREDITS_DEDUCTED,  conversation_category: 'MARKETING', value: 10) }

      it 'returns whatsapp credit history' do
        post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers
          expect(response.status).to eq(200)
          expect(response.parsed_body).to eq({
            "content"=> [
              {
                "balance"=> another_whatsapp_credit_history.balance,
                "entryType" => 'CREDITS_DEDUCTED',
                "category" => 'MARKETING',
                "value"=> -another_whatsapp_credit_history.value,
                "startTime" => '2024-08-12T16:23:20.000Z',
                "connectedAccount" => {
                  "id" => connected_account.id,
                  "name" => "Test Account"
                }
              },
              {
                "balance"=> whatsapp_credit_history.balance,
                "category"=> nil,
                "entryType" => 'CREDITS_ADDED',
                "value"=> whatsapp_credit_history.value,
                "startTime" => '2024-08-12T16:23:20.000Z'
              }
            ],
            "first"=>true,
            "last"=>true,
            "page"=>{"no"=>1, "size"=>10},
            "totalElements"=>2,
            "totalPages"=>1,
            "templateUsage" => {
              "CHATBOT" => 0,
              "MARKETING" => another_whatsapp_credit_history.value,
              "SERVICE" => 0,
              "UTILITY" => 0
            }
          })
      end

      it 'returns usage per template' do
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account_2.id, conversation_category: 'MARKETING', value: 20, balance: 9970)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'MARKETING', value: 50, balance: 9920)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'UTILITY', value: 90, balance: 9830)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account_2.id, conversation_category: 'UTILITY', value: 100, balance: 9730)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'UTILITY', value: 150, balance: 9580)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'SERVICE', value: 80, balance: 9500)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account_2.id, conversation_category: 'SERVICE', value: 40, balance: 9460)
        create(:whatsapp_credit_history, tenant_id: auth_data.tenant_id, start_time: **********, entry_type: CREDITS_DEDUCTED, connected_account_id: connected_account.id, conversation_category: 'CHATBOT', value: 30, balance: 9430)
        
        post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers
          expect(response.status).to eq(200)
          expect(response.parsed_body['templateUsage']).to eq({
            "MARKETING" => 80.0,
            "UTILITY" => 340.0,
            "SERVICE" => 120.0,
            "CHATBOT" => 30.0
          })
      end

      context 'when filters are present' do
        context 'when connected account rule is not present in json rule' do
          let(:params) {
            {
              "jsonRule": {
                "condition": "AND",
                "valid": true,
                "rules": [
                ]
              }
            }
          }

          it 'returns whatsapp credit history logs of all connected accounts' do
            post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers, params: params.to_json

            expect(response.status).to eq(200)
            expect(response.parsed_body).to eq({
              "content"=> [
                {
                  "balance"=> another_whatsapp_credit_history.balance,
                  "entryType" => 'CREDITS_DEDUCTED',
                  "category" => 'MARKETING',
                  "value"=> -another_whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z',
                  "connectedAccount" => {
                    "id" => connected_account.id,
                    "name" => "Test Account"
                  }
                },
                {
                  "balance"=> whatsapp_credit_history.balance,
                  "category"=> nil,
                  "entryType" => 'CREDITS_ADDED',
                  "value"=> whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z'
                }
              ],
              "first"=>true,
              "last"=>true,
              "page"=>{"no"=>1, "size"=>10},
              "totalElements"=>2,
              "totalPages"=>1,
              "templateUsage" => {
                "MARKETING" => another_whatsapp_credit_history.value,
                "SERVICE" => 0,
                "UTILITY" => 0,
                "CHATBOT" => 0
              }
            })
          end
        end

        context 'when connected account rule is present in json rule' do
          let(:params) {
            {
              "jsonRule": {
                "condition": "AND",
                "valid": true,
                "rules": [
                  {
                    "id": "connectedAccount",
                    "field": "connectedAccount",
                    "type": "long",
                    "operator": "equal",
                    "value": connected_account.id
                  }
                ]
              }
            }
          }

          it 'returns whatsapp credit history logs for that account only along with credits added logs' do
            post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers, params: params.to_json

            expect(response.status).to eq(200)
            expect(response.parsed_body).to eq({
              "content"=> [
                {
                  "balance"=> another_whatsapp_credit_history.balance,
                  "entryType" => 'CREDITS_DEDUCTED',
                  "category" => 'MARKETING',
                  "value"=> -another_whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z',
                  "connectedAccount" => {
                    "id" => connected_account.id,
                    "name" => "Test Account"
                  }
                },
                {
                  "balance"=> whatsapp_credit_history.balance,
                  "category"=> nil,
                  "entryType" => 'CREDITS_ADDED',
                  "value"=> whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z'
                }
              ],
              "first"=>true,
              "last"=>true,
              "page"=>{"no"=>1, "size"=>10},
              "totalElements"=>2,
              "totalPages"=>1,
              "templateUsage" => {
                "MARKETING" => another_whatsapp_credit_history.value,
                "SERVICE" => 0,
                "UTILITY" => 0,
                "CHATBOT" => 0
              }
            })
          end
        end

        context 'when startTime field is presnet in json rule' do
          let(:params) {
            {
              "jsonRule": {
                "condition": "AND",
                "valid": true,
                "rules": [
                  {
                    "id": "connectedAccount",
                    "field": "connectedAccount",
                    "type": "long",
                    "operator": "equal",
                    "value": connected_account.id
                  },
                  {
                    "id": "startTime",
                    "field": "startTime",
                    "type": "date",
                    "operator": "between",
                    "timeZone": "Asia/Calcutta",
                    "value": ["2024-08-11T05:00:00.000+0000", Time.now]
                  }
                ]
              }
            }
          }

          it 'returns whatsapp credit history by filtering according to start time' do
            post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers, params: params.to_json

            expect(response.status).to eq(200)
            expect(response.parsed_body).to eq({
              "content"=> [
                {
                  "balance"=> another_whatsapp_credit_history.balance,
                  "entryType" => 'CREDITS_DEDUCTED',
                  "category" => 'MARKETING',
                  "value"=> -another_whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z',
                  "connectedAccount" => {
                    "id" => connected_account.id,
                    "name" => "Test Account"
                  }
                },
                {
                  "balance"=> whatsapp_credit_history.balance,
                  "category"=> nil,
                  "entryType" => 'CREDITS_ADDED',
                  "value"=> whatsapp_credit_history.value,
                  "startTime" => '2024-08-12T16:23:20.000Z'
                }
              ],
              "first"=>true,
              "last"=>true,
              "page"=>{"no"=>1, "size"=>10},
              "totalElements"=>2,
              "totalPages"=>1,
              "templateUsage" => {
                "MARKETING" => another_whatsapp_credit_history.value,
                "SERVICE" => 0,
                "UTILITY" => 0,
                "CHATBOT" => 0
              }
            })
          end

          context 'when yesterday filter applied on startTime' do
            let(:params) {
              {
                "jsonRule": {
                  "condition": "AND",
                  "valid": true,
                  "rules": [
                    {
                      "id": "connectedAccount",
                      "field": "connectedAccount",
                      "type": "long",
                      "operator": "equal",
                      "value": connected_account.id
                    },
                    {
                      "id": "startTime",
                      "field": "startTime",
                      "type": "date",
                      "operator": "yesterday",
                      "timeZone": "Asia/Calcutta",
                      "value": nil
                    }
                  ]
                }
              }
            }

            before(:each) { second_whatsapp_credit_history }

            it 'returns whatsapp credit history for yesterday' do
              post "/v1/messages/whatsapp-credits/history?page=1&size=10", headers: headers, params: params.to_json
              expect(response.status).to eq(200)
              expect(response.parsed_body).to eq({
                "content"=> [
                  {
                    "balance"=> second_whatsapp_credit_history.balance,
                    "entryType" => 'CREDITS_DEDUCTED',
                    "category" => 'MARKETING',
                    "value"=> -second_whatsapp_credit_history.value,
                    "startTime" => Time.at(second_whatsapp_credit_history.start_time).strftime('%FT%T.%LZ'),
                    "connectedAccount" => {
                      "id" => connected_account.id,
                      "name" => "Test Account"
                    }
                  }
                ],
                "first"=>true,
                "last"=>true,
                "page"=>{"no"=>1, "size"=>10},
                "totalElements"=>1,
                "totalPages"=>1,
                "templateUsage" => {
                  "MARKETING" => second_whatsapp_credit_history.value,
                  "SERVICE" => 0,
                  "UTILITY" => 0,
                  "CHATBOT" => 0
                }
              })
            end
          end
        end
      end
    end

    context 'with invalid token' do
      before { post '/v1/messages/whatsapp-credits/history?page=1&size=10', headers: invalid_headers }

      it 'should throw invalid token error' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
      end
    end
  end

  describe '#get_status' do
    context 'when bulk message credit status is requested' do
      let!(:whatsapp_credit) { create(:whatsapp_credit, tenant_id: auth_data.tenant_id, credits_revised_at: **********) }

      it 'returns bulk message credits status' do
        get "/v1/messages/whatsapp-credits/status", headers: headers

        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "availableForBulkMessages"=> true
        })
      end
    end

    context 'when credit info is not found' do
      it 'returns dummy response' do
        get "/v1/messages/whatsapp-credits/status", headers: headers

        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "availableForBulkMessages"=> false
        })
      end
    end

    context 'with invalid token' do
      before { get "/v1/messages/whatsapp-credits/status", headers: invalid_headers }

      it 'should throw invalid token error' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
      end
    end
  end
end
