# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::WhatsappTemplatesController, type: :request do
  let(:user)                          { create(:user) }
  let(:another_user)                  { create(:user, tenant_id: user.tenant_id) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                       { valid_headers(valid_auth_token) }
  let(:invalid_auth_token)            { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:no_whatsapp_permission_headers)  { valid_headers(invalid_auth_token) }
  let(:invalid_header)                { invalid_headers }

  describe '#show' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account) }

    context 'when user has access to template' do
      it 'returns template' do
        get "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers

        expect(response.status).to eq(200)
        expect(response.parsed_body.slice(*%w[id name entityType category language status createdAt updatedAt connectedAccount createdBy updatedBy recordActions])).to eq({
          "id" => whatsapp_template.id,
          "name" => whatsapp_template.name,
          "entityType" => whatsapp_template.entity_type,
          "category" => whatsapp_template.category,
          "language" => whatsapp_template.language,
          "status" => whatsapp_template.status,
          "createdAt" => whatsapp_template.created_at.iso8601(3),
          "updatedAt" => whatsapp_template.updated_at.iso8601(3),
          "connectedAccount" => {
            "id" => connected_account.id,
            "name" => connected_account.display_name,
            "businessName" => connected_account.name
          },
          "createdBy" => {
            "id" => user.id,
            "name" => user.name
          },
          "updatedBy" => {
            "id" => user.id,
            "name" => user.name
          },
          'recordActions' => {
            'read' => true,
            'update' => true
          }
        })

        expect(response.parsed_body['components']).to match_array(
          whatsapp_template.components.map do |wtc|
            {
              "id" => wtc.id,
              "position" => wtc.position,
              "type" => wtc.component_type,
              "format" => wtc.component_format,
              "text" => wtc.component_text,
              "value" => wtc.component_value
            }
          end
        )
      end
    end

    context 'when user does not have access to template' do
      it 'returns error' do
        get "/v1/messages/whatsapp-templates/-1", headers: headers

        expect(response.status).to eq(404)
        expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => nil })
      end
    end

    context 'when user does not have read' do
      it 'returns error' do
        get "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: no_whatsapp_permission_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid token' do
      it 'returns error' do
        get "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#search' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }

    context 'when templates are present' do
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account) }
      let(:params) { { jsonRule: { rules: [{ field: 'id', type: 'long', operator: 'equal', value: whatsapp_template.id }] } } }

      before { post '/v1/messages/whatsapp-templates/search', headers: headers, params: params.to_json }

      it 'returns status 200' do
        expect(response.status).to eq(200)
      end

      it 'returns template in response' do
        expect(response.parsed_body['content'].count).to eq(1)

        template_response = response.parsed_body['content'].first
        expect(template_response).to eq({
          'id' => whatsapp_template.id,
          'name' => whatsapp_template.name,
          'entityType' => whatsapp_template.entity_type,
          'category' => whatsapp_template.category,
          'language' => 'en',
          'status' => 'DRAFT',
          'createdAt' => whatsapp_template.created_at.iso8601(3),
          'updatedAt' => whatsapp_template.updated_at.iso8601(3),
          'connectedAccount' => {
              'id' => connected_account.id,
              'name' => connected_account.display_name,
              "businessName" => connected_account.name
          },
          'createdBy' => {
              'id' => user.id,
              'name' => user.name
          },
          'updatedBy' => {
              'id' => user.id,
              'name' => user.name
          },
          'recordActions' => {
            'read' => true,
            'update' => true
          }
        })
      end

      it 'returns pagination params as well' do
        expect(response.parsed_body['page']).to eq({ 'no' => 1, 'size' => 10 })
        expect(response.parsed_body['totalElements']).to eq(1)
        expect(response.parsed_body['totalPages']).to eq(1)
        expect(response.parsed_body['first']).to be_truthy
        expect(response.parsed_body['last']).to be_truthy
      end
    end

    context 'when no template is present' do
      before { post '/v1/messages/whatsapp-templates/search', headers: headers }

      it 'returns status 200' do
        expect(response.status).to eq(200)
      end

      it 'returns blank response' do
        expect(response.parsed_body['content'].count).to eq(0)
      end

      it 'returns pagination params as well' do
        expect(response.parsed_body['page']).to eq({ 'no' => 1, 'size' => 10 })
        expect(response.parsed_body['totalElements']).to eq(0)
        expect(response.parsed_body['totalPages']).to eq(1)
        expect(response.parsed_body['first']).to be_truthy
        expect(response.parsed_body['last']).to be_truthy
      end
    end

    context 'when user does not have permission' do
      it 'returns error' do
        post '/v1/messages/whatsapp-templates/search', headers: no_whatsapp_permission_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid headers' do
      it 'returns error' do
        post '/v1/messages/whatsapp-templates/search', headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#create' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:params) do
      parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
      parsed_params['connectedAccount']['id'] = connected_account.id
      parsed_params
    end

    context 'when valid params' do
      before { create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id) }

      it 'creates template' do
        expect { post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json }
          .to change(WhatsappTemplate, :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: HEADER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BODY), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: FOOTER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON), :count).by(2)
      end

      it 'returns serialized template' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        expect(response.status).to eq(200)
        new_template = WhatsappTemplate.last
        serialized_template = response.parsed_body
        expect(serialized_template.except('components')).to eq({
          "id" => new_template.id,
          "name" => new_template.name,
          "entityType" => "lead",
          "category" => new_template.category,
          "language" => "en",
          "status" => "DRAFT",
          "createdAt" => new_template.created_at.iso8601(3),
          "updatedAt" => new_template.updated_at.iso8601(3),
          "connectedAccount" => {
            "id" => connected_account.id,
            "name" => connected_account.display_name,
            "businessName" => connected_account.name
          },
          "createdBy" => {
            "id" => user.id,
            "name" => user.name
          },
          "updatedBy" => {
            "id" => user.id,
            "name" => user.name
          },
          'recordActions' => {
            'read' => true,
            'update' => true
          }
        })
      end

      it 'returns serialized header component' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        header_component = WhatsappTemplate.last.components.find_by(component_type: HEADER)
        serialized_header = response.parsed_body['components'].find { |component| component['type'] == HEADER }

        expect(serialized_header).to eq({
          "id" => header_component.id,
          "position" => 0,
          "type" => 'HEADER',
          "format" => 'TEXT',
          "text" => 'Our {{1}} is on!',
          "value" => nil
        })
      end
      
      context 'with header component having image format' do
        let(:template_media) { create(:template_media, tenant_id: user.tenant_id) }
        let(:image_header_params) do
          parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
          parsed_params['connectedAccount']['id'] = connected_account.id
          header_component = parsed_params['components'].find { |component| component['type'] == 'HEADER' }
          header_component['format'] = 'IMAGE'
          header_component['text'] = nil
          header_component['value'] = template_media.id
          header_component['mediaType'] = 'STATIC'
          parsed_params
        end
        
        it 'creates template with image header and static media type' do
          expect { post '/v1/messages/whatsapp-templates', headers: headers, params: image_header_params.to_json }
            .to change(WhatsappTemplate, :count).by(1)
            .and change(WhatsappTemplateComponent.where(component_type: HEADER, component_format: IMAGE), :count).by(1)
        end
        
        it 'returns serialized header component with media type' do
          post '/v1/messages/whatsapp-templates', headers: headers, params: image_header_params.to_json
          
          header_component = WhatsappTemplate.last.components.find_by(component_type: HEADER)
          serialized_header = response.parsed_body['components'].find { |component| component['type'] == HEADER }
          
          expect(serialized_header).to eq({
            "id" => header_component.id,
            "position" => 0,
            "type" => 'HEADER',
            "format" => 'IMAGE',
            "text" => nil,
            "value" => "#{template_media.id}",
            "mediaType" => 'STATIC'
          })
        end
      end

      it 'returns serialized body component' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        body_component = WhatsappTemplate.last.components.find_by(component_type: BODY)
        serialized_body = response.parsed_body['components'].find { |component| component['type'] == BODY }

        expect(serialized_body).to eq({
          "id" => body_component.id,
          "position" => 0,
          "type" => 'BODY',
          "format" => 'TEXT',
          "text" => 'Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.',
          "value" => nil
        })
      end

      it 'returns serialized footer component' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        footer_component = WhatsappTemplate.last.components.find_by(component_type: FOOTER)
        serialized_footer = response.parsed_body['components'].find { |component| component['type'] == FOOTER }

        expect(serialized_footer).to eq({
          "id" => footer_component.id,
          "position" => 0,
          "type" => 'FOOTER',
          "format" => 'TEXT',
          "text" => 'Use the buttons below to manage your marketing subscriptions',
          "value" => nil
        })
      end

      it 'returns serialized button components' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        button_components = WhatsappTemplate.last.components.where(component_type: BUTTON).order(position: :asc)
        serialized_buttons = response.parsed_body['components'].select { |component| component['type'] == BUTTON }

        expect(serialized_buttons).to eq([
          {
            "id" => button_components.first.id,
            "position" => 1,
            "type" => 'BUTTON',
            "format" => 'QUICK_REPLY',
            "text" => 'Unsubscribe from Promos',
            "value" => nil
          },
          {
            "id" => button_components.last.id,
            "position" => 2,
            "type" => 'BUTTON',
            "format" => 'COPY_CODE',
            "text" => '25OFF',
            "value" => nil
          }
        ])
      end
    end

    context 'when invalid params' do
      context 'when connected account is invalid' do
        it 'returns error' do
          post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

          expect(response.status).to eq(422)
          expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent." })
        end
      end

      context 'when name is duplicated' do
        before do
          create(:whatsapp_template, connected_account: connected_account, name: params['name'])
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        end

        it 'returns error' do
          post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

          expect(response.status).to eq(422)
          expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Name has already been taken and Whatsapp template namespace has already been taken" })
        end
      end
    end

    context 'when component validations fail' do
      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        params['components'].find { |component| component['type'] == 'BODY' }['text'] = ''
        params['components'].find { |component| component['type'] == 'BUTTON' && component['format'] == 'COPY_CODE' }['text'] = '250OFF&'
      end

      it 'returns error' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: params.to_json

        expect(response.status).to eq(422)
        expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Component text can't be blank, copy code text should only contain alphanumeric characters" })
      end
    end
    
    context 'when media header component validation fails' do
      let(:template_media) { create(:template_media, tenant_id: user.tenant_id) }
      let(:invalid_media_params) do
        parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
        parsed_params['connectedAccount']['id'] = connected_account.id
        
        header_component = parsed_params['components'].find { |component| component['type'] == 'HEADER' }
        header_component['format'] = 'IMAGE'
        header_component['text'] = nil
        header_component['value'] = template_media.id
        header_component['mediaType'] = 'INVALID_TYPE'
        
        parsed_params
      end
      
      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
      end
      
      it 'returns error for invalid media type' do
        post '/v1/messages/whatsapp-templates', headers: headers, params: invalid_media_params.to_json
        
        expect(response.status).to eq(422)
      end
    end

    context 'when user does not have permission to create' do
      it 'returns error' do
        post '/v1/messages/whatsapp-templates', headers: no_whatsapp_permission_headers, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        post '/v1/messages/whatsapp-templates', headers: invalid_headers, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#create_and_submit' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:params) do
      parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
      parsed_params['connectedAccount']['id'] = connected_account.id
      parsed_params
    end

    before { create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id) }

    context 'when valid params' do
      before do
        expect(SubmitWhatsappTemplateJob).to receive(:perform_later).once
      end

      it 'creates template' do
        expect { post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json }
          .to change(WhatsappTemplate, :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: HEADER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BODY), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: FOOTER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON), :count).by(2)
          .and change(VariableMapping.where(component_type: HEADER), :count).by(1)
          .and change(VariableMapping.where(component_type: BODY), :count).by(3)
          .and change(VariableMapping.where(component_type: BUTTON_COPY_CODE), :count).by(1)
      end

      it 'returns serialized template' do
        post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

        expect(response.status).to eq(200)
        new_template = WhatsappTemplate.last
        serialized_template = response.parsed_body
        expect(serialized_template.except('components')).to eq({
          "id" => new_template.id,
          "name" => new_template.name,
          "entityType" => "lead",
          "category" => new_template.category,
          "language" => "en",
          "status" => "SUBMITTING",
          "createdAt" => new_template.created_at.iso8601(3),
          "updatedAt" => new_template.updated_at.iso8601(3),
          "connectedAccount" => {
            "id" => connected_account.id,
            "name" => connected_account.display_name,
            "businessName" => connected_account.name
          },
          "createdBy" => {
            "id" => user.id,
            "name" => user.name
          },
          "updatedBy" => {
            "id" => user.id,
            "name" => user.name
          },
          'recordActions' => {
            'read' => true,
            'update' => true
          }
        })
      end

      it 'returns serialized header component' do
        post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

        header_component = WhatsappTemplate.last.components.find_by(component_type: HEADER)
        serialized_header = response.parsed_body['components'].find { |component| component['type'] == HEADER }

        expect(serialized_header).to eq({
          "id" => header_component.id,
          "position" => 0,
          "type" => 'HEADER',
          "format" => 'TEXT',
          "text" => 'Our {{1}} is on!',
          "value" => nil
        })
      end

      it 'returns serialized body component' do
        post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

        body_component = WhatsappTemplate.last.components.find_by(component_type: BODY)
        serialized_body = response.parsed_body['components'].find { |component| component['type'] == BODY }

        expect(serialized_body).to eq({
          "id" => body_component.id,
          "position" => 0,
          "type" => 'BODY',
          "format" => 'TEXT',
          "text" => 'Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.',
          "value" => nil
        })
      end

      it 'returns serialized footer component' do
        post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

        footer_component = WhatsappTemplate.last.components.find_by(component_type: FOOTER)
        serialized_footer = response.parsed_body['components'].find { |component| component['type'] == FOOTER }

        expect(serialized_footer).to eq({
          "id" => footer_component.id,
          "position" => 0,
          "type" => 'FOOTER',
          "format" => 'TEXT',
          "text" => 'Use the buttons below to manage your marketing subscriptions',
          "value" => nil
        })
      end

      it 'returns serialized button components' do
        post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

        button_components = WhatsappTemplate.last.components.where(component_type: BUTTON).order(position: :asc)
        serialized_buttons = response.parsed_body['components'].select { |component| component['type'] == BUTTON }

        expect(serialized_buttons).to eq([
          {
            "id" => button_components.first.id,
            "position" => 1,
            "type" => 'BUTTON',
            "format" => 'QUICK_REPLY',
            "text" => 'Unsubscribe from Promos',
            "value" => nil
          },
          {
            "id" => button_components.last.id,
            "position" => 2,
            "type" => 'BUTTON',
            "format" => 'COPY_CODE',
            "text" => '25OFF',
            "value" => nil
          }
        ])
      end
    end

    context 'when invalid params' do
      context 'when facebook returns error' do
        before do
          expect(SubmitWhatsappTemplateJob).not_to receive(:perform_later)
          allow_any_instance_of(WhatsappTemplate).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(WhatsappTemplate.new(status: 'invalid')))
        end

        it 'does not create template' do
          expect { post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json }
            .to change(WhatsappTemplate, :count).by(0)
        end

        it 'returns error' do
          post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

          expect(response.status).to eq(422)
        end
      end

      context 'when component validations fail' do
        before do
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          params['components'].find { |component| component['type'] == 'BODY' }['text'] = ''
          params['components'].find { |component| component['type'] == 'BUTTON' && component['format'] == 'COPY_CODE' }['text'] = '250OFF&'
        end

        it 'returns error' do
          post '/v1/messages/whatsapp-templates/create-and-submit', headers: headers, params: params.to_json

          expect(response.status).to eq(422)
          expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Component text can't be blank, copy code text should only contain alphanumeric characters" })
        end
      end
    end
  end

  describe '#update' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, whatsapp_template_id: '***************') }
    let(:params) do
      parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
      parsed_params['id'] = whatsapp_template.id
      parsed_params['components'].find { |comp| comp['type'] == 'HEADER' }['id'] = whatsapp_template.components.find_by(component_type: 'HEADER').id
      parsed_params['components'].find { |comp| comp['type'] == 'BODY' }['id'] = whatsapp_template.components.find_by(component_type: 'BODY').id
      parsed_params['components'].reject! { |comp| comp['type'] == 'FOOTER' }
      parsed_params['components'].find { |comp| comp['type'] == 'BUTTON' && comp['format'] == 'QUICK_REPLY' }['id'] = whatsapp_template.components.find_by(component_type: 'BUTTON', component_format: 'QUICK_REPLY').id
      parsed_params
    end

    context 'when valid params' do
      before do
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1)
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2)
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3)
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1)
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1)

        expect(SubmitWhatsappTemplateJob).to receive(:perform_later).once
        expect(Publishers::WhatsappTemplateNameUpdatedPublisher).to receive(:call).once
      end

      it 'updates template' do
        expect { put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers, params: params.to_json }
          .to change(WhatsappTemplateComponent.where(component_type: HEADER), :count).by(0)
          .and change(WhatsappTemplateComponent.where(component_type: BODY), :count).by(0)
          .and change(WhatsappTemplateComponent.where(component_type: FOOTER), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON, component_format: 'PHONE_NUMBER'), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON, component_format: 'URL'), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(id: whatsapp_template.components.find_by(component_format: COPY_CODE)), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(id: whatsapp_template.components.find_by(component_format: QUICK_REPLY)), :count).by(0)
          .and change(VariableMapping.where(component_type: BUTTON_URL), :count).by(-1)
      end

      it 'returns serialized template' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers, params: params.to_json

        expect(response.status).to eq(200)
        whatsapp_template.reload
        serialized_template = response.parsed_body
        expect(serialized_template.except('components')).to eq({
          "id" => whatsapp_template.id,
          "name" => params['name'],
          "entityType" => "lead",
          "category" => 'MARKETING',
          "language" => "en",
          "status" => "SUBMITTING",
          "createdAt" => whatsapp_template.created_at.iso8601(3),
          "updatedAt" => whatsapp_template.updated_at.iso8601(3),
          "connectedAccount" => {
            "id" => whatsapp_template.connected_account_id,
            "name" => whatsapp_template.connected_account.display_name,
            "businessName" => whatsapp_template.connected_account.name
          },
          "createdBy" => {
            "id" => user.id,
            "name" => user.name
          },
          "updatedBy" => {
            "id" => user.id,
            "name" => user.name
          },
          'recordActions' => {
            'read' => true,
            'update' => true
          }
        })

        expect(serialized_template['components']).to match_array(
          whatsapp_template.components.map do |wtc|
            {
              "id" => wtc.id,
              "position" => wtc.position,
              "type" => wtc.component_type,
              "format" => wtc.component_format,
              "text" => wtc.component_text,
              "value" => wtc.component_value
            }
          end
        )
      end
    end
    
    context 'when updating template with media header' do
      let(:template_media) { create(:template_media, tenant_id: user.tenant_id) }
      let(:whatsapp_template_with_text_header) { create(:whatsapp_template, connected_account: connected_account) }
      let(:media_header_params) do
        parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
        parsed_params['id'] = whatsapp_template_with_text_header.id
        
        header_component = whatsapp_template_with_text_header.components.find_by(component_type: HEADER)
        body_component = whatsapp_template_with_text_header.components.find_by(component_type: BODY)
        
        parsed_params['components'].find { |comp| comp['type'] == 'HEADER' }['id'] = header_component.id
        parsed_params['components'].find { |comp| comp['type'] == 'BODY' }['id'] = body_component.id
        
        header_comp = parsed_params['components'].find { |comp| comp['type'] == 'HEADER' }
        header_comp['format'] = 'IMAGE'
        header_comp['text'] = nil
        header_comp['value'] = template_media.id
        header_comp['mediaType'] = 'STATIC'
        
        parsed_params
      end
      
      before do
        create(:variable_mapping, whatsapp_template: whatsapp_template_with_text_header, component_type: HEADER, template_variable: 1)
        create(:variable_mapping, whatsapp_template: whatsapp_template_with_text_header, component_type: BODY, template_variable: 1)
        expect(SubmitWhatsappTemplateJob).to receive(:perform_later).once
      end
      
      it 'updates template header to use image format with static media type' do
        expect(Publishers::WhatsappTemplateNameUpdatedPublisher).to receive(:call).once

        put "/v1/messages/whatsapp-templates/#{whatsapp_template_with_text_header.id}", headers: headers, params: media_header_params.to_json
        
        expect(response.status).to eq(200)
        whatsapp_template_with_text_header.reload
        
        header_component = whatsapp_template_with_text_header.components.find_by(component_type: HEADER)
        expect(header_component.component_format).to eq('IMAGE')
        expect(header_component.component_text).to be_nil
        expect(header_component.component_value).to eq(template_media.id.to_s)
        expect(header_component.media_type).to eq('STATIC')
      end
      
      it 'returns serialized header component with media type' do
        expect(Publishers::WhatsappTemplateNameUpdatedPublisher).to receive(:call).once

        put "/v1/messages/whatsapp-templates/#{whatsapp_template_with_text_header.id}", headers: headers, params: media_header_params.to_json
        
        whatsapp_template_with_text_header.reload
        header_component = whatsapp_template_with_text_header.components.find_by(component_type: HEADER)
        serialized_header = response.parsed_body['components'].find { |component| component['type'] == HEADER }
        
        expect(serialized_header).to eq({
          "id" => header_component.id,
          "position" => header_component.position,
          "type" => 'HEADER',
          "format" => 'IMAGE',
          "text" => nil,
          "value" => "#{template_media.id}",
          "mediaType" => 'STATIC'
        })
      end
    end

    context 'when invalid params' do
      context 'when name is duplicated' do
        before { create(:whatsapp_template, connected_account: whatsapp_template.connected_account, name: params['name']) }

        it 'returns error' do
          put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers, params: params.to_json

          expect(response.status).to eq(422)
          expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Name has already been taken" })
        end
      end

      context 'when component validations fail' do
        before do
          params['components'].find { |component| component['type'] == 'BODY' }['text'] = ''
          params['components'].find { |component| component['type'] == 'BUTTON' && component['format'] == 'COPY_CODE' }['text'] = '250OFF&'
        end

        it 'returns error' do
          put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers, params: params.to_json

          expect(response.status).to eq(422)
          expect(response.parsed_body).to eq({ "errorCode" => "022020", "message" => "Component text can't be blank, copy code text should only contain alphanumeric characters" })
        end
      end
    end

    context 'when user does not have access to template' do
      before do
        whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
      end

      it 'returns error' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: headers, params: params.to_json

        expect(response.status).to eq(404)
        expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => nil })
      end
    end

    context 'when user does not have permission to update' do
      it 'returns error' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: no_whatsapp_permission_headers, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}", headers: invalid_headers, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#deactivate' do
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: create(:connected_account, created_by: user), whatsapp_template_id: '***************', status: REJECTED) }

    context 'when valid request' do
      it 'returns status 200 and deactivates template' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/deactivate", headers: headers

        expect(response.status).to eq(200)
        whatsapp_template.reload
        expect(whatsapp_template.status).to eq('INACTIVE')
        expect(whatsapp_template.updated_by).to eq(user)
      end
    end

    context 'when user does not have access to template' do
      before do
        whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
      end

      it 'returns error' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/deactivate", headers: headers

        expect(response.status).to eq(404)
        expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => nil })
      end
    end

    context 'when user does not have permission to update' do
      it 'returns error' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/deactivate", headers: no_whatsapp_permission_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/deactivate", headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#variables' do
    context 'when requested for valid entity' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
          status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
        )

        stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
      end

      it 'returns lead variables' do
        get '/v1/messages/whatsapp-templates/lead/variables', headers: headers

        expect(response).to be_ok
        expect(response.parsed_body.count).to eq(132)
      end
    end

    context 'when requested for invalid entity' do
      it 'returns error' do
        get '/v1/messages/whatsapp-templates/invalid/variables', headers: headers
        expect(response.parsed_body['errorCode']).to eq('022003')
        expect(response.parsed_body['message']).to eq('Invalid Entity')
      end
    end
  end

  describe '#lookup' do
    before do
      @connected_account = create(:connected_account, created_by: user)
      @template = create(:whatsapp_template, connected_account: @connected_account, name: "Lead Template approved", status: APPROVED)
      create(:whatsapp_template, connected_account: create(:connected_account, created_by: user), name: "Another Lead Template approved", status: APPROVED)
      create(:whatsapp_template, connected_account: @connected_account, name: "Lead Template unapproved")
      create(:whatsapp_template, connected_account: @connected_account, name: "Contact Template", entity_type: LOOKUP_CONTACT)
    end

    context 'when requested with data' do
      it 'returns whatsapp templates' do
        get "/v1/messages/whatsapp-templates/lookup?connectedAccountId=#{@connected_account.id}&entityType=lead&q=lead%20template", headers: headers
        expect(response.parsed_body).to eq({ "content" => [{"id"=> @template.id, "name"=>"Lead Template approved"}] })
      end
    end
  end

  describe '#preview' do
    context 'when requested preview' do
      let(:connected_account) { create(:connected_account, created_by: user) }
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: user, status: 'APPROVED') }

      before do
        whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        auth_data = User::TokenParser.parse(valid_auth_token.token)
        permissions = auth_data.permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy customFieldValues lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

        user_resp = JSON.parse(file_fixture('get_users_response.json').read)
        user_resp['content'][2]['id'] = user.id
        stub_request(:post, "http://localhost:8081/v1/users/search").with(
          body: {
            fields: %w[timezone dateFormat firstName lastName],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: "4010,#{user.id}"
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: user_resp.to_json, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})
      end

      it 'returns replaced data' do
        get "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/preview/lead/34343", headers: headers
        expect(response.parsed_body['components'].find { |component| component['type'] == 'BODY' }['text']).to eq("TEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK")
        expect(response.parsed_body['components'].find { |component| component['format'] == 'URL' }['value']).to eq('Shubham')
      end
    end
  end

  describe '#send_message' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let!(:deal_whatsapp_template) { create(:whatsapp_template, entity_type: LOOKUP_DEAL, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }
    let(:contact_user_share_rule) { create(:share_rule, share_rule_id: 3659, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'CONTACT', actions: { 'sms': true }) }
  
    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')

      deal_whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}}')
      deal_whatsapp_template.components.where(component_type: [FOOTER, BUTTON]).destroy_all

      create(:variable_mapping, whatsapp_template: deal_whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'name')
      create(:variable_mapping, whatsapp_template: deal_whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'name')

      allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
      auth_data = User::TokenParser.parse(valid_auth_token.token)

      permissions = auth_data.permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
      stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
        body: {
          fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy customFieldValues lastName ownerId],
          jsonRule: {
            rules: [
              {
                operator: 'equal',
                id: 'id',
                field: 'id',
                type: 'double',
                value: 34343
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{token_without_pid}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][2]['id'] = user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[timezone dateFormat firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: "4010,#{user.id}"
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})

      stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: {
            records: { used: 100, total: 1000 },
            storage: { used: 0.5, total: 2.0 }
          }.to_json, headers: {})
    end

    context 'when requested for send message' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send", headers: headers, params: { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 }.to_json

        message = Message.last
        expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShubham\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end

    context 'when two url buttons and two url variable mappings' do
      before do
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 0, component_text: 'Refer Now')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 0,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Open'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send", headers: headers, params: { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 }.to_json

        message = Message.last
        expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nRefer Now\nCall Us\nShubham\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
      end
    end

    context 'when using templateEntity and templateEntityId for template variable replacement' do
      before do
        auth_data = User::TokenParser.parse(valid_auth_token.token)

        permissions = auth_data.permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call

        # Mock the deal entity data fetch for template variable replacement
        stub_request(:post, 'http://localhost:8083/v1/search/deal').with(
          body: {
            fields: %w[name phoneNumbers customFieldValues firstName lastName],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 123
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_deal_response.json').read, headers: {})

        # Mock the contact entity data fetch for conversation tracking
        stub_request(:post, 'http://localhost:8083/v1/search/contact').with(
          body: {
            fields: %w[phoneNumbers firstName lastName ownerId customFieldValues],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_contact_response.json').read, headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: deal_whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'primary'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'primary'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        contact_user_share_rule
      end

      it 'sends message using templateEntity and templateEntityId for variable replacement' do
        post "/v1/messages/whatsapp-templates/#{deal_whatsapp_template.id}/send", headers: headers, params: {
          id: deal_whatsapp_template.id,
          entityType: 'contact',
          entityId: 34343,
          templateEntity: 'deal',
          templateEntityId: 123,
          phoneId: 207783
        }.to_json

        message = Message.last
        expect(message.content).to include('primary')
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
        # Verify that the message is still associated with the conversation entity (contact) for conversation tracking
        lookup = message.related_to.first
        expect(lookup.entity_type).to eq('contact')
        expect(lookup.entity_id).to eq(34343)
      end
    end
  end

  describe '#send_bulk_message' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let!(:deal_whatsapp_template) { create(:whatsapp_template, entity_type: LOOKUP_DEAL, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }
    let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
    let(:contact_user_share_rule) { create(:share_rule, share_rule_id: 3659, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'CONTACT', actions: { 'sms': true }) }

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')

      deal_whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}}')
      deal_whatsapp_template.components.where(component_type: [FOOTER, BUTTON]).destroy_all

      create(:variable_mapping, whatsapp_template: deal_whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'name')
      create(:variable_mapping, whatsapp_template: deal_whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'deal', entity: 'deal', internal_name: 'name')

      allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
      auth_data = User::TokenParser.parse(valid_auth_token.token)

      permissions = auth_data.permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
      stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
        body: {
          fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy lastName ownerId],
          jsonRule: {
            rules: [
              {
                operator: 'equal',
                id: 'id',
                field: 'id',
                type: 'double',
                value: 34343
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{token_without_pid}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][1]['id'] = 7638
      user_resp['content'][2]['id'] = user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[timezone dateFormat firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: "7638,#{user.id}"
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})

      stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: {
            records: { used: 100, total: 1000 },
            storage: { used: 0.5, total: 2.0 }
          }.to_json, headers: {})
    end

    context 'when requested for send bulk message' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  "type": "header",
                  "parameters": [{
                    "type": "text",
                    "text": "MaskedPhone312"
                  }]
                },
                {
                  "type": "body",
                  "parameters": [{
                      "type": "text",
                      "text": "MaskedPhone312"
                    },
                    {
                      "type": "text",
                      "text": "+91 **********"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "0.0"
                    }
                  ]
                },
                {
                  "type": "button",
                  "sub_type": "copy_code",
                  "index": 2,
                  "parameters": [{
                    "type": "coupon_code",
                    "coupon_code": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 3,
                  "parameters": [{
                    "type": "text",
                    "text": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "quick_reply",
                  "index": 4,
                  "parameters": [{
                    "type": "payload",
                    "payload": "Unsubcribe from Promos"
                  }]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
          headers: headers,
          params: {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details,
            bulk_job: false
          }.to_json

        message = Message.last

        expect(message.content).to eq("Our MaskedPhone312 is on!\nTEXT_FIELD - MaskedPhone312 || PHONE - +91 ********** || SYSTEM_PICK_LIST - John || EMAIL - John || URL - John || PIPELINE - John || LOOKUP - John || NUMBER - John || DATE_PICKER - John || DATETIME_PICKER - John || MULTI_PICKLIST - John || fallback_value - 0.0\nUse the buttons below to manage your marketing subscriptions\nCall Us\nAjit\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end

      context 'when campaignId and activityId are present in the params' do
        it 'creates a message along with campaign_info' do
          post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
            headers: headers,
            params: {
              id: whatsapp_template.id,
              entityType: 'lead',
              entityId: 34343,
              phoneNumber: {
                "code": "IN",
                "dialCode": "+91",
                "id": 216106,
                "type": "WORK",
                "value": "**********",
                "primary": true
              },
              entityData: entity_details,
              bulk_job: false,
              campaignId: 12345,
              activityId: 67890
            }.to_json

          message = Message.last

          expect(message.campaign_info).to be_present
          expect(message.campaign_info['campaignId']).to eq(12345)
          expect(message.campaign_info['activityId']).to eq(67890)
        end
      end
    end

    context 'when two url buttons and two url variable mappings' do
      before do
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 0, component_text: 'Refer Now')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  "type": "header",
                  "parameters": [{
                    "type": "text",
                    "text": "MaskedPhone312"
                  }]
                },
                {
                  "type": "body",
                  "parameters": [{
                      "type": "text",
                      "text": "MaskedPhone312"
                    },
                    {
                      "type": "text",
                      "text": "+91 **********"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "0.0"
                    }
                  ]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 0,
                  "parameters": [{
                    "type": "text",
                    "text": "John"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "copy_code",
                  "index": 2,
                  "parameters": [{
                    "type": "coupon_code",
                    "coupon_code": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 3,
                  "parameters": [{
                    "type": "text",
                    "text": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "quick_reply",
                  "index": 4,
                  "parameters": [{
                    "type": "payload",
                    "payload": "Unsubcribe from Promos"
                  }]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
          headers: headers,
          params: {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details
          }.to_json

        message = Message.last

        expect(message.content).to eq("Our MaskedPhone312 is on!\nTEXT_FIELD - MaskedPhone312 || PHONE - +91 ********** || SYSTEM_PICK_LIST - John || EMAIL - John || URL - John || PIPELINE - John || LOOKUP - John || NUMBER - John || DATE_PICKER - John || DATETIME_PICKER - John || MULTI_PICKLIST - John || fallback_value - 0.0\nUse the buttons below to manage your marketing subscriptions\nRefer Now\nCall Us\nAjit\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end

    context 'when using templateEntity and templateEntityId for bulk message' do
      before do
        auth_data = User::TokenParser.parse(valid_auth_token.token)

        permissions = auth_data.permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
        stub_request(:post, 'http://localhost:8083/v1/search/contact').with(
          body: {
            fields: ["phoneNumbers","firstName","lastName","ownerId","customFieldValues"],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_contact_response.json').read, headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: deal_whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'deal first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'deal first name'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        contact_user_share_rule
      end

      it 'sends bulk message using templateEntity and templateEntityId for variable replacement' do
        deal_entity_details = entity_details.dup
        deal_entity_details['name'] = 'deal first name'

        post "/v1/messages/whatsapp-templates/#{deal_whatsapp_template.id}/send-bulk-message",
          headers: headers,
          params: {
            id: deal_whatsapp_template.id,
            entityType: 'contact',
            entityId: 34343,
            templateEntity: 'deal',
            templateEntityId: 123,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: deal_entity_details,
            bulk_job: false
          }.to_json

        message = Message.last
        expect(message.content).to include('deal first name')
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
        # Verify that the message is still associated with the conversation entity (contact) for conversation tracking
        lookup = message.related_to.first
        expect(lookup.entity_type).to eq('contact')
        expect(lookup.entity_id).to eq(34343)
      end
    end
  end

  describe '#sync_status' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, whatsapp_template_id: '***************') }

    context 'when valid params' do
      before do
        stub_request(:get, "https://graph.facebook.com/v19.0/#{whatsapp_template.whatsapp_template_id}")
        .with(
          headers: {
            Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
          }
        )
        .to_return(status: 200, body: { status: APPROVED }.to_json)
      end

      it 'updates template status' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/sync-status", headers: headers

        expect(whatsapp_template.reload.status).to eq(APPROVED)
      end
    end

    context 'when user does not have access to template' do
      before do
        whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
      end

      it 'returns error' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/sync-status", headers: headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        put "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/sync-status", headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#send_message' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')

      allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
      auth_data = User::TokenParser.parse(valid_auth_token.token)

      permissions = auth_data.permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
      stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
        body: {
          fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy customFieldValues lastName ownerId],
          jsonRule: {
            rules: [
              {
                operator: 'equal',
                id: 'id',
                field: 'id',
                type: 'double',
                value: 34343
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{token_without_pid}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][2]['id'] = user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[timezone dateFormat firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: "4010,#{user.id}"
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: {
            records: { used: 100, total: 1000 },
            storage: { used: 0.5, total: 2.0 }
          }.to_json, headers: {})
    end

    context 'when requested for send message' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send", headers: headers, params: { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 }.to_json

        message = Message.last
        expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShubham\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end

    context 'when two url buttons and two url variable mappings' do
      before do
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 0, component_text: 'Refer Now')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 0,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Open'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send", headers: headers, params: { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 }.to_json

        message = Message.last
        expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nRefer Now\nCall Us\nShubham\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
      end
    end

     it 'passes is_manual as true to Whatsapp_temaplte' do
        expect(WhatsappTemplateService).to receive(:new).with(hash_including(is_manual: true)).and_call_original

        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send", headers: headers, params: { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 }.to_json
     end
    end

  describe '#send_bulk_message' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }
    let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')

      allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
      auth_data = User::TokenParser.parse(valid_auth_token.token)

      permissions = auth_data.permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
      stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
        body: {
          fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy lastName ownerId],
          jsonRule: {
            rules: [
              {
                operator: 'equal',
                id: 'id',
                field: 'id',
                type: 'double',
                value: 34343
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{token_without_pid}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][1]['id'] = 7638
      user_resp['content'][2]['id'] = user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[timezone dateFormat firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: "7638,#{user.id}"
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})

      stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants/usage").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: {
            records: { used: 100, total: 1000 },
            storage: { used: 0.5, total: 2.0 }
          }.to_json, headers: {})
    end

    context 'when requested for send bulk message' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  "type": "header",
                  "parameters": [{
                    "type": "text",
                    "text": "MaskedPhone312"
                  }]
                },
                {
                  "type": "body",
                  "parameters": [{
                      "type": "text",
                      "text": "MaskedPhone312"
                    },
                    {
                      "type": "text",
                      "text": "+91 **********"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "0.0"
                    }
                  ]
                },
                {
                  "type": "button",
                  "sub_type": "copy_code",
                  "index": 2,
                  "parameters": [{
                    "type": "coupon_code",
                    "coupon_code": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 3,
                  "parameters": [{
                    "type": "text",
                    "text": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "quick_reply",
                  "index": 4,
                  "parameters": [{
                    "type": "payload",
                    "payload": "Unsubcribe from Promos"
                  }]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
          headers: headers,
          params: {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details,
            bulk_job: false
          }.to_json

        message = Message.last

        expect(message.content).to eq("Our MaskedPhone312 is on!\nTEXT_FIELD - MaskedPhone312 || PHONE - +91 ********** || SYSTEM_PICK_LIST - John || EMAIL - John || URL - John || PIPELINE - John || LOOKUP - John || NUMBER - John || DATE_PICKER - John || DATETIME_PICKER - John || MULTI_PICKLIST - John || fallback_value - 0.0\nUse the buttons below to manage your marketing subscriptions\nCall Us\nAjit\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end

      context 'when campaignId and activityId are present in the params' do
        it 'creates a message along with campaign_info' do
          post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
            headers: headers,
            params: {
              id: whatsapp_template.id,
              entityType: 'lead',
              entityId: 34343,
              phoneNumber: {
                "code": "IN",
                "dialCode": "+91",
                "id": 216106,
                "type": "WORK",
                "value": "**********",
                "primary": true
              },
              entityData: entity_details,
              bulk_job: false,
              campaignId: 12345,
              activityId: 67890
            }.to_json

          message = Message.last

          expect(message.campaign_info).to be_present
          expect(message.campaign_info['campaignId']).to eq(12345)
          expect(message.campaign_info['activityId']).to eq(67890)
        end
      end
    end
    
    context 'when chat bot is in progress' do
      before do
        conversation.update!(chatbot_conversation_completed: false, chatbot_conversation_id: 1)
      end

      it 'does not send a message and returns error' do
        expect {
          post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
            headers: headers,
            params: {
              id: whatsapp_template.id,
              entityType: 'lead',
              entityId: 34343,
              phoneNumber: {
                "code": "IN",
                "dialCode": "+91",
                "id": 216106,
                "type": "WORK",
                "value": "**********",
                "primary": true
              },
              entityData: entity_details,
              bulk_job: false,
              campaignId: 12345,
              activityId: 67890
            }.to_json
        }.to change(Message, :count).by(0)

        expect(response.status).to eq(409)
        expect(response.parsed_body).to eq({
          "errorCode" => "022034",
          "message" => "Cannot send message as chatbot is in progress"
        })
      end
    end

    context 'when two url buttons and two url variable mappings' do
      before do
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 0, component_text: 'Refer Now')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  "type": "header",
                  "parameters": [{
                    "type": "text",
                    "text": "MaskedPhone312"
                  }]
                },
                {
                  "type": "body",
                  "parameters": [{
                      "type": "text",
                      "text": "MaskedPhone312"
                    },
                    {
                      "type": "text",
                      "text": "+91 **********"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "John"
                    },
                    {
                      "type": "text",
                      "text": "0.0"
                    }
                  ]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 0,
                  "parameters": [{
                    "type": "text",
                    "text": "John"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "copy_code",
                  "index": 2,
                  "parameters": [{
                    "type": "coupon_code",
                    "coupon_code": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "url",
                  "index": 3,
                  "parameters": [{
                    "type": "text",
                    "text": "Ajit"
                  }]
                },
                {
                  "type": "button",
                  "sub_type": "quick_reply",
                  "index": 4,
                  "parameters": [{
                    "type": "payload",
                    "payload": "Unsubcribe from Promos"
                  }]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends message' do
        post "/v1/messages/whatsapp-templates/#{whatsapp_template.id}/send-bulk-message",
          headers: headers,
          params: {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details
          }.to_json

        message = Message.last

        expect(message.content).to eq("Our MaskedPhone312 is on!\nTEXT_FIELD - MaskedPhone312 || PHONE - +91 ********** || SYSTEM_PICK_LIST - John || EMAIL - John || URL - John || PIPELINE - John || LOOKUP - John || NUMBER - John || DATE_PICKER - John || DATETIME_PICKER - John || MULTI_PICKLIST - John || fallback_value - 0.0\nUse the buttons below to manage your marketing subscriptions\nRefer Now\nCall Us\nAjit\nShop Now\nUnsubcribe from Promos")
        expect(message.direction).to eq('outgoing')
        expect(message.status).to eq('sending')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.remote_id).to eq('wamid.ID')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end
  end

  describe '#sync' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:headers) { valid_headers(valid_auth_token) }

    it 'calls FetchWhatsappTemplatesFromMetaService and returns ok' do
      expect_any_instance_of(FetchWhatsappTemplatesFromMetaService).to receive(:fetch).once.and_return(true)

      post '/v1/messages/whatsapp-templates/sync', params: { entityType: 'lead', connectedAccountId: connected_account.id }.to_json, headers: headers

      expect(response.status).to eq(200)
      expect(response.body).to eq("")
    end
  end

  describe '#delete_all_templates' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:another_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: another_user) }

    context 'when connected account has templates' do
      let!(:template1) { create(:whatsapp_template, connected_account: connected_account, name: 'Template 1') }
      let!(:template2) { create(:whatsapp_template, connected_account: connected_account, name: 'Template 2') }
      let!(:template3) { create(:whatsapp_template, connected_account: another_connected_account, name: 'Template 3') }

      before do
        create(:variable_mapping, whatsapp_template: template1, component_type: HEADER, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template1, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template2, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template3, component_type: HEADER, template_variable: 1)
      end

      it 'deletes all templates belonging to the connected account' do
        expect {
          delete "/v1/messages/connected-accounts/#{connected_account.id}/templates"
        }.to change(WhatsappTemplate, :count).by(-2)
         .and change(WhatsappTemplateComponent, :count).by(-14)
         .and change(VariableMapping, :count).by(-3)

        expect(response.status).to eq(200)
        expect(response.body).to eq("")
      end
    end
  end

  describe '#retry_message_delivery' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let!(:message) { create(:message, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, whatsapp_template_id: whatsapp_template.id, status: 'sending') }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }
    let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
    let(:contact_user_share_rule) { create(:share_rule, share_rule_id: 3659, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'CONTACT', actions: { 'sms': true }) }

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'test field')
      whatsapp_template.components.where(component_type: [HEADER, FOOTER, BUTTON]).destroy_all

      allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
      auth_data = User::TokenParser.parse(valid_auth_token.token)

      permissions = auth_data.permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
      stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
        body: {
          fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy lastName ownerId],
          jsonRule: {
            rules: [
              {
                operator: 'equal',
                id: 'id',
                field: 'id',
                type: 'double',
                value: 34343
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{token_without_pid}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

      stub_request(:get, "http://localhost:8081/v1/tenants").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

      user_resp = JSON.parse(file_fixture('get_users_response.json').read)
      user_resp['content'][1]['id'] = 7638
      user_resp['content'][2]['id'] = user.id
      stub_request(:post, "http://localhost:8081/v1/users/search").with(
        body: {
          fields: %w[timezone dateFormat firstName lastName],
          jsonRule: {
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: "7638,#{user.id}"
              }
            ],
            condition: 'AND',
            valid: true
          }
        }.to_json,
        headers: {
          'Accept'=>'application/json',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          'Content-Type'=>'application/json',
        }
      ).to_return(status: 200, body: user_resp.to_json, headers: {})

      stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
        headers: {
        'Accept'=>'*/*',
        'Authorization'=>"Bearer #{valid_auth_token.token}",
        }
      ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})
    end

    context 'when requested for retry message' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  "type": "header",
                  "parameters": []
                },
                {
                  "type": "body",
                  "parameters": []
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

        user_share_rule
      end

      it 'retries message delivery' do
        put "/v1/messages/whatsapp/#{message.id}/retry",
          headers: headers,
          params: {
            whatsappTemplateId: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details,
            bulk_job: false
          }.to_json

        expect(message.reload.status).to eq('sending')
        expect(response.body).to eq({ id: message.id }.to_json)
      end
    end
  end
end
