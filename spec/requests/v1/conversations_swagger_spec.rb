require 'swagger_helper'

RSpec.describe 'Conversations', type: :request do
  let(:user){ create(:user) }
  let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization){ valid_auth_token.token }

  path '/v1/messages/conversations/{id}/search' do
    post 'returns messages in the conversation' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id).id }

      response '200', 'Returns messages in the given conversation with entity permissions' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:jsonRule) { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_to_lookup", id: "related_to", field: "related_to", value: { id: 34343, entity: LOOKUP_LEAD } }] } } }

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:get, "http://localhost:8086/v1/entities/lead/masked-fields")
          .with(
            headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:jsonRule) { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_to_lookup", id: "related_to", field: "related_to", value: { id: 34343, entity: LOOKUP_LEAD } }] } } }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/conversations/search' do
    post 'returns conversations' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id).id }

      response '200', 'Returns conversations for given user' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:jsonRule) { { jsonRule: nil } }

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:jsonRule) { { jsonRule: nil } }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/conversations/{id}/permissions' do
    get 'returns entity permissions for a conversation' do
      tags 'Message'
      security [ bearerAuth: [] ]
      produces 'application/json'

      parameter name: :id, in: :path, type: :integer, required: true, description: 'Conversation ID'
      parameter name: :entityType, in: :query, type: :string, required: true, description: 'Entity type (e.g., lead, contact)'
      parameter name: :entityId, in: :query, type: :string, required: true, description: 'Entity ID'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id).id }
      let(:entityType) { 'lead' }
      let(:entityId) { '123' }

      response '200', 'Returns entity permissions for the conversation' do
        schema type: :object,
          properties: {
            isEntityAccessible: { type: :boolean },
            isPhoneNumberPresent: { type: :boolean }
          },
          required: ['isEntityAccessible', 'isPhoneNumberPresent']

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 123
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '422', 'Conversation not found' do
        let(:id) { '999999' }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations/{id}' do
    delete 'deletes a conversation' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer, required: true, description: 'Conversation ID'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:valid_auth_token){ build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id).id }

      response '200', 'Conversation soft deleted successfully' do
        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '403', 'Permission denied' do
        let(:unauthorized_user) { create(:user) }
        let(:unauthorized_auth_token) { build(:auth_token, :without_sms_permission, user_id: unauthorized_user.id, tenant_id: unauthorized_user.tenant_id) }
        let(:Authorization) { unauthorized_auth_token.token }

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(unauthorized_auth_token.token)
        end

        run_test!
      end

      response '422', 'Conversation not found' do
        let(:id) { 'invalid_id' }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations' do
    post 'create or fetch a conversation' do
      tags 'Message'
      consumes 'application/json'
      produces 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        in: :header,
        type: :string,
        name: :Authorization,
        required: true,
        description: 'User token'
      })

      parameter name: :body, in: :body, schema: {
        type: :object,
        properties: {
          lastConversationId: { type: :integer, description: 'ID of the last conversation (required)' },
          connectedAccountId: { type: :integer, description: 'ID of the connected account (required)' },
          entityId: { type: :integer, description: 'Entity ID (optional, for entity-based conversations)' },
          entityType: { type: :string, description: 'Entity type (optional, for entity-based conversations)' }
        },
        required: [ 'lastConversationId', 'connectedAccountId' ]
      }

      response '200', 'Returns the created or fetched conversation ID' do
        schema type: :object, properties: { id: { type: :integer } }, required: ['id']
        let(:user) { create(:user) }
        let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
        let(:Authorization) { valid_auth_token.token }
        let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
        let(:last_conversation) { create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
        let(:body) { { lastConversationId: last_conversation.id, connectedAccountId: connected_account.id } }
        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { 'Bearer invalid' }
        let(:body) { { lastConversationId: 1, connectedAccountId: 1 } }
        run_test!
      end

      response '403', 'Permission denied' do
        let(:user) { create(:user) }
        let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
        let(:Authorization) { valid_auth_token.token }
        let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
        let(:second_connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
        let(:last_conversation) { create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
        let(:body) { { lastConversationId: last_conversation.id, connectedAccountId: second_connected_account.id } }
        before do
          allow_any_instance_of(User).to receive(:can_create_conversation?).and_return(false)
        end
        run_test!
      end

      response '404', 'Connected account not found or inactive' do
        let(:user) { create(:user) }
        let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
        let(:Authorization) { valid_auth_token.token }
        let(:last_conversation) { create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id: 9999, owner_id: user.id) }
        let(:body) { { lastConversationId: last_conversation.id, connectedAccountId: 9999 } }
        run_test!
      end

      response '422', 'Conversation not found' do
        let(:user) { create(:user) }
        let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
        let(:Authorization) { valid_auth_token.token }
        let(:body) { { lastConversationId: 999999, connectedAccountId: 1 } }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations/by-entity' do
    post 'returns conversations by entity' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :body, in: :body, schema: {
        type: :object,
        properties: {
          entityType: { type: :string, description: 'Entity type (e.g., lead, contact)' },
          entityId: { type: :integer, description: 'Entity ID' },
          entityName: { type: :string, description: 'Name of the entity' },
          phoneId: { type: :integer, description: 'ID of the phone number (optional)' }
        },
        required: ['entityType', 'entityId', 'entityName']
      }

      response '200', 'Returns conversations for the given entity' do
        let(:user) { create(:user) }
        let(:valid_auth_token) { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
        let(:Authorization) { valid_auth_token.token }
        let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
        let(:body) { { entityType: 'lead', entityId: 34343, entityName: 'Test Entity', phoneId: 207783 } }
        let!(:conversation) { create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:body) { { entityType: 'lead', entityId: 123, entityName: 'Test Entity' } }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations/{id}/complete' do
    put 'marks a conversation as completed' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer, required: true, description: 'Conversation ID'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id, status: IN_PROGRESS).id }
      let(:sub_conversation){ create(:sub_conversation, conversation_id: id, status: IN_PROGRESS) }

      response '200', 'Conversation marked as completed successfully' do
        schema type: :boolean

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations/{id}/unread' do
    put 'marks a conversation as unread' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer, required: true, description: 'Conversation ID'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id, is_read: true).id }

      response '200', 'Conversation marked as unread successfully' do
        schema type: :object,
          properties: {
            id: { type: :integer },
            isRead: { type: :boolean }
          },
          required: ['id', 'isRead']

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '422', 'Conversation not found' do
        let(:id) { '999999' }
        run_test!
      end
    end
  end

  path '/v1/messages/conversations/{id}/read' do
    put 'marks a conversation as read' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer, required: true, description: 'Conversation ID'

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id){ create(:conversation, phone_number: '+919942384499', tenant_id: user.tenant_id, owner_id: user.id, is_read: false).id }

      response '200', 'Conversation marked as read successfully' do
        schema type: :object,
          properties: {
            id: { type: :integer },
            isRead: { type: :boolean }
          },
          required: ['id', 'isRead']

        before do
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '422', 'Conversation not found' do
        let(:id) { '999999' }
        run_test!
      end
    end
  end
end
