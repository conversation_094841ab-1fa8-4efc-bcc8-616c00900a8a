require 'swagger_helper'

RSpec.describe 'ConnectedAccounts', type: :request do
  let(:user)              { create(:user, tenant_id: 99) }
  let(:valid_auth_token)  { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }
  let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }

  path '/v1/messages/connected-accounts' do
    get 'Lists Connected Accounts' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      response '200', 'Return Connected Accounts' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}' do
    get 'Get Connected Account Details' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer, required: true
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let!(:connected_account) { create(:connected_account, created_by: user) }
      let(:id) { connected_account.id }

      response '200', 'Connected Account Details' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { connected_account.id + 1 }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts' do
    post 'Create Connected Account' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :data, in: :body, schema: {
        type: :object,
        required: %w[phoneNumberId wabaId authCode],
        properties: {
          phoneNumberId: {
            type: :number
          },
          wabaId: {
            type: :number
          },
          authCode: {
            type: :number
          }
        }
      }

      let(:data) { { phoneNumberId: '***************', wabaId: '1234', authCode: '1234' } }

      response '201', 'Creates Connected Account' do
        let(:access_token_response) do
          JSON.parse({
            access_token: 'EAANNzXrhnuwBOZBY9ZBeCTZAm7AZA7woZA1WnBYwhVq0vyZCrYbGX2akZCTflnGWRydEaOCGx57ZCz00yIRSoJ1iZBcyMyB9u9jKZCRbq7V6RakLZARK4Bi9BLXlbI8dWGCS83t7V7DK1FaRiLTZBeZACY7gX07OGPZAKkXEnBzgpZAzV5JIcLw82HcmmlihE0LJavZC8BXcYP3iqulIqPYrscdmbQW7NYPpUvjksadbsjakdbsajdbaskdbaskjdbaskdbaskjdb',
            token_type: 'bearer'
          }.to_json)
        end

        let(:phone_number_response) do
          JSON.parse({
            verified_name: 'Kylas Demo Test',
            code_verification_status: 'VERIFIED',
            display_phone_number: '+91 70302 40148',
            quality_rating: 'UNKNOWN',
            platform_type: 'NOT_APPLICABLE',
            throughput: {
              level: 'NOT_APPLICABLE'
            },
            last_onboarded_time: '2024-02-28T07:29:01+0000',
            id: '***************'
          }.to_json)
        end

        before do
          facebook_auth_response = Facebook::Response.new(
            status_code: '200',
            body: access_token_response
          )

          facebook_phone_number_response = Facebook::Response.new(
            status_code: '200',
            body: phone_number_response
          )
          expect_any_instance_of(Facebook::AuthCode).to receive(:exchange).and_return(facebook_auth_response)
          expect_any_instance_of(Facebook::PhoneNumber).to receive(:find).and_return(facebook_phone_number_response)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}' do
    put 'Update Connected Account' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :id, in: :path, type: :string, required: true

      parameter name: :data, in: :body, schema: {
        type: :object,
        required: %w[displayName],
        properties: {
          displayName: {
            type: :string
          },
          entitiesToCreate: {
            type: :array
          }
        }
      }

      let(:data) { { displayName: 'Connected Account Update' } }
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      response '200', 'Updates Connected Account' do
        let(:id) { connected_account.id }

        before do
          stub_request(:post, "https://api.interakt.ai/v1/organizations/tp-signup/")
            .with(
              headers: {
                content_type: 'application/json',
                Authorization: 'partner-token'
              },
              body: {
                entry: [
                  {
                    changes: [
                      {
                        value: {
                          event: 'PARTNER_ADDED',
                          waba_info: {
                            waba_id: connected_account&.waba_id,
                            solution_id: 'solution-id',
                            phone_number: connected_account&.waba_number
                          }
                        }
                      }
                    ]
                  }
                ],
                object: 'tech_partner'
              }.to_json
            ).to_return(status: 200, body: file_fixture('interakt/onboarding/waba-onboarding-api-success.json'))
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { connected_account.id }

        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { connected_account.id + 1 }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/activate' do
    post 'Activates Connected Account' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :id, in: :path, type: :string, required: true

      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, interakt_onboarding_status: WABA_ONBOARDED) }
      let(:id) { connected_account.id }

      response '200', 'Connected account activated' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { connected_account.id + 1 }

        run_test!
      end

      response '422', 'Failed to activate account' do
        before { connected_account.update(interakt_onboarding_status: WABA_ONBOARDING_FAILED) }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/request-code' do
    post 'Requests Code for Connected Account' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :id, in: :path, type: :string, required: true
      parameter name: :request_data, in: :body, schema: {
        type: :object,
        required: %w[codeMethod],
        properties: {
          codeMethod: { type: :string, enum: %w[SMS VOICE] },
          language: { type: :string }
        }
      }

      let(:codeMethod) { 'SMS' }
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
      let(:request_data) { { codeMethod: codeMethod, language: 'en' } }
      let(:id) { connected_account.id }

      response '200', 'Verification code requested successfully' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/request_code")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: {
                code_method: 'SMS',
                language: 'en'
              }
            )
            .to_return(status: 200, body: { success: true }.to_json)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { connected_account.id + 1 }

        run_test!
      end

      response '422', 'Invalid data' do
        let(:codeMethod) { 'sms' }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/verify-code' do
    post 'Verify Code for Connected Account' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :id, in: :path, type: :string, required: true
      parameter name: :verification_data, in: :body, schema: {
        type: :object,
        required: %w[otpCode],
        properties: {
          otpCode: { type: :integer }
        }
      }

      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
      let(:verification_data) { { otpCode: 123456 } }
      let(:id) { connected_account.id }

      response '200', 'Phone Number verified successfully' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: {
                code: 123456
              }
            )
            .to_return(status: 200, body: { success: true }.to_json)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { connected_account.id + 1 }

        run_test!
      end

      response '422', 'Invalid code' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: {
                code: 123456
              }
            )
            .to_return(status: 400, body: file_fixture('facebook/phone_number/verify-code-error.json').read)
        end

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/lookup' do
    get 'Connected Accounts Lookup' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :q, type: :string, in: :query
      parameter name: :view, type: :string, in: :query
      parameter name: :entityType, type: :string, in: :query

      let(:q) { 'Query' }
      let(:view) { 'billing' }
      let(:entityType) { 'lead'}

      response '200', 'Connected Accounts Lookup' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/{entity_type}/{entity_id}/phone-numbers' do
    get 'Entity phone numbers with session details' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :id, in: :path, type: :string, required: true
      parameter name: :entity_type, in: :path, type: :string, required: true
      parameter name: :entity_id, in: :path, type: :string, required: true

      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active') }

      response '200', 'Phone numbers with session details' do
        let(:id) { connected_account.id }
        let(:entity_type) { LOOKUP_LEAD }
        let(:entity_id) { 34343 }
        let!(:first_user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }

        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})


          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
            headers: {
              'Authorization' => "Bearer #{ valid_auth_token.token }"
            }
          ).to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:id) { connected_account.id }
        let(:entity_type) { LOOKUP_LEAD }
        let(:entity_id) { 34343 }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account Not Found' do
        let(:id) { -1 }
        let(:entity_type) { LOOKUP_LEAD }
        let(:entity_id) { 34343 }

        run_test!
      end
    end
  end
end
