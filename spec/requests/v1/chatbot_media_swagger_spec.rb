# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'ChatbotMedia', type: :request do
  let(:user)                          { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let!(:Authorization)   { valid_auth_token.token }
  let(:chatbot_media) { create(:chatbot_media, file_name: "tenant_#{user.tenant_id}/connetced_account_#{connected_account.id}/file/ab/c.png", tenant_id: user.tenant_id) }

  path '/v1/messages/connected-accounts/{connected_account_id}/chatbot-media' do
    post 'Upload Chatbot Media' do
      tags 'Message'
      security [ bearerAuth: [] ]
      consumes 'multipart/form-data'

      parameter name: :connected_account_id, in: :path, type: :integer
      parameter name: :chatbotId, in: :formData, type: :string, required: :true
      parameter name: :mediaType, in: :formData, type: :string, required: :true
      parameter name: :mediaFile, in: :formData, schema: {
        type: :file
      }
     
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:mediaFile) { sample_png_3mb }
      let(:connected_account_id) { connected_account.id }
      let(:chatbotId) { 123 }
      let(:mediaType) { 'image' }

      response '201', 'Create Chatbot Media' do
        before do
          create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
          allow(SecureRandom).to receive(:uuid).and_return('48a02e4f-aef4-42e2-a2ad-de97b9004106')
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})
          file_name = "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_#{SecureRandom.uuid}.png"
          s3_instance = instance_double(S3::UploadFile)
          allow(S3::UploadFile).to receive(:new).with(anything, file_name, S3_ATTACHMENT_BUCKET, { skip_delete: true }).and_return(s3_instance)
          expect(s3_instance).to receive(:call)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end
    end
  end

    path '/v1/messages/connected-accounts/{connected_account_id}/chatbot-media' do
    get 'Get Chatbot Media' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :connected_account_id, in: :path, type: :integer
      parameter name: :chatbotId, in: :query, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:connected_account_id) { connected_account.id }
      let(:chatbotId) { 234 }

      response '200', 'Returns Chatbot Media Details' do
        before do
          create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)

          s3_instance = instance_double(S3::GetPresignedUrl)
          allow(S3::GetPresignedUrl).to receive(:new).with("tenant_#{user.tenant_id}/connetced_account_#{connected_account.id}/file/ab/c.png", 'file/ab/c.png', S3_ATTACHMENT_BUCKET, nil).and_return(s3_instance)
          allow(s3_instance).to receive(:call).and_return('https://www.aws.com/file_url.png')
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected Account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end
    end
  end
end
