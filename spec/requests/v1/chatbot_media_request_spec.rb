# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::ChatbotMediaController, type: :request do
  let(:user)                          { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                       { valid_headers(valid_auth_token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }

  describe '#upload' do
    context 'when requested to upload media' do
      before do
        create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
        allow(SecureRandom).to receive(:uuid).and_return('48a02e4f-aef4-42e2-a2ad-de97b9004106')
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        file_name = "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_#{SecureRandom.uuid}.png"
        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, file_name, S3_ATTACHMENT_BUCKET, { skip_delete: true }).and_return(s3_instance)
        expect(s3_instance).to receive(:call)
      end

      it 'should upload media to s3 and return the file details' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/chatbot-media", params: { mediaFile: sample_png_3mb, mediaType: 'image', chatbotId: 123 }, headers: headers
        body = response.parsed_body
        expect(body['id']).to eq(ChatbotMedia.last.id)
        expect(body['fileName']).to eq('sample_png_3mb.png')
      end
    end
  end

   describe '#get' do
    let(:chatbot_media) { create(:chatbot_media, file_name: "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/file_ab_c.png", tenant_id: user.tenant_id, chatbot_id: 123, connected_account_id: connected_account.id) }

    context 'when requested media' do
      before do
        chatbot_media
        create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
        s3_instance = instance_double(S3::GetPresignedUrl)
        allow(S3::GetPresignedUrl).to receive(:new).with("tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/file_ab_c.png", 'file_ab_c.png', S3_ATTACHMENT_BUCKET, nil).and_return(s3_instance)
        allow(s3_instance).to receive(:call).and_return('https://www.aws.com/file_url.png')
      end

      it 'returns media data with pre-signed url' do
        get "/v1/messages/connected-accounts/#{connected_account.id}/chatbot-media?chatbotId=123", headers: headers

        expect(response.parsed_body.first['mediaUrl']).to eq('https://www.aws.com/file_url.png')
        expect(response.parsed_body.first['fileName']).to eq('file_ab.png')
      end
    end
  end
end
