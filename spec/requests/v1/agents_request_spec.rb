# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::AgentsController, type: :request do
  let(:user)                          { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                       { valid_headers(valid_auth_token) }
  let(:invalid_auth_token)            { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:no_whatsapp_permission_headers)  { valid_headers(invalid_auth_token) }
  let(:invalid_header)                { invalid_headers }

  describe '#index' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        context 'when connected account is present' do
          let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
          let(:another_user) { create(:user, tenant_id: user.tenant_id) }

          before do
            connected_account.send("#{entity_type}_agents=", [user, another_user])
          end

          it 'returns agents' do
            get "/v1/messages/connected-accounts/#{connected_account.id}/agents/#{entity_type}", headers: headers

            parsed_response = response.parsed_body
            expect(response.status).to eq(200)
            expect(parsed_response['content']).to match_array([{ 'id' => user.id, 'name' => user.name }, { 'id' => another_user.id, 'name' => another_user.name }])
          end
        end

        context 'when connected acccount is absent' do
          it 'returns error' do
            get "/v1/messages/connected-accounts/-1/agents/#{entity_type}", headers: headers

            parsed_response = response.parsed_body
            expect(response.status).to eq(404)
            expect(parsed_response).to eq({ 'errorCode' => '022006', 'message' => 'Connected Account not found' })
          end
        end

        context 'when user does not have whatsapp read all' do
          let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

          it 'returns error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to view connected account agents")
            get "/v1/messages/connected-accounts/#{connected_account.id}/agents/#{entity_type}", headers: no_whatsapp_permission_headers

            parsed_response = response.parsed_body
            expect(response.status).to eq(401)
            expect(parsed_response).to eq({ 'errorCode' => '022002', 'message' => 'Unauthorized access.' })
          end
        end
      end
    end

    context 'when invalid entity type' do
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id}/agents/deal", headers: headers

        parsed_response = response.parsed_body
        expect(response.status).to eq(422)
        expect(parsed_response).to eq({ 'errorCode' => '022014', 'message' => 'Invalid entity type' })
      end
    end

    context 'when invalid user' do
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id}/agents/lead", headers: invalid_headers

        parsed_response = response.parsed_body
        expect(response.status).to eq(401)
        expect(parsed_response).to eq({ 'errorCode' => '022001', 'message' => nil })
      end
    end
  end

  describe '#save' do
    let(:user_summary_response) do
      user_response = JSON.parse(file_fixture('user-summary-response.json').read)
      user_response.first['id'] = user.id
      user_response.last['id'] = another_user.id
      user_response
    end
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:params) { { agents: [{ id: user.id, name: user.name }, { id: another_user.id, name: another_user.name }] } }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        context 'when connected account is present' do
          context 'when agents are valid' do
            before do
              stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
                .with(
                  headers: {
                    Authorization: "Bearer #{valid_auth_token.token}"
                  }
                )
                .to_return(status: 200, body: user_summary_response.to_json)
            end

            it 'adds agents to connected account' do
              expect {
                post "/v1/messages/connected-accounts/#{connected_account.id}/agents/#{entity_type}/save", headers: headers, params: params.to_json
              }.to change(AgentUser, :count).by(2)
            end
          end

          context 'when one of the agents is invalid' do
            before do
              stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
                .with(
                  headers: {
                    Authorization: "Bearer #{valid_auth_token.token}"
                  }
                )
                .to_return(status: 404, body: '')
            end

            it 'returns error' do
              expect(Rails.logger).to receive(:error).with('User::Summary iam 404')
              post "/v1/messages/connected-accounts/#{connected_account.id}/agents/#{entity_type}/save", headers: headers, params: params.to_json

              parsed_response = response.parsed_body
              expect(parsed_response['errorCode']).to eq('022003')
              expect(parsed_response['message']).to be_nil
            end
          end
        end

        context 'when connected account is not present' do
          it 'raises not found error' do
            post "/v1/messages/connected-accounts/#{connected_account.id + 1}/agents/#{entity_type}/save", headers: headers, params: params.to_json

            parsed_response = response.parsed_body
            expect(parsed_response['errorCode']).to eq('022006')
            expect(parsed_response['message']).to eq('Connected Account not found')
          end
        end

        context 'when user does not have permission' do
          it 'raises authorization error' do
            expect(Rails.logger).to receive(:error).with("User doesn't have permission to update connected account agents")
            post "/v1/messages/connected-accounts/#{connected_account.id + 1}/agents/#{entity_type}/save", headers: no_whatsapp_permission_headers, params: params.to_json

            parsed_response = response.parsed_body
            expect(parsed_response['errorCode']).to eq('022002')
            expect(parsed_response['message']).to eq('Unauthorized access.')
          end
        end
      end
    end

    context 'when invalid entity type'  do
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/agents/deal/save", headers: headers

        parsed_response = response.parsed_body
        expect(response.status).to eq(422)
        expect(parsed_response).to eq({ 'errorCode' => '022014', 'message' => 'Invalid entity type' })
      end
    end

    context 'when invalid token' do
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/agents/lead/save", headers: invalid_headers

        parsed_response = response.parsed_body
        expect(response.status).to eq(401)
        expect(parsed_response).to eq({ 'errorCode' => '022001', 'message' => nil })
      end
    end
  end

  describe '#delete_all' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let(:another_user) { create(:user, tenant_id: user.tenant_id) }
    let(:third_user) { create(:user, tenant_id: user.tenant_id) }
    let(:fourth_user) { create(:user, tenant_id: user.tenant_id) }

    context 'when connected account has agents' do
      before do
        connected_account.lead_agents = [user, another_user]
        connected_account.contact_agents = [third_user, fourth_user]
      end

      it 'deletes all agents for the connected account' do
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(4)

        delete "/v1/messages/connected-accounts/#{connected_account.id}/agents"

        expect(response.status).to eq(200)
        expect(response.body).to eq('')
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
      end

      it 'only deletes agents for the specified connected account' do
        other_connected_account = create(:connected_account, tenant_id: user.tenant_id)
        other_connected_account.lead_agents = [user]

        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(4)
        expect(AgentUser.where(connected_account_id: other_connected_account.id).count).to eq(1)

        delete "/v1/messages/connected-accounts/#{connected_account.id}/agents"

        expect(response.status).to eq(200)
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
        expect(AgentUser.where(connected_account_id: other_connected_account.id).count).to eq(1)
      end

      it 'deletes agents for both lead and contact entity types' do
        lead_agent_users = AgentUser.where(connected_account_id: connected_account.id, entity_type: LOOKUP_LEAD)
        contact_agent_users = AgentUser.where(connected_account_id: connected_account.id, entity_type: LOOKUP_CONTACT)

        expect(lead_agent_users.count).to eq(2)
        expect(contact_agent_users.count).to eq(2)

        delete "/v1/messages/connected-accounts/#{connected_account.id}/agents"

        expect(response.status).to eq(200)
        expect(lead_agent_users.count).to eq(0)
        expect(contact_agent_users.count).to eq(0)
      end
    end

    context 'when connected account has no agents' do
      it 'returns success without error' do
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)

        delete "/v1/messages/connected-accounts/#{connected_account.id}/agents"

        expect(response.status).to eq(200)
        expect(response.body).to eq('')
        expect(AgentUser.where(connected_account_id: connected_account.id).count).to eq(0)
      end
    end
  end
end
