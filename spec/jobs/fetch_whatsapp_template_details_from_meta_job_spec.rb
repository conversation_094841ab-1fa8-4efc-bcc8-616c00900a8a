# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchWhatsappTemplateDetailsFromMetaJob, type: :job do
  let(:connected_account) { create(:connected_account) }
  let(:user) { create(:user) }
  let(:template_id) { '12345' }
  let(:tenant_id) { user.tenant_id }
  let(:job) { described_class.new }
  let(:entity_type) { ['lead'] }

  describe '#perform' do
    context 'when connected_account or user is missing or Meta API fail' do
      it 'logs error and returns when connected_account is missing' do
        allow(ConnectedAccount).to receive(:find_by).with(id: connected_account.id).and_return(nil)
        expect(Rails.logger).to receive(:error).with(/FetchWhatsappTemplateDetailsFromMetaJob \| Invalid connected_account_id #{connected_account.id}/)
        job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type)
      end

      it 'logs error and returns when user is missing' do
        allow(User).to receive(:find_by).with(id: user.id).and_return(nil)
        expect(Rails.logger).to receive(:error).with(/FetchWhatsappTemplateDetailsFromMetaJob \| Invalid user_id #{user.id}/)
        job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type)
      end

      it 'logs error on API failure' do
        allow(Facebook::Request).to receive(:process).and_raise(StandardError.new('API Error'))

        expect(Rails.logger).to receive(:error).with(/FetchWhatsappTemplateDetailsFromMetaJob \| Error while Fetching Whatsapp Template Details for template #{template_id}: API Error/)

        job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type)
      end
    end

    context 'when fetching template details successfully' do
      context 'meta response include whatsapp template with header text component' do
        let(:header_with_text_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'HEADER',
                format: 'TEXT',
                text: 'Welcome to our service',
                example: { header_text: ['a'] }
              }
            ]
          }
        end

        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: header_with_text_meta_response.to_json)
        end

        it 'creates whatsapp template with header text component' do

          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)

          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('HEADER')
          expect(whatsapp_template.components.first.component_format).to eq('TEXT')
          expect(whatsapp_template.components.first.component_text).to eq('Welcome to our service')
          expect(whatsapp_template.components.first.content).to eq({ "header_text" => ['a'] })
        end
      end

      context 'meta response include whatsapp template with body text component' do
        let(:body_with_text_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BODY',
                format: 'TEXT',
                text: 'Thank you for your interest in our services.',
                example: { body_text: ['a'] }
              }
            ]
          }
        end

        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: body_with_text_meta_response.to_json)
        end

        it 'creates whatsapp template with body text component' do

          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)

          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('BODY')
          expect(whatsapp_template.components.first.component_format).to eq('TEXT')
          expect(whatsapp_template.components.first.component_text).to eq('Thank you for your interest in our services.')
          expect(whatsapp_template.components.first.content).to eq({ "body_text" => ['a'] })
        end      
      end

      context 'meta response include whatsapp template with footer text component' do 
        let(:footer_with_text_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'FOOTER',
                format: 'TEXT',
                text: 'Have a great day!'
              }
            ]
          }
        end

        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: footer_with_text_meta_response.to_json)
        end

          it 'creates whatsapp template with footer text component' do

            expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)

            whatsapp_template = WhatsappTemplate.last
            expect(whatsapp_template.name).to eq('test_template')
            expect(whatsapp_template.category).to eq('MARKETING')
            expect(whatsapp_template.language).to eq('en')
            expect(whatsapp_template.status).to eq('APPROVED')
            expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
            expect(whatsapp_template.components.first.component_type).to eq('FOOTER')
            expect(whatsapp_template.components.first.component_format).to eq('TEXT')
            expect(whatsapp_template.components.first.component_text).to eq('Have a great day!')
        end      
      end

      context 'meta response include whatsapp template with buttons type quick_reply component' do 
        let(:button_with_quick_reply_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                "type": "BUTTONS",
                "buttons": [
                    {
                        "type": "QUICK_REPLY",
                        "text": "https://kylas.io/"
                    }
                ]
              }
            ]
          }
        end

        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: button_with_quick_reply_meta_response.to_json)
        end

        it 'creates whatsapp template with button with quick reply component' do
            expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)

            whatsapp_template = WhatsappTemplate.last
            expect(whatsapp_template.name).to eq('test_template')
            expect(whatsapp_template.category).to eq('MARKETING')
            expect(whatsapp_template.language).to eq('en')
            expect(whatsapp_template.status).to eq('APPROVED')
            expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
            expect(whatsapp_template.components.first.component_type).to eq('BUTTON')
            expect(whatsapp_template.components.first.component_format).to eq('QUICK_REPLY')
            expect(whatsapp_template.components.first.component_text).to eq('https://kylas.io/')
        end      
      end
      
      context 'meta response include whatsapp template with buttons type phone_number component' do
        let(:button_with_phone_number_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BUTTONS',
                buttons: [
                  {
                    type: 'PHONE_NUMBER',
                    text: 'Call Us',
                    phone_number: '+************'
                  }
                ]
              }
            ]
          }
        end
  
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: button_with_phone_number_meta_response.to_json)
        end
  
        it 'creates whatsapp template with button with phone number component' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)
  
          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('BUTTON')
          expect(whatsapp_template.components.first.component_format).to eq('PHONE_NUMBER')
          expect(whatsapp_template.components.first.component_text).to eq('Call Us')
          expect(whatsapp_template.components.first.component_value).to eq('+************')
        end
      end
  
      context 'meta response include whatsapp template with buttons type url component' do
        let(:button_with_url_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BUTTONS',
                buttons: [
                  {
                    type: 'URL',
                    text: 'Visit Website',
                    url: 'https://example.com',
                    example: ['https://example.com']
                  }
                ]
              }
            ]
          }
        end
  
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: button_with_url_meta_response.to_json)
        end
  
        it 'creates whatsapp template with button with url component' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)
  
          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('BUTTON')
          expect(whatsapp_template.components.first.component_format).to eq('URL')
          expect(whatsapp_template.components.first.component_text).to eq('Visit Website')
          expect(whatsapp_template.components.first.component_value).to eq('https://example.com')
          expect(whatsapp_template.components.first.content).to eq(['https://example.com'])
        end
      end
  
      context 'meta response include whatsapp template with buttons type copy_code component' do
        let(:button_with_copy_code_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BUTTONS',
                buttons: [
                  {
                    type: 'COPY_CODE',
                    example: ['ABC123']
                  }
                ]
              }
            ]
          }
        end
  
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: button_with_copy_code_meta_response.to_json)
        end
  
        it 'creates whatsapp template with button with copy code component' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)
  
          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('BUTTON')
          expect(whatsapp_template.components.first.component_format).to eq('COPY_CODE')
          expect(whatsapp_template.components.first.component_text).to eq('ABC123')
        end
      end
  
      context 'meta response include whatsapp template with header image component using HeaderHandleMediaService' do
        let(:header_with_image_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'HEADER',
                format: 'IMAGE',
                example: {
                  header_handle: ['https://scontent.whatsapp.net/v/t61.29466-34/test_image.jpg']
                }
              }
            ]
          }
        end
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          header_url = 'https://scontent.whatsapp.net/v/t61.29466-34/test_image.jpg'
          stub_request(:get, header_url).to_return(status: 200, body: 'fake-image-bytes')
          allow(S3::UploadFile).to receive(:new).and_return(double(call: true))
          allow(S3::DownloadFileFromS3).to receive(:new).and_return(double(call: true))
          allow(Facebook::UploadSession).to receive(:new).and_return(
            double(start: double(body: { 'id' => 'upload_session_id' }))
          )
          allow(Facebook::WhatsappTemplateMedia).to receive(:new).and_return(
            double(start_upload: double(body: { 'h' => 'mock_whatsapp_handle' }))
          )
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: header_with_image_meta_response.to_json)
        end
  
        it 'creates whatsapp template with header image component using HeaderHandleMediaService' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }
            .to change(WhatsappTemplate, :count).by(1)
            .and change(TemplateMedia, :count).by(1)
  
          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('HEADER')
          expect(whatsapp_template.components.first.component_format).to eq('IMAGE')
          # component_value should point to the newly created TemplateMedia id
          expect(whatsapp_template.components.first.component_value).to eq(TemplateMedia.last.id.to_s)
        end
      end
  
      context 'meta response include whatsapp template with header video component using HeaderHandleMediaService' do
        let(:header_with_video_meta_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'HEADER',
                format: 'VIDEO',
                example: {
                  header_handle: ['https://scontent.whatsapp.net/v/t61.29466-34/test_video.mp4']
                }
              }
            ]
          }
        end
        
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          header_url = 'https://scontent.whatsapp.net/v/t61.29466-34/test_video.mp4'
          stub_request(:get, header_url).to_return(status: 200, body: 'fake-video-bytes')
          allow(S3::UploadFile).to receive(:new).and_return(double(call: true))
          allow(S3::DownloadFileFromS3).to receive(:new).and_return(double(call: true))
          allow(Facebook::UploadSession).to receive(:new).and_return(
            double(start: double(body: { 'id' => 'upload_session_id' }))
          )
          allow(Facebook::WhatsappTemplateMedia).to receive(:new).and_return(
            double(start_upload: double(body: { 'h' => 'mock_whatsapp_handle' }))
          )
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: header_with_video_meta_response.to_json)
        end
  
        it 'creates whatsapp template with header video component using HeaderHandleMediaService' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }
            .to change(WhatsappTemplate, :count).by(1)
            .and change(TemplateMedia, :count).by(1)
  
          whatsapp_template = WhatsappTemplate.last
          expect(whatsapp_template.name).to eq('test_template')
          expect(whatsapp_template.category).to eq('MARKETING')
          expect(whatsapp_template.language).to eq('en')
          expect(whatsapp_template.status).to eq('APPROVED')
          expect(whatsapp_template.whatsapp_template_id).to eq(template_id)
          expect(whatsapp_template.components.first.component_type).to eq('HEADER')
          expect(whatsapp_template.components.first.component_format).to eq('VIDEO')
          expect(whatsapp_template.components.first.component_value).to eq(TemplateMedia.last.id.to_s)
        end
      end
  
      context 'when response contains named parameters' do
        let(:response_with_named_params) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BODY',
                format: 'TEXT',
                text: 'Hello {{name}}, welcome to our service!'
              }
            ]
          }
        end
  
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}").with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: response_with_named_params.to_json)
        end
  
        it 'skips processing due to named parameters' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.not_to change(WhatsappTemplate, :count)
        end
      end
  
      context 'edge cases for named parameter check' do
        let(:response_invalid_params) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BODY',
                format: 'TEXT',
                text: 'Hello {name}, welcome!' 
              }
            ]
          }
        end
  
        before do
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
          .to_return(body: response_invalid_params.to_json)
        end
  
        it 'handles text that does not match the regex' do
          expect { job.perform(template_id, connected_account.id, tenant_id, user.id, entity_type) }.to change(WhatsappTemplate, :count).by(1)
        end
      end

      context 'counter management with sync_whatsapp_template_active_job_count' do
        let(:entity_types) { ['lead'] }
        let(:successful_response) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BODY',
                format: 'TEXT',
                text: 'Hello world',
                example: { body_text: ['a'] }
              }
            ]
          }
        end
  
        let(:successful_response_named_param) do
          {
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            id: template_id,
            components: [
              {
                type: 'BODY',
                format: 'TEXT',
                text: 'Hello {{name}} world',
                example: { body_text: ['a'] }
              }
            ]
          }
        end
  
        before do
          connected_account.update!(sync_whatsapp_template_active_job_count: 1)
          create(:agent_user, tenant_id: tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: successful_response.to_json)
          allow(Facebook::WhatsappTemplateHelper).to receive(:parse_template_body).and_return(
            name: 'test_template',
            category: 'MARKETING',
            language: 'en',
            status: 'APPROVED',
            whatsapp_template_id: template_id
          )
        end
  
        it 'decrements the counter on successful template creation' do
          initial_count = connected_account.reload.sync_whatsapp_template_active_job_count
          expect {
            job.perform(template_id, connected_account.id, tenant_id, user.id, entity_types)
          }.to change(WhatsappTemplate, :count).by(1)
          expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count - 1)
        end
  
        it 'decrements the counter even on API failure' do
          initial_count = connected_account.reload.sync_whatsapp_template_active_job_count
          allow(Facebook::Request).to receive(:process).and_raise(StandardError.new('API Error'))
          expect(Rails.logger).to receive(:error).with(/Error while Fetching Whatsapp Template Details for template #{template_id}/)
          expect {
            job.perform(template_id, connected_account.id, tenant_id, user.id, entity_types)
          }.not_to change(WhatsappTemplate, :count)
          expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count - 1)
        end
  
        it 'decrements the counter even on template save failure' do
          initial_count = connected_account.reload.sync_whatsapp_template_active_job_count
          allow_any_instance_of(WhatsappTemplate).to receive(:save!).and_raise(StandardError.new('Save Error'))
          expect(Rails.logger).to receive(:error).with(/Error while saving template #{template_id}/)
          expect {
            job.perform(template_id, connected_account.id, tenant_id, user.id, entity_types)
          }.not_to change(WhatsappTemplate, :count)
          expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count - 1)
        end
  
        it 'decrements the counter even when skipping due to named parameters' do
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}")
            .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
            .to_return(body: successful_response_named_param.to_json)
  
          initial_count = connected_account.reload.sync_whatsapp_template_active_job_count
         
          expect {
            job.perform(template_id, connected_account.id, tenant_id, user.id, entity_types)
          }.not_to change(WhatsappTemplate, :count)
          expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count - 1)
        end
      end
    end
  end
end
