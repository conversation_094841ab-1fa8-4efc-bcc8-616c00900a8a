# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ProcessChatbotResponseJob, type: :job do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, created_by: user, updated_by: user) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: 'conv-uuid', chatbot_conversation_completed: false) }
  let(:chatbot_conversation_id) { 'conv-uuid' }
  let(:message_content) { 'Thank you, <PERSON>! What\'s your email address?' }
  let(:completed) { false }
  let(:charge) { 0 }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let(:chatbot_media) { create(:chatbot_media, file_name: "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png", tenant_id: user.tenant_id, connected_account_id: connected_account.id) }
  let(:s3_downloader) { instance_double('S3::DownloadFileFromS3') }
  let(:ai_based_chatbot_payload) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "welcomeMessage": "Hi, Welcome to kylas! How can I help you?",
      "message": "Thank you, John! What's your email address?",
      "completed": false,
      "charge":0,
      "chatbotType": "AI"
    }
  end

  let(:rule_based_chatbot_question_payload) do
    {
      "messageConversationId": "msg-conv-uuid", 
      "chatbotConversationId": "conv-uuid",
      "charge":0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "question",
        "data": {
          "text": "Question to be ask lead first name?"
        }
      }
    }
  end

  let(:rule_based_chatbot_send_message_payload) do
    {
      "messageConversationId": "msg-conv-uuid", 
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "sendMessage",
        "data": [
          {
            "type": "text",
            "text": "Question to be ask lead first name?"
          },
          {
            "type": "media",
            "mediaFile": {
              "fileId": chatbot_media.id,
              "fileName": "c.png",
              "fileSize": 111111,
              "fileType": "image/png",
              "fileCaption": "caption to show"
            }
          }
        ]
      }
    }
  end

  let(:rule_based_chatbot_send_message_payload_with_multiple_nodes) do
    {
      "messageConversationId": "msg-conv-uuid", 
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": [
        {
          "id": 123333,
          "name": "n1",
          "type": "sendMessage",
          "data": [
            {
              "type": "text",
              "text": "Question to be ask lead first name?"
            }
          ]
        },
        {
          "id": 123334,
          "name": "n2",
          "type": "sendMessage",
          "data": [
            {
              "type": "media",
              "mediaFile": {
                "fileId": chatbot_media.id,
                "fileName": "c.png",
                "fileSize": 111111,
                "fileType": "image/jpg",
                "fileCaption": "caption to show"
              }
            }
          ]
        }
      ]
    }
  end

  let(:rule_based_chatbot_interactive_button_payload) do
    {
      "messageConversationId": "msg-conv-uuid", 
      "chatbotConversationId": "conv-uuid",
      "charge":0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "buttons",
        "data": {
          "header": {
            "format": "text",
            "text": "Header text"
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "buttons": [
            {
              "id": "opt1",
              "text": "change",
              "position": 1
            },
            {
              "id": "opt2",
              "text": "cancel",
              "position": 2
            }
          ],
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

   let(:media_header_interactive_button_payload) do
    {
      "messageConversationId": "msg-conv-uuid", 
      "chatbotConversationId": "conv-uuid",
      "charge":0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "buttons",
        "data": {
          "header": {
            "format": "image/png",
            "mediaFile": {
              "fileId": chatbot_media.id
            }
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "buttons": [
            {
              "id": "opt1",
              "text": "change",
              "position": 1
            },
            {
              "id": "opt2",
              "text": "cancel",
              "position": 2
            }
          ],
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

  let(:rule_based_chatbot_interactive_list_payload) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "list",
        "data": {
          "header": {
            "format": "text",
            "text": "Header text"
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "menuButton": "Menu Button",
          "sections": [
            {
              "title": "Section 1",
              "rows": [
                {
                  "id": "row1",
                  "title": "Item 1",
                  "description": "Description for item 1"
                },
                {
                  "id": "row2",
                  "title": "Item 2",
                  "description": "Description for item 2"
                }
              ]
            }
          ],
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

  let(:rule_based_chatbot_payload_with_blank_node_details) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": nil
    }
  end

  let(:media_header_interactive_list_payload) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "list",
        "data": {
          "header": {
            "format": "image/png",
            "mediaFile": {
              "fileId": chatbot_media.id
            }
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "menuButton": "Menu Button",
          "sections": [
            {
              "title": "Section 1",
              "rows": [
                {
                  "id": "row1",
                  "title": "Item 1",
                  "description": "Description for item 1"
                },
                {
                  "id": "row2",
                  "title": "Item 2",
                  "description": "Description for item 2"
                }
              ]
            }
          ],
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

  let(:rule_based_chatbot_interactive_cta_url_payload) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "url",
        "data": {
          "header": {
            "format": "text",
            "text": "Header text"
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "buttonText": "Click Here",
          "ctaURL": "https://example.com",
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

  let(:media_header_interactive_cta_url_payload) do
    {
      "messageConversationId": "msg-conv-uuid",
      "chatbotConversationId": "conv-uuid",
      "charge": 0,
      "chatbotType": "RULE",
      "nodeDetails": {
        "id": 123333,
        "name": "n1",
        "type": "url",
        "data": {
          "header": {
            "format": "image/png",
            "mediaFile": {
              "fileId": chatbot_media.id
            }
          },
          "body": "Question to be ask",
          "footer": "Footer value",
          "buttonText": "Click Here",
          "ctaURL": "https://example.com",
          "entityFields": [
            {
              "entityType": "LEAD",
              "fieldId": 123,
              "standard": true,
              "displayName": "First Name"
            }
          ]
        }
      }
    }
  end

  describe '#perform' do
    context 'when chatbot conversation id changes and not completed' do
      let(:conversation_with_old_id) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: 'old-conv-uuid', chatbot_conversation_completed: false) }
      let(:payload_with_new_id) do
        {
          "messageConversationId": conversation_with_old_id.id,
          "chatbotConversationId": "new-conv-uuid",
          "message": "Test message",
          "completed": false,
          "charge": 0,
          "chatbotType": "AI"
        }
      end

      before do
        allow_any_instance_of(described_class).to receive(:should_process_chatbot_response?).and_return(true)
        session_message_double = double('SessionMessage')
        allow(session_message_double).to receive(:send_text)
        allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)
        allow(Thread).to receive(:current).and_return(Thread.current)
        allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
        allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
        allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
      end

      it 'updates the conversation with new chatbot_conversation_id and sets chatbot_conversation_completed to false' do
        described_class.perform_now(payload_with_new_id)

        expect(conversation_with_old_id.reload.chatbot_conversation_id).to eq('new-conv-uuid')
        expect(conversation_with_old_id.reload.chatbot_conversation_completed).to eq(false)

      end
    end

    context 'when chatbot type is AI_BASED' do
      context 'when conversation exists and conditions are met' do
        let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

        before do
          session_message_double = double('SessionMessage')
          allow(session_message_double).to receive(:send_text)
          allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

          allow(Thread).to receive(:current).and_return(Thread.current)
          allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
          allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
          allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
        end

        it 'sends chatbot response message using session message service' do
          expected_params = {
            id: connected_account.id,
            conversation_id: conversation.id,
            message_type: 'text',
            message_body: message_content,
            entity_id: 123,
            entity_type: 'lead'
          }

          expect(Message::SessionMessage).to receive(:new).with(expected_params)

          described_class.perform_now(ai_based_chatbot_payload)
        end

        it 'logs successful processing' do
          allow(Rails.logger).to receive(:info)
          expect(Rails.logger).to receive(:info).with(/Successfully processed chatbot response/)

          described_class.perform_now(ai_based_chatbot_payload)
        end

        context 'when chatbot conversation is completed' do
          let(:conversation_double) { double('Conversation', id: 123, chatbot_conversation_id: chatbot_conversation_id, connected_account_id: connected_account.id, chatbot_conversation_completed: false, status: IN_PROGRESS) }
          let(:sub_conversation_double) { double('SubConversation') }

          it 'marks conversation as completed' do
            ai_based_chatbot_payload['completed'] = true
            allow(Rails.logger).to receive(:info)
            allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_double)
            allow_any_instance_of(described_class).to receive(:should_process_chatbot_response?).and_return(true)
            allow(conversation_double).to receive(:ongoing_sub_conversation).and_return(sub_conversation_double)
            expect(sub_conversation_double).to receive(:update!).with(status: COMPLETED)
            expect(conversation_double).to receive(:update!).with(chatbot_conversation_completed: true, status: COMPLETED, chatbot_conversation_id: nil)
            expect(Rails.logger).to receive(:info).with(/Marked chatbot conversation as completed/)

            described_class.perform_now(ai_based_chatbot_payload)
          end
        end

        context 'when charge is provided' do
          let(:whatsapp_credits_service) { double('WhatsappCreditsService') }

          before do
            allow(WhatsappCreditsService).to receive(:new).and_return(whatsapp_credits_service)
          end

          it 'calls deduct_chatbot_credits with correct parameters' do
            ai_based_chatbot_payload['charge'] = 0.5
            expect(whatsapp_credits_service).to receive(:deduct_chatbot_credits).with(
              connected_account.tenant_id,
              connected_account,
              0.5
            )

            described_class.perform_now(ai_based_chatbot_payload)
          end

          it 'logs successful credit deduction' do
            ai_based_chatbot_payload['charge'] = 0.5
            allow(whatsapp_credits_service).to receive(:deduct_chatbot_credits)
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: 0.5/)

            described_class.perform_now(ai_based_chatbot_payload)
          end
        end

        context 'when charge is zero' do
          let(:charge) { 0 }

          it 'does not call deduct_chatbot_credits' do
            expect(WhatsappCreditsService).not_to receive(:new)

            described_class.perform_now(ai_based_chatbot_payload)
          end
        end
      end

      context 'when conversation does not exist' do
        it 'logs error and returns early' do
          ai_based_chatbot_payload['chatbotConversationId'] = 'non_existent_id'
          ai_based_chatbot_payload['messageConversationId'] = nil
          expect(Rails.logger).to receive(:error).with(/Conversation not found/)
          described_class.perform_now(ai_based_chatbot_payload)
        end
      end

      context 'when connected account does not exist' do
        let(:conversation_without_account) { create(:conversation, connected_account_id: 99999, chatbot_conversation_id: chatbot_conversation_id) }

        before do
          allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_without_account)
        end

        it 'logs error and returns early' do
          expect(Rails.logger).to receive(:error).with(/Connected account not found/)
          described_class.perform_now(ai_based_chatbot_payload)
        end
      end

      context 'when chatbot conversation should not be processed' do
        let(:conversation_without_chatbot_id) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: nil) }

        before do
          allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_without_chatbot_id)
        end

        it 'logs skip message and returns early' do
          ai_based_chatbot_payload['messageConversationId'] = nil
          allow(Rails.logger).to receive(:info)
          expect(Rails.logger).to receive(:info).with(/Chatbot response processing skipped/)
          described_class.perform_now(ai_based_chatbot_payload)
        end
      end

      context 'when chatbot_conversation_id is not present' do
        let(:conversation_without_chatbot_id) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_id: nil) }

        before do
          allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_without_chatbot_id)
        end

        it 'logs skip message and returns early' do
          ai_based_chatbot_payload['messageConversationId'] = nil
          allow(Rails.logger).to receive(:info)
          expect(Rails.logger).to receive(:info).with(/Chatbot response processing skipped/)
          described_class.perform_now(ai_based_chatbot_payload)
        end
      end
    end

    context 'when chatbot type is RULE_BASED' do
      context 'when conversation is exist and all conditions are met' do
        context 'when node type is question' do
          let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
          let(:message_content) { 'Question to be ask lead first name?' }

          before do
            session_message_double = double('SessionMessage')
            allow(session_message_double).to receive(:send_text)
            allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

            allow(Thread).to receive(:current).and_return(Thread.current)
            allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
            allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
            allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
          end

          it 'sends text session message' do
            expected_params = {
              id: connected_account.id,
              conversation_id: conversation.id,
              message_type: 'text',
              message_body: message_content,
              entity_id: 123,
              entity_type: 'lead'
            }

            expect(Message::SessionMessage).to receive(:new).with(expected_params)

            described_class.perform_now(rule_based_chatbot_question_payload)
          end

          it 'logs successful processing' do
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:info).with(/Successfully processed chatbot response/)

            described_class.perform_now(rule_based_chatbot_question_payload)
          end

          context 'when chatbot conversation is completed' do
            let(:conversation_double) { double('Conversation', id: 123, chatbot_conversation_id: chatbot_conversation_id, connected_account_id: connected_account.id, chatbot_conversation_completed: false, status: IN_PROGRESS) }
            let(:sub_conversation_double) { double('SubConversation') }

            it 'marks conversation as completed' do
              rule_based_chatbot_question_payload['completed'] = true
              allow(Rails.logger).to receive(:info)
              allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_double)
              allow_any_instance_of(described_class).to receive(:should_process_chatbot_response?).and_return(true)
              allow(conversation_double).to receive(:ongoing_sub_conversation).and_return(sub_conversation_double)
              expect(sub_conversation_double).to receive(:update!).with(status: COMPLETED)
              expect(conversation_double).to receive(:update!).with(chatbot_conversation_completed: true, status: COMPLETED, chatbot_conversation_id: nil)
              expect(Rails.logger).to receive(:info).with(/Marked chatbot conversation as completed/)

              described_class.perform_now(rule_based_chatbot_question_payload)
            end
          end

          context 'when charge is provided' do
            let(:whatsapp_credits_service) { double('WhatsappCreditsService') }

            before do
              allow(WhatsappCreditsService).to receive(:new).and_return(whatsapp_credits_service)
            end

            it 'calls deduct_chatbot_credits with correct parameters' do
              rule_based_chatbot_question_payload['charge'] = 0.5
              expect(whatsapp_credits_service).to receive(:deduct_chatbot_credits).with(
                connected_account.tenant_id,
                connected_account,
                0.5
              )

              described_class.perform_now(rule_based_chatbot_question_payload)
            end

            it 'logs successful credit deduction' do
              rule_based_chatbot_question_payload['charge'] = 0.5
              allow(whatsapp_credits_service).to receive(:deduct_chatbot_credits)
              allow(Rails.logger).to receive(:info)
              expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: 0.5/)

              described_class.perform_now(rule_based_chatbot_question_payload)
            end
          end

          context 'when charge is zero' do
            it 'does not call deduct_chatbot_credits' do
              expect(WhatsappCreditsService).not_to receive(:new)

              described_class.perform_now(rule_based_chatbot_question_payload)
            end
          end
        end

        context 'when node type is sendMessage' do
          let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
          let(:message_content) { 'Question to be ask lead first name?' }

          let(:bucket_name) { 'qa-message-attachment' }
          let(:file_path) { "spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png" }
          let(:file_name) { "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png" }
          let(:s3_resource) { instance_double(Aws::S3::Resource) }
          let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
          let(:s3_object) { instance_double(Aws::S3::Object) }

          before do
            session_message_double = double('SessionMessage')
            allow(session_message_double).to receive(:send_text)
            allow(session_message_double).to receive(:send_media)
            allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

            allow(Thread).to receive(:current).and_return(Thread.current)
            allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
            allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))

            stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
            allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
            allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
            allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
            allow(s3_object).to receive(:download_file).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png")
            allow(Rails.root).to receive(:join).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png").and_return(file_path)
            allow(File).to receive(:open).with(file_path, "rb").and_return(sample_png_3mb)
            allow(File).to receive(:delete).with(file_path)
            allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
          end

          it 'sends text session message and media session message' do
            text_session_message_params = {
              id: connected_account.id,
              conversation_id: conversation.id,
              message_type: 'text',
              message_body: message_content,
              entity_id: 123,
              entity_type: 'lead'
            }
           
            uploaded_file = ActionDispatch::Http::UploadedFile.new(
              filename: "sample_png_3mb.png",
              type: "image",
              tempfile: File.open(file_path, "rb")
            )

            allow(ActionDispatch::Http::UploadedFile).to receive(:new).and_return(uploaded_file)
            media_session_message_params = {
              id: connected_account.id,
              conversation_id: conversation.id,
              message_type: 'media',
              entity_id: 123,
              entity_type: 'lead',
              media: [
                {
                  type: 'image',
                  caption: 'caption to show',
                  file: uploaded_file
                }
              ]
            }

            expect(Message::SessionMessage).to receive(:new).with(text_session_message_params)
            expect(Message::SessionMessage).to receive(:new).with(media_session_message_params)

            described_class.perform_now(rule_based_chatbot_send_message_payload)
          end
        end

        context 'when multiple node details are present in the payload and when we dont have to wait for user response' do
          let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
          let(:message_content) { 'Question to be ask lead first name?' }

          let(:bucket_name) { 'qa-message-attachment' }
          let(:file_path) { "spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png" }
          let(:file_name) { "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png" }
          let(:s3_resource) { instance_double(Aws::S3::Resource) }
          let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
          let(:s3_object) { instance_double(Aws::S3::Object) }

          before do
            session_message_double = double('SessionMessage')
            allow(session_message_double).to receive(:send_text)
            allow(session_message_double).to receive(:send_media)
            allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

            allow(Thread).to receive(:current).and_return(Thread.current)
            allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
            allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))

            stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
            allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
            allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
            allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
            allow(s3_object).to receive(:download_file).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png")
            allow(Rails.root).to receive(:join).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png").and_return(file_path)
            allow(File).to receive(:open).with(file_path, "rb").and_return(sample_png_3mb)
            allow(File).to receive(:delete).with(file_path)
            allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
          end

          it 'sends text session message and media session message as per the given node details' do
            text_session_message_params = {
              id: connected_account.id,
              conversation_id: conversation.id,
              message_type: 'text',
              message_body: message_content,
              entity_id: 123,
              entity_type: 'lead'
            }
           
            uploaded_file = ActionDispatch::Http::UploadedFile.new(
              filename: "sample_png_3mb.png",
              type: "image",
              tempfile: File.open(file_path, "rb")
            )

            allow(ActionDispatch::Http::UploadedFile).to receive(:new).and_return(uploaded_file)
            media_session_message_params = {
              id: connected_account.id,
              conversation_id: conversation.id,
              message_type: 'media',
              entity_id: 123,
              entity_type: 'lead',
              media: [
                {
                  type: 'image',
                  caption: 'caption to show',
                  file: uploaded_file
                }
              ]
            }

            expect(Message::SessionMessage).to receive(:new).with(text_session_message_params)
            expect(Message::SessionMessage).to receive(:new).with(media_session_message_params)

            described_class.perform_now(rule_based_chatbot_send_message_payload_with_multiple_nodes)
          end
        end

        context 'when node type is interactive button' do
          context 'when header type is text' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
            let(:message_content) { 'Question to be ask lead first name?' }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends text header interactive button session message' do
              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'button',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "text",
                  text: "Header text"
                },
                body: "Question to be ask",
                footer: "Footer value",
                buttons: [
                  {
                    id: "opt1",
                    text: "change",
                    position: 1
                  },
                  {
                    id: "opt2",
                    text: "cancel",
                    position: 2
                  }
                ]
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(rule_based_chatbot_interactive_button_payload)
            end

            it 'logs successful processing' do
              allow(Rails.logger).to receive(:info)
              expect(Rails.logger).to receive(:info).with(/Successfully processed chatbot response/)

              described_class.perform_now(rule_based_chatbot_interactive_button_payload)
            end

            context 'when charge is provided' do
              let(:whatsapp_credits_service) { double('WhatsappCreditsService') }

              before do
                allow(WhatsappCreditsService).to receive(:new).and_return(whatsapp_credits_service)
              end

              it 'calls deduct_chatbot_credits with correct parameters' do
                rule_based_chatbot_interactive_button_payload['charge'] = 0.5
                expect(whatsapp_credits_service).to receive(:deduct_chatbot_credits).with(
                  connected_account.tenant_id,
                  connected_account,
                  0.5
                )

                described_class.perform_now(rule_based_chatbot_interactive_button_payload)
              end

              it 'logs successful credit deduction' do
                rule_based_chatbot_interactive_button_payload['charge'] = 0.5
                allow(whatsapp_credits_service).to receive(:deduct_chatbot_credits)
                allow(Rails.logger).to receive(:info)
                expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: 0.5/)

                described_class.perform_now(rule_based_chatbot_interactive_button_payload)
              end
            end

            context 'when charge is zero' do
              it 'does not call deduct_chatbot_credits' do
                expect(WhatsappCreditsService).not_to receive(:new)

                described_class.perform_now(rule_based_chatbot_interactive_button_payload)
              end
            end
          end

          context 'when header type is image' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
            let(:message_content) { 'Question to be ask lead first name?' }

            let(:bucket_name) { 'qa-message-attachment' }
            let(:file_path) { "spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png" }
            let(:file_name) { "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png" }
            let(:s3_resource) { instance_double(Aws::S3::Resource) }
            let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
            let(:s3_object) { instance_double(Aws::S3::Object) }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))

              stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
              allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
              allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
              allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
              allow(s3_object).to receive(:download_file).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png")
              allow(Rails.root).to receive(:join).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png").and_return(file_path)
              allow(File).to receive(:open).with(file_path, "rb").and_return(sample_png_3mb)
              allow(File).to receive(:exist?).with(file_path).and_return(true)
              allow(File).to receive(:delete).with(file_path)
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends media header interactive button message' do
              uploaded_file = ActionDispatch::Http::UploadedFile.new(
                filename: "sample_png_3mb.png",
                type: "image",
                tempfile: File.open(file_path, "rb")
              )

              allow(ActionDispatch::Http::UploadedFile).to receive(:new).and_return(uploaded_file)
            
              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'button',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "image",
                  mediaDetails: {
                    file: uploaded_file,
                    type: "image",
                  }
                },
                body: "Question to be ask",
                footer: "Footer value",
                buttons: [
                  {
                    id: "opt1",
                    text: "change",
                    position: 1
                  },
                  {
                    id: "opt2",
                    text: "cancel",
                    position: 2
                  }
                ]
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(media_header_interactive_button_payload)
            end
          end
        end

        context 'when node type is interactive list' do
          context 'when header type is text' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
            let(:message_content) { 'Question to be ask lead first name?' }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends text header interactive list session message' do
              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'list',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "text",
                  text: "Header text"
                },
                body: "Question to be ask",
                footer: "Footer value",
                menu_button_text: "Menu Button",
                sections: [
                  { 
                    title: "Section 1",
                    rows: [
                      { id: "row1", title: "Item 1", description: "Description for item 1" },
                      { id: "row2", title: "Item 2", description: "Description for item 2" }
                    ]
                  }
                ]
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(rule_based_chatbot_interactive_list_payload)
            end

            it 'logs successful processing' do
              allow(Rails.logger).to receive(:info)
              expect(Rails.logger).to receive(:info).with(/Successfully processed chatbot response/)

              described_class.perform_now(rule_based_chatbot_interactive_list_payload)
            end

            context 'when charge is provided' do
              let(:whatsapp_credits_service) { double('WhatsappCreditsService') }

              before do
                allow(WhatsappCreditsService).to receive(:new).and_return(whatsapp_credits_service)
              end

              it 'calls deduct_chatbot_credits with correct parameters' do
                rule_based_chatbot_interactive_list_payload['charge'] = 0.5
                expect(whatsapp_credits_service).to receive(:deduct_chatbot_credits).with(
                  connected_account.tenant_id,
                  connected_account,
                  0.5
                )

                described_class.perform_now(rule_based_chatbot_interactive_list_payload)
              end

              it 'logs successful credit deduction' do
                rule_based_chatbot_interactive_list_payload['charge'] = 0.5
                allow(whatsapp_credits_service).to receive(:deduct_chatbot_credits)
                allow(Rails.logger).to receive(:info)
                expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: 0.5/)

                described_class.perform_now(rule_based_chatbot_interactive_list_payload)
              end
            end

            context 'when charge is zero' do
              it 'does not call deduct_chatbot_credits' do
                expect(WhatsappCreditsService).not_to receive(:new)

                described_class.perform_now(rule_based_chatbot_interactive_button_payload)
              end
            end
          end

          context 'when header type is image' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
            let(:message_content) { 'Question to be ask lead first name?' }

            let(:bucket_name) { 'qa-message-attachment' }
            let(:file_path) { "spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png" }
            let(:file_name) { "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png" }
            let(:s3_resource) { instance_double(Aws::S3::Resource) }
            let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
            let(:s3_object) { instance_double(Aws::S3::Object) }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))

              stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
              allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
              allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
              allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
              allow(s3_object).to receive(:download_file).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png")
              allow(Rails.root).to receive(:join).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png").and_return(file_path)
              allow(File).to receive(:open).with(file_path, "rb").and_return(sample_png_3mb)
              allow(File).to receive(:exist?).with(file_path).and_return(true)
              allow(File).to receive(:delete).with(file_path)
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends media header interactive button message' do
              uploaded_file = ActionDispatch::Http::UploadedFile.new(
                filename: "sample_png_3mb.png",
                type: "image",
                tempfile: File.open(file_path, "rb")
              )

              allow(ActionDispatch::Http::UploadedFile).to receive(:new).and_return(uploaded_file)
            
              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'list',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "image",
                  mediaDetails: {
                    file: uploaded_file,
                    type: "image",
                  }
                },
                body: "Question to be ask",
                footer: "Footer value",
                menu_button_text: "Menu Button",
                sections: [
                  {
                    title: "Section 1",
                    rows: [
                      { id: "row1", title: "Item 1", description: "Description for item 1" },
                      { id: "row2", title: "Item 2", description: "Description for item 2" }
                    ]
                  }
                ]
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(media_header_interactive_list_payload)
            end
          end
        end

        context 'when node type is interactive cta url' do
          context 'when header type is text' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends text header interactive cta url session message' do
              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'cta_url',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "text",
                  text: "Header text"
                },
                body: "Question to be ask",
                footer: "Footer value",
                button_text: "Click Here",
                cta_url: "https://example.com"
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(rule_based_chatbot_interactive_cta_url_payload)
            end

            it 'logs successful processing' do
              allow(Rails.logger).to receive(:info)
              expect(Rails.logger).to receive(:info).with(/Successfully processed chatbot response/)

              described_class.perform_now(rule_based_chatbot_interactive_cta_url_payload)
            end

            context 'when charge is provided' do
              let(:whatsapp_credits_service) { double('WhatsappCreditsService') }

              before do
                allow(WhatsappCreditsService).to receive(:new).and_return(whatsapp_credits_service)
              end

              it 'calls deduct_chatbot_credits with correct parameters' do
                rule_based_chatbot_interactive_cta_url_payload['charge'] = 0.5
                expect(whatsapp_credits_service).to receive(:deduct_chatbot_credits).with(
                  connected_account.tenant_id,
                  connected_account,
                  0.5
                )

                described_class.perform_now(rule_based_chatbot_interactive_cta_url_payload)
              end

              it 'logs successful credit deduction' do
                rule_based_chatbot_interactive_cta_url_payload['charge'] = 0.5
                allow(whatsapp_credits_service).to receive(:deduct_chatbot_credits)
                allow(Rails.logger).to receive(:info)
                expect(Rails.logger).to receive(:info).with(/Deducted chatbot credits: 0.5/)

                described_class.perform_now(rule_based_chatbot_interactive_cta_url_payload)
              end
            end

            context 'when charge is zero' do
              it 'does not call deduct_chatbot_credits' do
                expect(WhatsappCreditsService).not_to receive(:new)

                described_class.perform_now(rule_based_chatbot_interactive_cta_url_payload)
              end
            end
          end

          context 'when header type is image' do
            let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

            let(:bucket_name) { 'qa-message-attachment' }
            let(:file_path) { "spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png" }
            let(:file_name) { "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/chatbot-attachments/sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png" }
            let(:s3_resource) { instance_double(Aws::S3::Resource) }
            let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
            let(:s3_object) { instance_double(Aws::S3::Object) }

            before do
              session_message_double = double('SessionMessage')
              allow(session_message_double).to receive(:send_interactive_message)
              allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

              allow(Thread).to receive(:current).and_return(Thread.current)
              allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
              allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))

              stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
              allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
              allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
              allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
              allow(s3_object).to receive(:download_file).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png")
              allow(Rails.root).to receive(:join).with("sample_png_3mb_48a02e4f-aef4-42e2-a2ad-de97b9004106.png").and_return(file_path)
              allow(File).to receive(:open).with(file_path, "rb").and_return(sample_png_3mb)
              allow(File).to receive(:exist?).with(file_path).and_return(true)
              allow(File).to receive(:delete).with(file_path)
              allow(WhatsappCreditsService).to receive(:new).and_return(double(deduct_chatbot_credits: true))
            end

            it 'sends media header interactive cta url message' do
              uploaded_file = ActionDispatch::Http::UploadedFile.new(
                filename: "sample_png_3mb.png",
                type: "image",
                tempfile: File.open(file_path, "rb")
              )

              allow(ActionDispatch::Http::UploadedFile).to receive(:new).and_return(uploaded_file)

              expected_params = {
                id: connected_account.id,
                conversation_id: conversation.id,
                message_type: 'cta_url',
                entity_id: 123,
                entity_type: 'lead',
                header: {
                  format: "image",
                  mediaDetails: {
                    file: uploaded_file,
                    type: "image",
                  }
                },
                body: "Question to be ask",
                footer: "Footer value",
                button_text: "Click Here",
                cta_url: "https://example.com"
              }

              expect(Message::SessionMessage).to receive(:new).with(expected_params)

              described_class.perform_now(media_header_interactive_cta_url_payload)
            end
          end
        end
      end

      context 'when nodeDetails is blank' do
        context 'when completed is true' do
          let(:conversation_double) { double('Conversation', id: 123, chatbot_conversation_id: chatbot_conversation_id, connected_account_id: connected_account.id, chatbot_conversation_completed: false, status: IN_PROGRESS) }
          let(:sub_conversation_double) { double('SubConversation') }

          before do
            allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation_double)
            allow_any_instance_of(described_class).to receive(:should_process_chatbot_response?).and_return(true)
            allow(conversation_double).to receive(:ongoing_sub_conversation).and_return(sub_conversation_double)
            allow(Rails.logger).to receive(:info)
          end

          it 'marks conversation as completed and logs skip message' do
            rule_based_chatbot_payload_with_blank_node_details['completed'] = true

            expect(sub_conversation_double).to receive(:update!).with(status: COMPLETED)
            expect(conversation_double).to receive(:update!).with(chatbot_conversation_completed: true, status: COMPLETED, chatbot_conversation_id: nil)
            expect(Rails.logger).to receive(:info).with(/Chatbot response processing skipped.*Error: Node details are missing/)
            expect(Rails.logger).to receive(:info).with(/Marked chatbot conversation as completed/)

            described_class.perform_now(rule_based_chatbot_payload_with_blank_node_details)
          end
        end

        context 'when completed is false' do
          before do
            allow(Conversation).to receive(:find_by).with(chatbot_conversation_id: chatbot_conversation_id).and_return(conversation)
            allow_any_instance_of(described_class).to receive(:should_process_chatbot_response?).and_return(true)
            allow(Rails.logger).to receive(:info)
          end

          it 'does not mark as completed but logs skip message' do
            rule_based_chatbot_payload_with_blank_node_details['completed'] = false

            expect(conversation).not_to receive(:ongoing_sub_conversation)
            expect(conversation).not_to receive(:update!)
            expect(Rails.logger).to receive(:info).with(/Chatbot response processing skipped.*Error: Node details are missing/)

            described_class.perform_now(rule_based_chatbot_payload_with_blank_node_details)
          end
        end
      end
    end
  end

  describe '#should_process_chatbot_response?' do
    let(:job_instance) { described_class.new }

    context 'when all conditions are met' do
      it 'returns true' do
        result = job_instance.send(:should_process_chatbot_response?, conversation)
        expect(result).to be true
      end
    end

    context 'when chatbot_conversation_id is not present' do
      let(:conversation_without_chatbot_id) { create(:conversation, chatbot_conversation_id: nil) }

      it 'returns false' do
        result = job_instance.send(:should_process_chatbot_response?, conversation_without_chatbot_id)
        expect(result).to be false
      end
    end
  end

  describe '#get_entity_details_for_conversation' do
    let(:job_instance) { described_class.new }
    let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }

    it 'returns entity details array' do
      result = job_instance.send(:get_entity_details_for_conversation, conversation)
      
      expect(result).to be_an(Array)
      expect(result.first[:id]).to eq(123)
      expect(result.first[:entityType]).to eq('lead')
    end
  end

  describe '#send_chatbot_response_message' do
    let(:job_instance) { described_class.new }
    let(:entity_details) { [{ id: 123, entityType: 'lead' }] }
    let(:session_message_double) { double('SessionMessage') }

    before do
      allow(job_instance).to receive(:setup_thread_context)
      allow(job_instance).to receive(:get_entity_details_for_conversation).and_return(entity_details)
      allow(session_message_double).to receive(:send_text)
    end

    it 'calls SessionMessage with correct parameters including entity details' do
      expected_params = {
        id: connected_account.id,
        conversation_id: conversation.id,
        message_type: 'text',
        message_body: message_content,
        entity_id: 123,
        entity_type: 'lead'
      }

      expect(Message::SessionMessage).to receive(:new).with(expected_params).and_return(session_message_double)
      expect(session_message_double).to receive(:send_text)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, 'text', message_content)
    end

    it 'calls SessionMessage without entity details when none available' do
      allow(job_instance).to receive(:get_entity_details_for_conversation).and_return([])
      
      expected_params = {
        id: connected_account.id,
        conversation_id: conversation.id,
        message_type: 'text',
        message_body: message_content
      }

      expect(Message::SessionMessage).to receive(:new).with(expected_params).and_return(session_message_double)
      expect(session_message_double).to receive(:send_text)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, 'text', message_content)
    end

    it 'logs successful message sending' do
      allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)
      expect(Rails.logger).to receive(:info).with(/Chatbot response message sent/)
      
      job_instance.send(:send_chatbot_response_message, conversation, connected_account, 'text', message_content)
    end

    it 'handles errors gracefully' do
      allow(Message::SessionMessage).to receive(:new).and_raise(StandardError.new('Test error'))
      
      expect(Rails.logger).to receive(:error).with(/Error sending chatbot response message/)
      
      job_instance.send(:send_chatbot_response_message, conversation, connected_account, 'text', message_content)
    end

    context 'when entity not found exception occurs' do
      before do
        allow(Message::SessionMessage).to receive(:new).and_raise(ExceptionHandler::EntityNotFound.new('Entity not found'))
      end

      it 'rescues the exception by sending the same message after delay' do
        expected_params = {
          id: connected_account.id,
          conversation_id: conversation.id,
          message_type: 'text',
          message_body: message_content,
          entity_id: 123,
          entity_type: 'lead'
        }

        expect(Message::SessionMessage).to receive(:new).with(expected_params).and_return(session_message_double)
        expect(session_message_double).to receive(:send_text)
        
        job_instance.send(:send_chatbot_response_message, conversation, connected_account, 'text', message_content)
      end
    end
  end
end
