require 'rails_helper'

RSpec.describe WhatsappMessageStatusRetryJob, type: :job do
  include ActiveJob::TestHelper

  include ActiveSupport::Testing::TimeHelpers

  let(:field_entry) do
    {
      'value' => {
        'statuses' => [{ 'id' => '9876543210' }],
        'metadata' => { 'display_phone_number' => '+919876543210' }
      }
    }
  end
  let(:message) { create(:message, remote_id: '9876543210') }

  describe '#perform' do
    context 'when message is found' do
      it 'calls WhatsappMessageStatusTransitionService' do
        service_instance = instance_double(WhatsappMessageStatusTransitionService)
        expect(WhatsappMessageStatusTransitionService).to receive(:new).with(message, field_entry).and_return(service_instance)
        expect(service_instance).to receive(:call)
        described_class.new.perform(field_entry, 1)
      end
    end

    context 'when message is not found' do


      context 'and retry count is less than 3' do
        it 'enqueues the job again with incremented retry count and correct wait time' do
          freeze_time do
            allow(Rails.logger).to receive(:error)
            expect(Rails.logger).to receive(:error).with(/Retrying in 30 seconds \(attempt 2\)/)
            
            expect do
              described_class.new.perform(field_entry, 1)
            end.to have_enqueued_job(described_class).with(field_entry, 2).at(30.seconds.from_now)
          end
        end
      end

      context 'and retry count is 2 ' do
        it 'enqueues the job again with incremented retry count and correct wait time' do
          freeze_time do
            allow(Rails.logger).to receive(:error)
            expect(Rails.logger).to receive(:error).with(/Retrying in 60 seconds \(attempt 3\)/)

            expect do
              described_class.new.perform(field_entry, 2)
            end.to have_enqueued_job(described_class).with(field_entry, 3).at(60.seconds.from_now)
          end
        end
      end

      context 'and retry count is 3' do
        it 'logs an error and does not enqueue the job' do
          expect(Rails.logger).to receive(:error).with('Whatsapp Message with remote_id 9876543210 and sender_number +919876543210 still not found for message status update after 3 attempts')
          expect do
            described_class.new.perform(field_entry, 3)
          end.not_to have_enqueued_job(WhatsappMessageStatusRetryJob)
        end
      end
    end
  end
end
