# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InitiateChatbotConversationJob, type: :job do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, is_chatbot_configured: true, created_by: user, updated_by: user) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, chatbot_conversation_completed: false) }
  let(:message_content) { 'Hello, I need help' }

  describe '#perform' do
    context 'when conversation exists and chatbot should be initiated' do
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
      let!(:sub_conversation) { create(:sub_conversation, conversation: conversation) }

      let(:chatbot_response) do
        {
          success: true,
          chatbot_conversation_id: 'conv-uuid-123',
          message: 'Hello! How can I help you?',
          first_question: 'What is your name?'
        }
      end

      before do
        allow(ChatbotService).to receive(:new).and_return(double(initiate_conversation: chatbot_response))

        # session_message_double = double('SessionMessage')
        # allow(session_message_double).to receive(:send_text)
        # allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)

        allow(Thread).to receive(:current).and_return(Thread.current)
        allow(GenerateToken).to receive(:new).and_return(double(call: 'mock-token'))
        allow(User::TokenParser).to receive(:parse).and_return(double(tenant_id: connected_account.tenant_id))
      end

      it 'calls ChatbotService with correct parameters including admin token' do
        expect(ChatbotService).to receive(:new).with(
          message_content,
          [{ id: 123, entityType: 'lead' }],
          connected_account,
          an_instance_of(String), # admin_token
          conversation.id
        )

        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])
      end

      it 'does not updates conversation with chatbot_conversation_id' do
        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])

        expect(conversation.reload.chatbot_conversation_id).to be_nil
      end

      it 'does not sends chatbot response message using session message service' do
        expected_params = {
          id: connected_account.id,
          conversation_id: conversation.id,
          message_type: 'text',
          message_body: 'Hello! How can I help you?',
          entity_id: 123,
          entity_type: 'lead'
        }

        expect(Message::SessionMessage).not_to receive(:new).with(expected_params)

        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])
      end

      it 'does not sends chatbot first question using session message service' do
        expected_params = {
          id: connected_account.id,
          conversation_id: conversation.id,
          message_type: 'text',
          message_body: 'What is your name?',
          entity_id: 123,
          entity_type: 'lead'
        }

        expect(Message::SessionMessage).not_to receive(:new).with(expected_params)

        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])
      end

      it 'logs successful initiation' do
        allow(Rails.logger).to receive(:info)
        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])
      end
    end

    context 'when conversation does not exist' do
      it 'logs error and returns early' do
        expect(Rails.logger).to receive(:error).with(/Conversation not found/)
        described_class.perform_now(99999, message_content, [])
      end
    end

    context 'when connected account does not exist' do
      let(:conversation_without_account) { create(:conversation, connected_account_id: 99999) }

      it 'logs error and returns early' do
        expect(Rails.logger).to receive(:error).with(/Connected account not found/)
        described_class.perform_now(conversation_without_account.id, message_content, [])
      end
    end

    context 'when chatbot should not be initiated' do
      let(:conversation_with_disabled_chatbot) do
        create(:conversation,
               connected_account_id: create(:connected_account, is_chatbot_configured: false, created_by: user, updated_by: user).id)
      end

      it 'logs skip message and returns early' do
        allow(Rails.logger).to receive(:info)
        described_class.perform_now(conversation_with_disabled_chatbot.id, message_content, [])
      end
    end

    context 'when no entity details found' do
      it 'logs error and returns early' do
        expect(Rails.logger).to receive(:error).with(/No entity details found/)
        described_class.perform_now(conversation.id, message_content, [])
      end
    end

    context 'when chatbot service fails' do
      let!(:conversation_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
      let(:failed_response) { { success: false, error: 'Service unavailable' } }

      before do
        allow(ChatbotService).to receive(:new).and_return(double(initiate_conversation: failed_response))
      end

      it 'logs error' do
        expect(Rails.logger).to receive(:error).with(/Failed to initiate chatbot conversation/)
        described_class.perform_now(conversation.id, message_content, [{ id: 123, entityType: 'lead' }])
      end
    end
  end

  describe '#should_initiate_chatbot?' do
    subject { described_class.new.send(:should_initiate_chatbot?, connected_account, conversation) }

    context 'when all conditions are met' do
      it { is_expected.to be true }
    end

    context 'when chatbot is not configured' do
      let(:connected_account) { create(:connected_account, is_chatbot_configured: false, created_by: user, updated_by: user) }
      it { is_expected.to be false }
    end

    context 'when chatbot conversation is completed' do
      let(:conversation) { create(:conversation, chatbot_conversation_completed: true) }
      it { is_expected.to be false }
    end

    context 'when chatbot conversation ID is already set' do
      let(:conversation) { create(:conversation, chatbot_conversation_id: 'existing-id') }
      it { is_expected.to be false }
    end
  end

  describe '#get_entity_details_for_conversation' do
    let!(:lead_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'lead', entity_id: 123)) }
    let!(:contact_lookup) { create(:conversation_look_up, conversation: conversation, look_up: create(:look_up, entity_type: 'contact', entity_id: 456)) }

    subject { described_class.new.send(:get_entity_details_for_conversation, conversation) }

    it 'returns entity details array' do
      expect(subject).to match_array([
        { id: 123, entityType: 'lead' },
        { id: 456, entityType: 'contact' }
      ])
    end
  end

  describe '#send_chatbot_response_message' do
    let(:entity_details) { [{ id: 123, entityType: 'lead' }] }
    let(:message_content) { 'Hello! How can I help you?' }
    let(:job_instance) { described_class.new }
    let(:session_message_double) { double('SessionMessage') }

    before do
      allow(job_instance).to receive(:setup_thread_context)
      allow(session_message_double).to receive(:send_text)
    end

    it 'calls SessionMessage with correct parameters including entity details' do
      expected_params = {
        id: connected_account.id,
        conversation_id: conversation.id,
        message_type: 'text',
        message_body: message_content,
        entity_id: 123,
        entity_type: 'lead'
      }

      expect(Message::SessionMessage).to receive(:new).with(expected_params).and_return(session_message_double)
      expect(session_message_double).to receive(:send_text)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, message_content, entity_details)
    end

    it 'calls SessionMessage without entity details when none provided' do
      expected_params = {
        id: connected_account.id,
        conversation_id: conversation.id,
        message_type: 'text',
        message_body: message_content
      }

      expect(Message::SessionMessage).to receive(:new).with(expected_params).and_return(session_message_double)
      expect(session_message_double).to receive(:send_text)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, message_content, [])
    end

    it 'does not call setup_thread_context (called earlier in perform)' do
      allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)
      expect(job_instance).not_to receive(:setup_thread_context)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, message_content, entity_details)
    end

    it 'logs successful message sending' do
      allow(Message::SessionMessage).to receive(:new).and_return(session_message_double)
      expect(Rails.logger).to receive(:info).with(/Chatbot response message sent for conversation/)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, message_content, entity_details)
    end

    it 'handles errors gracefully' do
      allow(Message::SessionMessage).to receive(:new).and_raise(StandardError.new('Test error'))

      expect(Rails.logger).to receive(:error).with(/Error sending chatbot response message/)

      job_instance.send(:send_chatbot_response_message, conversation, connected_account, message_content, entity_details)
    end
  end
end
