# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchWhatsappTemplatesFromMetaJob, type: :job do
  let(:connected_account) { create(:connected_account) }
  let(:user) { create(:user) }
  let(:job) { described_class.new }
  let(:entity_type) { ['lead'] }

  describe '#perform' do
    context 'when connected_account or user is missing' do
      it 'logs error and returns' do
        expect(Rails.logger).to receive(:error).with(/FetchWhatsappTemplatesFromMetaJob \| Invalid connected_account_id/)
        job.perform(12345, user.tenant_id, user.id, entity_type)
      end
    end

    context 'when connected_account and user are present' do
      let(:facebook_response) do
        {
          'data' => [
            { 'id' => '1', 'status' => 'APPROVED', 'category' => 'MARKETING' },
            { 'id' => '2', 'status' => 'REJECTED', 'category' => 'MARKETING' },
            { 'id' => '3', 'status' => 'APPROVED', 'category' => 'UTILITY' },
            { 'id' => '4', 'status' => 'APPROVED', 'category' => 'AUTHENTICATION' }
          ],
          'paging' => { 'next' => nil }
        }.to_json
      end

      before do
        allow(FetchWhatsappTemplateDetailsFromMetaJob).to receive(:perform_later).and_return(true)

        stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.waba_id}/message_templates?fields=id,name,status,category&limit=100")
          .with(headers: { 'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" })
          .to_return(body: facebook_response.to_json)
      end
      it 'fetches approved templates and logs info' do

        expect(Rails.logger).to receive(:info).with(/Fetching templates from Meta/)
        expect(Rails.logger).to receive(:info).with(/Facebook response for request url/)
        expect(Rails.logger).to receive(:info).with(/FetchWhatsappTemplatesFromMetaJob | Scheduling job to fetch details/)
        expect(Rails.logger).to receive(:info).with(/FetchWhatsappTemplatesFromMetaJob | Skipping template/)
        expect(Rails.logger).to receive(:info).with(/FetchWhatsappTemplatesFromMetaJob \| Total templates: 4 and approved templates: 2 and skipped templates: 2/)
        expect(Rails.logger).to receive(:info).with(/FetchWhatsappTemplatesFromMetaJob \| Completed fetching templates /)

        job.perform(connected_account.id, user.tenant_id, user.id, entity_type)
      end

      it 'enqueues FetchWhatsappTemplateDetailsFromMetaJob for approved templates' do
        expect(FetchWhatsappTemplateDetailsFromMetaJob).to receive(:perform_later).with('1', connected_account.id, user.tenant_id, user.id, entity_type)
        expect(FetchWhatsappTemplateDetailsFromMetaJob).to receive(:perform_later).with('3', connected_account.id, user.tenant_id, user.id, entity_type)

        job.perform(connected_account.id, user.tenant_id, user.id, entity_type)
      end

      it 'increments sync_whatsapp_template_active_job_count for each new template scheduled' do
        initial_count = connected_account.sync_whatsapp_template_active_job_count

        job.perform(connected_account.id, user.tenant_id, user.id, entity_type)

        expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count + 2)
      end

      context 'when template already exists' do
        before do
          create(:whatsapp_template, whatsapp_template_id: '1', tenant_id: user.tenant_id)
        end

        it 'does not enqueue FetchWhatsappTemplateDetailsFromMetaJob for existing template' do
          expect(FetchWhatsappTemplateDetailsFromMetaJob).not_to receive(:perform_later).with('1', connected_account.id, user.tenant_id, user.id, entity_type)
          expect(FetchWhatsappTemplateDetailsFromMetaJob).to receive(:perform_later).with('3', connected_account.id, user.tenant_id, user.id, entity_type)

          job.perform(connected_account.id, user.tenant_id, user.id, entity_type)
        end

        it 'increments sync_whatsapp_template_active_job_count only for new templates' do
          initial_count = connected_account.sync_whatsapp_template_active_job_count

          job.perform(connected_account.id, user.tenant_id, user.id, entity_type)

          expect(connected_account.reload.sync_whatsapp_template_active_job_count).to eq(initial_count + 1)
        end
      end

      it 'returns empty array on error and logs error' do
        allow(Facebook::Request).to receive(:process).and_raise(StandardError.new('error'))

        expect(Rails.logger).to receive(:error).with(/FetchWhatsappTemplatesFromMetaJob \| Error While Fetching Templates from Meta/)

        result = job.send(:fetch_templates_ids, connected_account, user.tenant_id, user.id, entity_type)
        expect(result).to eq([])
      end
    end
  end
end
