{"metadata": {"tenantId": 75, "userId": 11094, "entityType": "DEAL", "workflowId": "WF_5105", "executedWorkflows": ["WF_5105"], "entityAction": "UPDATED", "executeWorkflow": true, "entityId": 329339, "workflowName": "Deal whatsapp", "eventId": 83658}, "entity": {"entityType": "DEAL", "id": 329339, "name": "Neuro link", "ownedBy": {"id": 4010, "name": "<PERSON><PERSON><PERSON><PERSON> kalkar"}, "estimatedValue": {"currencyId": 400, "value": 7985.0}, "actualValue": {"currencyId": 400, "value": 7985.0}, "estimatedClosureOn": "2025-07-22T18:30:00.000Z", "associatedContacts": [{"id": 381652, "name": "<PERSON><PERSON>"}, {"id": 381653, "name": "<PERSON><PERSON><PERSON>"}, {"id": 381654, "name": "No phone contact"}], "pipeline": {"id": 7654, "name": "Deal pipeline import", "stage": {"id": 47775, "name": "Open"}, "stages": null, "pipelineStageReason": null, "pipelineStageReasons": null}, "company": {"id": 218771, "name": "New Cmpany kylas crm"}, "forecastingType": "OPEN", "createdBy": {"id": 11094, "name": "<PERSON><PERSON><PERSON><PERSON> kalkar"}, "updatedBy": {"id": 11094, "name": "<PERSON><PERSON><PERSON><PERSON> kalkar"}, "createdAt": 1753251729517, "updatedAt": 1753272388336, "customFieldValues": {"cfKaranPickList": {"id": 17575, "name": "1"}, "karanUrl": "https://www.google.com", "cfDateInterakt": "2025-07-22T18:30:00.000Z"}, "products": [], "createdViaId": "11094", "createdViaName": "User", "createdViaType": "Web", "updatedViaId": "11094", "updatedViaName": "User", "updatedViaType": "Web", "latestActivityCreatedAt": "2025-07-23T12:06:28.336Z", "isNew": false, "idNameStore": {"updatedBy": {"11094": "<PERSON><PERSON><PERSON><PERSON> kalkar"}, "ownedBy": {"11094": "<PERSON><PERSON><PERSON><PERSON> kalkar"}, "createdBy": {"11094": "<PERSON><PERSON><PERSON><PERSON> kalkar"}}}, "messageDetail": {"templateId": 3232, "to": [{"name": "Record Primary Phone number", "type": "RECORD_PRIMARY_PHONE_NUMBER"}]}}