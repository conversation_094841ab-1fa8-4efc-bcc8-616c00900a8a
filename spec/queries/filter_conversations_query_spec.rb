require 'rails_helper'

RSpec.describe FilterConversationsQuery do
  let(:user) { create(:user)}
  let(:team) { create(:team, tenant_id: user.tenant_id, user_ids: [user.id])}
  let(:connected_account_id_1){create(:connected_account, tenant_id: user.tenant_id)}
  let(:connected_account_id_2){create(:connected_account, tenant_id: user.tenant_id)}
  let(:connected_account_id_3){create(:connected_account, tenant_id: user.tenant_id)}
  let(:another_user) { create(:user, tenant_id: user.tenant_id)}
  
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }

  let(:conversation_user_owner_1) {create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id, last_activity_at: 10.hours.ago)}
  let(:conversation_user_owner_2) {create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_2.id, last_activity_at: 20.hours.ago)}
  let(:conversation_user_owner_3) {create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_3.id, last_activity_at: nil)}
  let!(:other_tenant_conversation) {create(:conversation, phone_number: '+************')}
  let(:conversation_another_user_owner) {create(:conversation, owner_id: another_user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id)}
  let(:conversation_shared_all_via_user) {create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: user.id, to_type: 'USER', share_all_records: true)}
  let(:conversation_shared_all_via_team) {create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: team.id, to_type: 'TEAM', share_all_records: true)}

  before do
    Thread.current[:auth] = valid_auth_data
    Thread.current[:token] = valid_auth_token
    Thread.current[:user] = user
  end

  describe '#call' do
    context 'query' do
      let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 10, jsonRule: nil } ).permit! }

      context 'when user has read all' do

        before do
          conversation_user_owner_1
          conversation_user_owner_2
          conversation_user_owner_3
          conversation_another_user_owner
        end

        it 'returns all conversations for the tenant' do
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
        end
      end

      context 'when user has only read' do
        let(:valid_auth_token) { build(:auth_token, :with_meesage_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
        let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }

        before do
          Thread.current[:auth] = valid_auth_data
          Thread.current[:token] = valid_auth_token
        end

        context 'where user is owner of conversation' do
          before do
            conversation_user_owner_1
            conversation_user_owner_2
            conversation_user_owner_3
            conversation_another_user_owner
          end

          it 'returns conversations' do
            conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
            expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id])
          end
        end

        context 'when user is owner of entity' do
          let!(:lookup_with_user_owner_of_entity){create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)}
          let!(:conversation_look_up_1){create(:conversation_look_up, conversation_id: conversation_another_user_owner.id, look_up_id: lookup_with_user_owner_of_entity.id, tenant_id: user.tenant_id)}
          let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}

          before do
            conversation_user_owner_1
            conversation_user_owner_2
            conversation_user_owner_3
            conversation_another_user_owner
          end

          it 'returns conversations' do
            conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
            expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
          end
        end
        
        context 'shared parent entities' do
          context 'shared via all records' do
            context 'when entity is shared with user' do
              let!(:look_up){create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: another_user.id)}
              let!(:conversation_look_up_1){create(:conversation_look_up, conversation_id: conversation_another_user_owner.id, look_up_id: look_up.id, tenant_id: user.tenant_id)}
              let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}
              let!(:share_rule){create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: user.id, to_type: 'USER', share_all_records: true)}
              
              before do
                conversation_user_owner_1
                conversation_another_user_owner
              end

              it 'returns conversations' do
                conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
                expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
              end
            end

            context 'when entity is shared with user\'s team' do
              let(:team){create(:team, tenant_id: user.tenant_id, user_ids: [user.id])}
              let!(:look_up){create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: another_user.id)}
              let!(:conversation_look_up_1){create(:conversation_look_up, conversation_id: conversation_another_user_owner.id, look_up_id: look_up.id, tenant_id: user.tenant_id)}
              let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}
              let!(:share_rule){create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: team.id, to_type: 'TEAM', share_all_records: true)}
              
              before do
                conversation_user_owner_1
                conversation_another_user_owner
              end

              it 'returns conversations' do
                conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
                expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
              end
            end
          end

          context 'shared individual records' do
            context 'when entity is shared with user' do
              let!(:look_up){create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: another_user.id)}
              let!(:conversation_look_up_1){create(:conversation_look_up, conversation_id: conversation_another_user_owner.id, look_up_id: look_up.id, tenant_id: user.tenant_id)}
              let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}
              let!(:share_rule){create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: user.id, to_type: 'USER', entity_id: look_up.entity_id, entity_type: 'LEAD')}
              
              before do
                conversation_user_owner_1
                conversation_another_user_owner
              end

              it 'returns conversations' do
                conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
                expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
              end
            end

            context 'when entity is shared with user\'s team' do
              let(:team){create(:team, tenant_id: user.tenant_id, user_ids: [user.id])}
              let!(:look_up){create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: another_user.id)}
              let!(:conversation_look_up_1){create(:conversation_look_up, conversation_id: conversation_another_user_owner.id, look_up_id: look_up.id, tenant_id: user.tenant_id)}
              let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}
              let!(:share_rule){create(:share_rule, tenant_id: user.tenant_id, from_id: another_user.id, to_id: team.id, to_type: 'TEAM', entity_id: look_up.entity_id, entity_type: 'LEAD')}
              
              before do
                conversation_user_owner_1
                conversation_another_user_owner
              end

              it 'returns conversations' do
                conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
                expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id])
              end
            end
          end
        end
      end
    end

    context "pagination params in filter_params" do
      let!(:conversation_4){create(:conversation, tenant_id: user.tenant_id, phone_number: '+*************', connected_account_id: connected_account_id_1.id)}

      before do
        conversation_user_owner_1
        conversation_user_owner_2
        conversation_user_owner_3
        conversation_another_user_owner
      end

      context "when pagination is applied" do
        let(:size)          { 2 }
        let(:filter_params) { ActionController::Parameters.new({ page: 2, size: size, jsonRule: nil } ).permit! }

        it "returns correct number of conversations" do
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.size).to eq(1)
        end

        it "returns correct conversations" do
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to match_array([conversation_another_user_owner.id])
        end
      end

      context "when pagination is not applied" do
        let(:filter_params) { ActionController::Parameters.new({ jsonRule: nil } ).permit! }

        it "returns correct number of records as per default pagination" do
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.size).to eq(3)
        end

        it "returns correct conversations" do
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to match_array([conversation_user_owner_1.id, conversation_another_user_owner.id, conversation_4.id])
        end
      end
    end

    context 'when sufficient permissions are available' do
      let(:permission) { :sms_with_read_permission }

      before do
        @conversation = create(:conversation, owner_id: user.id, tenant_id: user.tenant_id)
        @soft_deleted_conversation = create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************')
        @soft_deleted_conversation.soft_delete!
      end

      it 'returns only non-deleted conversations by default' do
        result = FilterConversationsQuery.new(valid_auth_data, {}).call

        expect(result.count).to eq(1)
        expect(result.first.id).to eq(@conversation.id)
      end
    end

    context 'when filtering by isRead' do
      let!(:conversation_read) { create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+919999999991', is_read: true) }
      let!(:conversation_unread) { create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+919999999992', is_read: false) }

      context 'when jsonRule is present' do
        it 'returns read conversations when value is true' do
          json_rule = {
            rules: [
              { id: 'isRead', field: 'isRead', type: 'boolean', value: true, operator: 'equal' }
            ],
            condition: 'AND'
          }
          filter_params = ActionController::Parameters.new({ jsonRule: json_rule }).permit!
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to include(conversation_read.id)
          expect(conversations.map(&:id)).not_to include(conversation_unread.id)
        end

        it 'returns unread conversations when value is false' do
          json_rule = {
            rules: [
              { id: 'isRead', field: 'isRead', type: 'boolean', value: false, operator: 'equal' }
            ],
            condition: 'AND'
          }
          filter_params = ActionController::Parameters.new({ jsonRule: json_rule }).permit!
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to include(conversation_unread.id)
          expect(conversations.map(&:id)).not_to include(conversation_read.id)
        end

        it 'returns read conversations when value is false and operator is not_equal' do
          json_rule = {
            rules: [
              { id: 'isRead', field: 'isRead', type: 'boolean', value: false, operator: 'not_equal' }
            ],
            condition: 'AND'
          }
          filter_params = ActionController::Parameters.new({ jsonRule: json_rule }).permit!
          conversations = FilterConversationsQuery.new(valid_auth_data, filter_params).call
          expect(conversations.map(&:id)).to include(conversation_read.id)
          expect(conversations.map(&:id)).not_to include(conversation_unread.id)
        end
      end

      context 'when jsonRule is not present' do
        it 'returns all conversations' do
          conversations = FilterConversationsQuery.new(valid_auth_data, {}).call
          expect(conversations.map(&:id)).to include(conversation_read.id)
          expect(conversations.map(&:id)).to include(conversation_unread.id)
        end
      end
    end
  end
end
